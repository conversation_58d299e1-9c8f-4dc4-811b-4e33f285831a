const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const FilterWarningsPlugin = require('webpack-filter-warnings-plugin');
const tsNameof = require('ts-nameof');
const CopyWebpackPlugin = require('copy-webpack-plugin');

//Config
const developmentPlugins = require('./config/developmentPlugins');
const productionPlugins = require('./config/productionPlugins');

const smp = new SpeedMeasurePlugin();
const debugWebpackMode = process.argv.includes('--debug');

process.traceDeprecation = true;

const goodDataProxyConfig = () => {
    return [
        {
            context: (pathname) => /^\/(api\/|gdc\/|account\.html|truste\.html|account\/)/.test(pathname),
            changeOrigin: true,
            cookieDomainRewrite: '127.0.0.1',
            secure: false,
            target: 'http://localhost:5050',
            headers: {
                host: 'http://localhost:5050',
                origin: null,
            },
            onProxyReq(proxyReq) {
                proxyReq.removeHeader('origin');
                proxyReq.setHeader('accept-encoding', 'identity');
                if (process.env.TIGER_API_TOKEN) {
                    proxyReq.setHeader('Authorization', `Bearer ${process.env.TIGER_API_TOKEN}`);
                }
            },
        },
    ];
};

module.exports = function (opt) {
    const BUILD = !!opt.BUILD;
    const MODE = BUILD ? 'production' : 'development';
    const isDevModeOn = MODE === 'development';

    const cssPlugin = new MiniCssExtractPlugin({
        filename: '[name].css',
        chunkFilename: '[id].css',
    });

    const plugins = [
        new HtmlWebpackPlugin({
            filename: 'index.html',
            pkg: require('./package.json'),
            template: './app/index.html',
            hash: true,
            favicon: './app/assets/img/favicon.ico',
            inject: 'body',
            [BUILD ? 'minify' : '']: {
                caseSensitive: true,
                collapseWhitespace: true,
                minifyCSS: true,
            },
            chunks: ['portal', 'vendor'],
        }),
        new HtmlWebpackPlugin({
            filename: 'renew-callback.html',
            pkg: require('./package.json'),
            template: './app/renew-callback.html',
            hash: true,
            favicon: './app/assets/img/favicon.ico',
            inject: 'body',
            [BUILD ? 'minify' : '']: {
                caseSensitive: true,
                collapseWhitespace: true,
                minifyCSS: true,
            },
            chunks: [],
        }),
        new webpack.IgnorePlugin(/\.\/locale$/),
        new MonacoWebpackPlugin({
            languages: ['json', 'javascript'],
        }),
        // Temporarily disabled for testing webpack alias
        // new ForkTsCheckerWebpackPlugin({
        //     tslint: './tslint.json',
        //     tsconfig: './tsconfig.json',
        //     watch: './app/**/*.ts',
        //     async: false,
        // }),
        new FilterWarningsPlugin({
            exclude: /Critical dependency: the request of a dependency is an expression/,
        }),
        cssPlugin,
        new webpack.DefinePlugin({
            // Build-time environment variables
            ENVIRONMENT_VARIABLES_SERIALIZED: JSON.stringify(process.env),
        }),
        // copy custom static assets
        new CopyWebpackPlugin([
            {
                from: path.resolve(__dirname, './web.config'),
                to: path.resolve(__dirname, './dist'),
            },
            {
                from: path.resolve(__dirname, './config/settings.json'),
                to: path.resolve(__dirname, './dist'),
            },
            {
                from: path.resolve(__dirname, './config/mfesSettings.json'),
                to: path.resolve(__dirname, './dist'),
            },
        ]),
    ];

    const proxy = goodDataProxyConfig();

    const config = {
        cache: true,
        mode: MODE,
        context: __dirname,
        entry: {
            'portal': ['index'],
        },
        output: {
            path: path.resolve(__dirname, 'dist'),
            publicPath: '/',
            filename: '[name].js',
            chunkFilename: '[name].[hash].js',
            sourceMapFilename: '[name].js.map',
        },
        optimization: {
            namedChunks: true,
            splitChunks: {
                automaticNameDelimiter: '-',
                cacheGroups: {
                    vendor: {
                        chunks: 'async',
                        priority: -10,
                    },
                },
            },
            minimize: BUILD ? true : false,
            minimizer: [
                new UglifyJsPlugin({
                    cache: true,
                    parallel: true,
                    uglifyOptions: {
                        compress: {
                            warnings: false,
                        },
                        mangle: false,
                    },
                    sourceMap: true,
                }),
                new OptimizeCSSAssetsPlugin({
                    cssProcessorOptions: { reduceIdents: false },
                }),
            ],
        },
        module: {
            rules: [
                {
                    test: /\.js$/,
                    loader: 'babel-loader',
                    exclude: /node_modules(\/?!(react|react-dom)\/)*/,
                },
                {
                    test: /\.js$/,
                    enforce: 'pre',
                    exclude: /(node_modules|bower_components)/,
                    include: /src/,
                    loader: 'eslint-loader',
                },
                {
                    test: /\.ts(x?)$/,
                    exclude: /node_modules/,
                    use: [{
                        loader: 'ts-loader',
                        options: {
                            transpileOnly: true,
                            getCustomTransformers: () => ({ before: [tsNameof] }),
                        },
                    }],
                },
                {
                    test: /\.(woff|woff2|ttf|otf|eot)(\?]?.*)?$/,
                    exclude: /node_modules/,
                    use: [
                        {
                            loader: 'url-loader',
                            options: {
                                limit: 1024,
                                name: 'fonts/[name].[ext]?[hash]',
                            },
                        },
                    ],
                },
                {
                    test: /.*(?<!-icon)\.svg$/,
                    exclude: /node_modules/,
                    use: [
                        {
                            loader: 'url-loader',
                            options: {
                                limit: 1024,
                                name: 'fonts/[name].[ext]?[hash]',
                            },
                        },
                    ],
                },
                {
                    test: /.*-icon\.svg$/i,
                    use: [
                        {
                            loader: 'raw-loader',
                        },
                    ],
                    exclude: /node_modules/,
                },
                // {
                //     test: /\.ico$/i,
                //     include: [path.join(__dirname, './app/assets')],
                //     use: [
                //         {
                //             loader: 'file-loader',
                //         },
                //     ],
                //     exclude: /node_modules/,
                // },
                {
                    test: /\.ico$/i,
                    include: [path.join(__dirname, './app/assets')],
                    use: [
                        {
                            loader: 'file-loader',
                        },
                    ],
                    exclude: /node_modules/,
                },
                {
                    test: /\.(ttf|eot|svg|png|jpg|gif|ico|woff|woff2)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
                    loader: 'file-loader',
                    include: {
                        test: path.resolve(__dirname, 'node_modules/blip-components'),
                    },
                },
                {
                    test: /\.(jpe?g|gif|cur)$/i,
                    use: [
                        {
                            loader: 'url-loader',
                            options: {
                                limit: 8192,
                                name: 'img/[name].[ext]?[hash]',
                            },
                        },
                    ],
                    exclude: /node_modules/,
                },
                {
                    test: /\.png$/i,
                    use: [
                        {
                            loader: 'url-loader',
                            options: {
                                limit: 8192,
                                mimetype: 'image/png',
                                name: 'img/[name].[ext]?[hash]',
                            },
                        },
                    ],
                    exclude: /node_modules/,
                },
                {
                    test: /\.css$/,
                    use: [
                        isDevModeOn ? 'style-loader' : MiniCssExtractPlugin.loader,
                        'css-loader',
                    ],
                },
                {
                    test: /^((?!\.module).)*scss$/,
                    use: [
                        isDevModeOn ? 'style-loader' : MiniCssExtractPlugin.loader,
                        'css-loader',
                        'sass-loader',
                    ],
                    exclude: {
                        test: path.resolve(__dirname, 'node_modules'),
                        exclude: path.resolve(__dirname, 'node_modules/blip-toolkit'),
                    },
                },
                {
                    test: /\.module.scss$/,
                    use: [
                        isDevModeOn ? 'style-loader' : MiniCssExtractPlugin.loader,
                        {
                            loader: 'css-loader',
                            options: {
                                modules: true,
                                localsConvention: 'camelCase',
                            },
                        },
                        'sass-loader',
                    ],
                    exclude: /node_modules/,
                },
                {
                    test: /\.html$/,
                    use: [
                        {
                            loader: 'html-loader',
                            options: {
                                exportAsEs6Default: true,
                                root: path.resolve(__dirname, 'app'),
                            },
                        },
                    ],
                    exclude: /node_modules/,
                },
            ],
        },
        resolve: {
            alias: {
                '@details$': path.resolve(__dirname, 'app/modules/application/detail'),
                '@detailssubmodule$': path.resolve(__dirname, 'app/modules/application/detail/portal-submodule-builder/'),
                '@stencil/react-output-target/runtime': path.resolve(__dirname, 'node_modules/@stencil/react-output-target/dist/runtime.js'),
            },
            extensions: [
                '.webpack.js',
                '.web.js',
                '.js',
                '.ts',
                '.tsx',
            ],
            modules: [
                path.resolve(__dirname, 'app'),
                path.resolve(__dirname, 'app/modules'),
                'node_modules',
            ],
            symlinks: false,
        },
        plugins: BUILD
            ? plugins.concat(productionPlugins) // PRODUCTION ==> default + production plugins
            : plugins.concat(developmentPlugins), // DEVELOPMENT ==> default + development plugins
        // DEVELOPMENT ==> devServer
        devtool: 'source-map',
        devServer: !BUILD
            ? {
                disableHostCheck: true,
                contentBase: path.join(__dirname, 'dist'),
                compress: true,
                overlay: true,
                inline: true,
                quiet: true, // necessary for FriendlyErrorsPlugin
                historyApiFallback: {
                    disableDotRule: true,
                },
                proxy,
            }
            : {},
    };

    if (BUILD) return config;
    return debugWebpackMode ? smp.wrap(config) : config;
};
