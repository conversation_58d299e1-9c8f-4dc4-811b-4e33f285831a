{"name": "blip", "author": "take.net", "private": true, "license": "ISC", "scripts": {"compodoc": "./node_modules/.bin/compodoc -p ./tsconfig.json", "postinstall:webdriver": "./node_modules/.bin/webdriver-manager update", "lint": "./node_modules/.bin/eslint . --ext .js || true", "start": "webpack-dev-server --config webpack.dev.js -d --port 8080 --hot --open --bail", "start:https": "./node_modules/.bin/webpack-dev-server --config webpack.dev.js -d --port 8080 --hot --open --bail --https --key cert.key --cert cert.crt --cacert ca.crt", "start:debug": "./node_modules/.bin/webpack-dev-server --config webpack.dev.js -d --port 8080 --hot --open --debug", "build": "./node_modules/.bin/webpack --display=errors-only --config webpack.build.js --bail", "build:hmg": "./node_modules/.bin/webpack --display-modules --display-chunks --config webpack.build.js --bail", "bundle": "./node_modules/.bin/webpack --config webpack.dev.js --bail -d --profile", "tslint": "./node_modules/.bin/tslint ./app/**/*.ts", "start-submodules:ps1": "powershell -ExecutionPolicy Bypass -File ../scripts/submodules/start-submodules.ps1", "start-submodules:sh": "sh ../scripts/submodules/start-submodules.sh", "update-submodules:ps1": "powershell -ExecutionPolicy Bypass -File ../scripts/submodules/update-submodules.ps1", "update-submodules:sh": "sh ../scripts/submodules/update-submodules.sh"}, "dependencies": {"@sentry/browser": "^7.118.0", "@stencil/react-output-target": "^1.1.1", "angucomplete-alt": "^3.0.0", "angular": "^1.8.2", "angular-animate": "^1.8.2", "angular-base64": "^2.0.5", "angular-chart.js": "^1.1.1", "angular-clipboard": "^1.5.0", "angular-cookies": "^1.8.2", "angular-datetime-input": "^3.2.2", "angular-drag-and-drop-lists": "^1.4.0", "angular-file-saver": "^1.1.3", "angular-input-masks": "^4.1.0", "angular-legacy-sortablejs-maintained": "^0.6.1", "angular-messages": "^1.8.2", "angular-modal-service": "0.15.6", "angular-resource": "^1.8.2", "angular-sanitize": "^1.8.2", "angular-scalyr": "^1.0.4", "angular-svg-round-progressbar": "^0.4.8", "angular-tooltips": "^1.2.2", "angular-touch": "^1.8.2", "angular-translate": "^2.15.1", "angular-translate-storage-cookie": "^2.15.1", "angular-ui-router": "^0.4.2", "angular-wizard": "^1.1.1", "angularjs-datepicker": "^2.1.19", "angularjs-slider": "^6.4.0", "babel-polyfill": "^6.16.0", "blip-cards": "3.50.2", "blip-chat-widget": "^1.11.0", "blip-components": "^9.72.0", "blip-ds": "file:../../blip-ds/blip-ds-0.0.0-development.tgz", "blip-ds-react": "file:../../blip-ds/blip-ds-react/blip-ds-react-0.0.1.tgz", "blip-sdk": "^10.0.0", "blip-tokens": "^1.37.0", "blip-toolkit": "5.22.0", "color": "^4.2.3", "copy-webpack-plugin": "^5.1.2", "crypto": "^1.0.1", "exception-formatter": "^2.1.2", "fecha": "^2.3.3", "iframe-message-proxy": "^1.1.0", "immer": "^6.0.2", "jsplumb": "2.5.12", "jwt-decode": "^3.1.2", "lime-transport-websocket": "^2.0.3", "moment": "2.18.1", "monaco-editor": "0.13.1", "monaco-editor-webpack-plugin": "1.4.0", "monitoring-error-handler": "3.1.3", "ng-infinite-scroll": "^1.2.1", "ng-onload": "^0.8.0", "ng-tags-input": "^3.1.2", "ng-toast": "^2.0.0", "ngVue": "^0.4.2", "odata-query": "^5.4.0", "oidc-client": "^1.8.2", "prop-types": "15.8.1", "react": "18.3.1", "react-dom": "18.3.1", "react2angular": "^4.0.6", "resize-observer": "^1.0.0", "rxjs": "^6.5.4", "semaphore-async-await": "^1.5.1", "sortablejs": "1.7.0", "tslib": "2.3.1", "typescript": "^4.2.3", "uuid": "^2.0.2", "webdriver-manager": "^7.0.1"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-object-rest-spread": "^7.27.7", "@babel/preset-env": "^7.27.2", "@types/angular": "1.6.45", "@types/angular-cookies": "^1.4.5", "@types/angular-translate": "^2.16.0", "@types/angular-ui-router": "^1.1.40", "@types/applicationinsights-js": "^1.0.7", "@types/lime-js": "0.0.29", "@types/node": "^7.0.18", "@types/ts-nameof": "^4.0.0", "angular-translate-loader-partial": "^2.18.1", "babel-eslint": "^8.2.3", "babel-loader": "^8.4.1", "babel-plugin-angularjs-annotate": "^0.10.0", "babel-plugin-dynamic-import-webpack": "^1.0.2", "babel-plugin-syntax-async-functions": "^6.13.0", "babel-plugin-transform-class-properties": "^6.22.0", "babel-plugin-transform-exponentiation-operator": "^6.8.0", "babel-plugin-transform-object-rest-spread": "^6.16.0", "babel-plugin-transform-regenerator": "^6.16.1", "clean-webpack-plugin": "^0.1.19", "css-loader": "^3.2.0", "eslint": "^4.19.1", "eslint-loader": "^2.0.0", "eslint-plugin-babel": "^5.1.0", "exports-loader": "^0.6.4", "feature-toggle-client": "2.0.0", "file-loader": "^1.1.11", "fork-ts-checker-webpack-plugin": "^4.1.0", "friendly-errors-webpack-plugin": "^1.6.1", "google-charts": "^1.1.0", "html-loader": "^0.5.5", "html-webpack-plugin": "4.0.0-beta.11", "json-loader": "^0.5.7", "mini-css-extract-plugin": "^0.4.0", "ng-cache-loader": "0.0.15", "optimize-css-assets-webpack-plugin": "5.0.3", "raw-loader": "^0.5.1", "resolve-url-loader": "^2.3.0", "sass": "^1.49.0", "sass-loader": "^7.1.0", "speed-measure-webpack-plugin": "^1.0.0", "style-loader": "^0.21.0", "ts-loader": "6.2.1", "ts-nameof": "^4.1.0", "tslint": "5.12.0", "typescript-tslint-plugin": "0.2.1", "uglifyjs-webpack-plugin": "^1.2.2", "url-loader": "^2.1.0", "vue": "^2.5.13", "webpack": "4.8.1", "webpack-cli": "2.1.3", "webpack-dev-server": "^3.11.0", "webpack-filter-warnings-plugin": "^1.2.1"}}