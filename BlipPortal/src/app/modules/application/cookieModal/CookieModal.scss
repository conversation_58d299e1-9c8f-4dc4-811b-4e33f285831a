@import '~assets/scss/main';

#invite-answer-modal{
  .modal-dialog.modal-sm {
    width: max-content;
    height: auto;
    min-height: 450px;
    border-radius: 8px;
    overflow: visible;
    display: flex;
    justify-content: center;
    flex-direction: column;
    &::before {
      content: "";
      position: absolute;
      left: -14px;
      top: 0px;
      background: url('~assets/img/migration-modal-mask.svg') no-repeat left bottom;
      min-height: 104%;
      background-size: 100%;
      width: 100%;
      z-index: -1;
      overflow: hidden;
      border-radius: 30px 0px 0px 0px;
    }
  }
}

.input-chips-responsive {
  width: 100%;
  max-width: 100%;
  min-width: 400px;
  box-sizing: border-box;
  max-height: 450px;
  min-height: -webkit-fill-available;
  overflow-y: auto; 
}

bds-input-chips::part(input-container) {
  max-height: unset;
}

@media (max-width: 768px) {
  .input-chips-responsive {
      width: 100%;
  }
}

@media (max-width: 480px) {
  .input-chips-responsive {
      width: 100%;
  }
}
