import { SegmentService } from '../misc/SegmentService';
import { AccountService2, UserBlipAccount } from 'modules/account/AccountService2';
import moment from 'moment';
import './CookieModal.scss';

export class CookieModalController {
    private readonly COOKIE_ACCEPTED = 'portal-cookiesmodal-accepted';
    private readonly MORE_INFORMATION = 'portal-cookiesmodal-more-information-clicked';
    private readonly COOKIE_POLICY = 'portal-cookiesmodal-policy-clicked';
    private readonly COOKIES_HELP_URL = 'http://help.blip.ai/docs/en/security/cookies/';
    private readonly MAILTO_LEGAL = 'mailto:<EMAIL>';
    private readonly TARGET_BLANK = '_blank';
    account: UserBlipAccount;
    isBannerEnabled: boolean;
    constructor(private SegmentService: SegmentService,
         private AccountService2: AccountService2,
         private close) {
            this.onInit();
        }

    async onInit() {
        this.account = await this.AccountService2.me();
    }

    async cookieAccept() {
        await this.AccountService2.setExtras({
            ...this.account.extras,
            cookies: JSON.stringify({ ...this.account.extras?.cookies, portalAcceptedCookiesDate: moment().format() })
        });
        this.SegmentService.createOrganizationTrack(this.COOKIE_ACCEPTED);
        this.close();
    }

    async moreInformation() {
        this.SegmentService.createOrganizationTrack(this.MORE_INFORMATION);
        window.open(this.MAILTO_LEGAL, this.TARGET_BLANK);
    }

    async trackCookiesPolicy() {
        this.SegmentService.createOrganizationTrack(this.COOKIE_POLICY);
        window.open(this.COOKIES_HELP_URL, this.TARGET_BLANK);
    }
}
