import { IRootScopeService, translate } from 'angular';

import { ContextProcessorService, Contexts } from 'modules/core/ContextProcessorService';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { ApplicationService2 } from '../ApplicationService2';
import { ChatbotSettingsService } from '../misc/ChatbotSettingsService';
import { InitializeUserService } from 'modules/core/InitializeUserService';
import { TeamService } from '../detail/attendance/team/TeamService';
import { QueuesService } from '../detail/attendance/queueManagement/services/QueuesService';
import { TenantService } from '../tenant/TenantService';
import { AttendanceHourService } from '../detail/attendance/attendanceHour/AttendanceHourService';
import { AccountService2 } from 'modules/account/AccountService2';
import { MarketplaceTemplatesService } from '../MarketplaceTemplatesService';
import { ApplicationCreationFeatures } from './ApplicationCreationFeatures';
import { MessagingHubFeatures } from 'modules/messaginghub/MessagingHubFeatures';
import { BuilderFeatures as BuilderFeaturesFromSubmodule } from '../detail/portal-submodule-builder/templates/builder/BuilderFeatures';
import { BuilderFeatures } from '../detail/templates/builder/BuilderFeatures';
import CustomerServiceTemplatePT from './templates/customerService/customerServiceTemplate.json';
import CustomerServiceForwardToDeskTemplatePT from './templates/customerService/customerServiceFowardToDeskTemplate.json';
import AiAgentPublishFlowTemplate from './templates/aiAgentPublishFlowTemplate.json';
import { useBuilderSubmodule } from 'app.constants';

const APPLICATION_ERROR_CODE = 101;
const APPLICATION_ERROR_DESCRIPTION = 'Cannot set an account';

export enum CreateApplicationSteps {
    DataValidation = 'data-validation',
    InitialCreation = 'initial-creation',
    BuilderActivation = 'builder-activation',
    AttendanceActivation = 'attendance-activation',
    TemplateApplication = 'template-applying',
}

export enum ApplicationType {
    Master = 'master',
    Builder = 'builder'
}

export class CreateApplicationError extends Error {
    step: CreateApplicationSteps;
    constructor(err: Error, step: CreateApplicationSteps) {
        super(err.message);
        this.stack = err.stack;
        this.name = 'CreateApplicationError';
        this.step = step;
    }
}

export class CreateApplicationService {
    private redirectNoAgentsAvailableEnabled: boolean;
    private redirectOutOfAttendanceHourEnabled: boolean;
    private isForwardToDeskAttendanceBoxEnabled: boolean;
    private getCustomerInactivityDateEnable: Date;
    private enableCustomerInactivityFeatures: boolean = false;
    private isNewPublishFlowAiAgentEnabled: boolean = false;
    private isConfigurationsApplicationSetEnabled: boolean = false;

    constructor(
        private ContextProcessorService: ContextProcessorService,
        private ApplicationService2: ApplicationService2,
        private MessagingHubService: MessagingHubService,
        private ChatbotSettingsService: ChatbotSettingsService,
        private InitializeUserService: InitializeUserService,
        private TeamService: TeamService,
        private QueuesService: QueuesService,
        private TenantService: TenantService,
        private AttendanceHourService: AttendanceHourService,
        private AccountService2: AccountService2,
        private MarketplaceTemplatesService: MarketplaceTemplatesService,
        private $rootScope: IRootScopeService & any,
        private $translate: translate.ITranslateService,
        private AI_AGENT_STORAGE_BASE_URL: string,
        private MEDIA_PROFILE_FOR_AVATARS: string,
    ) {
        this.$onInit();
    }

    async $onInit(): Promise<void> {
        await this.checkFeatures();
    }

    private async checkFeatures() {
        if (!this.$rootScope.featureToggleInitialized) {
            await this.InitializeUserService.initFeatureToggle();
        }

        const builderFeatures = useBuilderSubmodule ? BuilderFeaturesFromSubmodule : BuilderFeatures;

        const [
            isForwardToDeskAttendanceBoxEnabled,
            redirectOutOfAttendanceHourEnabled,
            redirectNoAgentsAvailableEnabled,
            valueOfCustomerInactivityDate,
            isNewPublishFlowAiAgentEnabled,
            isConfigurationsApplicationSetEnabled
        ] = await Promise.all([
            builderFeatures.isForwardToDeskAttendanceBoxEnabled(),
            builderFeatures.isRedirectOutOfAttendanceHourEnabled(),
            builderFeatures.isRedirectNoAgentsAvailableEnabled(),
            ApplicationCreationFeatures.valueOfCustomerInactivityDate(),
            ApplicationCreationFeatures.newPublishFlowAiAgentIsEnabled(),
            builderFeatures.isUseConfigurationsDomainOnMiscOperationsSetEnabled()
        ]);

        this.isForwardToDeskAttendanceBoxEnabled = isForwardToDeskAttendanceBoxEnabled;
        this.redirectOutOfAttendanceHourEnabled = redirectOutOfAttendanceHourEnabled;
        this.redirectNoAgentsAvailableEnabled = redirectNoAgentsAvailableEnabled;
        this.getCustomerInactivityDateEnable = valueOfCustomerInactivityDate;
        this.isNewPublishFlowAiAgentEnabled = isNewPublishFlowAiAgentEnabled;
        this.isConfigurationsApplicationSetEnabled = isConfigurationsApplicationSetEnabled;
    }

    async createApplication(application: any, template: string): Promise<any> {

        try {
            application.marketplace = await this.getMarketplaceTemplateAsync(template);
            application.template = application.marketplace.type;
            application.shortName = application.name.toLowerCase();
            application.messagingHubCommandWithDelegation = await MessagingHubFeatures.GetMessagingHubCommandWithDelegation();
            this.validateApplicationName(application.name);
        } catch (err) {
            throw new CreateApplicationError(err, CreateApplicationSteps.DataValidation);
        }

        const tenantId = this.ContextProcessorService.context === Contexts.Tenants
            ? this.ContextProcessorService.tenant.id
            : undefined;

        try {
            // Upload application image to media storage
            await this.setApplicationImage(application);
        } catch (err) {
            console.error(err);
        }

        try {
            // Create application on Iris
            const createdApplication = await this.ApplicationService2.setApplication(application, tenantId);
            await this.MessagingHubService.resetClient(createdApplication);
            application = { ...application, ...createdApplication };
            if (application.tenantId && this.getCustomerInactivityDateEnable) {
                this.enableInactivityFeaturesInTemplate(application.tenantId);
            }
        } catch (err) {
            // Undo changes on Iris
            const error = JSON.parse(err);
            if (!(error.reason.code === APPLICATION_ERROR_CODE && !(error.reason.code === APPLICATION_ERROR_DESCRIPTION))) {
                await this.ApplicationService2.deleteAndRevokeApplication(application);
            }
            throw new CreateApplicationError(err, CreateApplicationSteps.InitialCreation);
        }

        try {
            // Set application extras
            const applicationAccount = await this.ApplicationService2.getAccount();
            if (applicationAccount) {
                applicationAccount.extras['#marketplace.id'] = application.marketplace.id;
                await this.ApplicationService2.setAccount(applicationAccount);
            }
        } catch (err) {
            console.error(err);
        }

        if (application.template == ApplicationType.Builder) {
            try {
                // Activate builder
                await this.ChatbotSettingsService.setBuilderActive(application, true);
            } catch (err) {
                await this.ApplicationService2.deleteAndRevokeApplication(application);
                throw new CreateApplicationError(err, CreateApplicationSteps.BuilderActivation);
            }

            try {
                // Activate attendance (help desk)
                await this.activateAttendance();
            } catch (err) {
                throw new CreateApplicationError(err, CreateApplicationSteps.AttendanceActivation);
            }
        }

        if (application.marketplace.id != '0') {
            try {
                // Apply template settings (flow, charts, etc.)
                await this.MarketplaceTemplatesService.processTemplate(application);
            } catch (err) {
                throw new CreateApplicationError(err, CreateApplicationSteps.TemplateApplication);
            }
        }
        return application;
    }

    async validateApplicationName(name: string): Promise<void> {
        const letterFirstPosition = /(^[a-zA-Z])/;
        if (!letterFirstPosition.exec(name)) {
            throw new CreateApplicationError(
                new Error('Application name is not valid'),
                CreateApplicationSteps.DataValidation);
        }
    }

    private async getMarketplaceTemplateAsync(template: string): Promise<{ id: string, type: string, template?: any }> {
        switch (template) {
            case 'blip_deskCustomerService':
                return {
                    id: 'blip_deskCustomerService',
                    type: ApplicationType.Builder,
                    template: this.getDeskCustomerServiceTemplate()
                };
            case 'blip_aiAgent_publish':
            case 'aiAgent':
                if (await ApplicationCreationFeatures.aiAgentTemplateHostingIsEnabled()) {
                    return {
                        id: '0',
                        type: template
                    };
                }
                return {
                    id: 'blip_aiAgent_publish',
                    type: ApplicationType.Builder,
                    template: await this.getAiAgentTemplateAsync()
                };
            default:
                return {
                    id: '0',
                    type: template
                };
        }
    }

    private async getAiAgentTemplateAsync(): Promise<any> {
        try {
            const aiAgentContainerBaseUrl = this.AI_AGENT_STORAGE_BASE_URL;
            const response = await fetch(
                `${aiAgentContainerBaseUrl}/aiagentstemplates/aiAgentTemplate_publish.json`,
            );

            if (!response.ok) {
                throw new Error('Failed to fetch AI agent template');
            }

            if (this.isNewPublishFlowAiAgentEnabled) {
                AiAgentPublishFlowTemplate.steps.find(
                    step => step.type == 'PublishFlow',
                ).arguments.flow = await response.json();
                return AiAgentPublishFlowTemplate;
            }

            return await response.json();
        } catch (err) {
            throw err;
        }
    }

    private getDeskCustomerServiceTemplate(): any {
        if (
            this.isForwardToDeskAttendanceBoxEnabled &&
            this.redirectOutOfAttendanceHourEnabled &&
            this.redirectNoAgentsAvailableEnabled
        ) {
            return CustomerServiceForwardToDeskTemplatePT;
        }

        return CustomerServiceTemplatePT;
    }

    private async setApplicationImage(application: any): Promise<void> {
        if (!application.image) {
            return;
        }

        const mediaUri = await this.ApplicationService2.uploadFile(application.image, { profile: this.MEDIA_PROFILE_FOR_AVATARS });
        if (mediaUri) {
            application.imageUri = mediaUri;
        }
    }

    private async activateAttendance(): Promise<void> {
        const configResource = this.enableCustomerInactivityFeatures ? {
            'Lime.IsActive': true,
            'DefaultProvider': 'Lime',
            'DistributionType': 'Redis',
            'InactivityMessage': await this.$translate('createApplication.customerInactivity.InactivityMessage'),
            'InactivityMessageDowntime': 15,
            'InactivityTags': [await this.$translate('createApplication.customerInactivity.inactivityTag')],
            'MaxClientMinutesDowntime': 25,
            'ResetClientInactivityTimerOnAgentInteraction': true,
            'ResetClientInactivityTimerOnFirstAgentInteraction': true,
            'UseInactivityMessage': true,
            'UseInactivityTags': true,
            'CustomerMaxResponseDelay': 1200,
            'CustomerWarningResponseDelay': 600,
            'AutomaticClosedTicketIsAllowed': true,
            'AutomaticClosedTicketIsEnabled': true,
            'AgentMaxResponseDelay': 900
        } : {
            'Lime.IsActive': true,
            'DefaultProvider': 'Lime',
            'DistributionType': 'Redis'
        };

        await this.MessagingHubService.processCommand({
            method: 'set',
            uri: 'lime://<EMAIL>/configuration',
            type: 'application/json',
            resource: configResource,
            to: this.isConfigurationsApplicationSetEnabled ?  '<EMAIL>' : undefined
        });

        const isBillablePlanContract = await this.TenantService.checkIfBillableContract();
        if (!isBillablePlanContract) {
            const account = await await this.AccountService2.me();
            const attendant = {
                teams: ['Default'],
                identity: account.identity,
                isEnabled: true,
            };
            await this.TeamService.set([attendant]);
        }

        const queue = {
            id: '',
            isActive: true,
            name: 'Default',
            numberAttendants: 0,
        };
        await this.QueuesService.save(queue);

        if (this.redirectOutOfAttendanceHourEnabled) {
            const defaultStartTime = '08:00:00';
            const defaultEndTime = '18:00:00';
            const weekDaysKeys = Array.from(Array(5), (_, index) => index + 1);
            const attendanceHourScheduleItems = weekDaysKeys.map(weekDayKey => ({
                startTime: defaultStartTime,
                endTime: defaultEndTime,
                dayOfWeek: weekDayKey
            }));
            const attendanceHourDefault = {
                attendanceHour: {
                    title: await this.$translate('createApplication.attendanceHourDefault.title'),
                    description: await this.$translate('createApplication.attendanceHourDefault.description'),
                    isMain: true
                },
                attendanceHourScheduleItems: attendanceHourScheduleItems
            };
            await this.AttendanceHourService.set(attendanceHourDefault);
        }
    }

    private async enableInactivityFeaturesInTemplate(tenantId) {
        if (tenantId) {
            const tenant = await this.TenantService.get(tenantId);
            this.enableCustomerInactivityFeatures = new Date(tenant.creationDate).toJSON() >= this.getCustomerInactivityDateEnable.toJSON();
        }
    }
}
