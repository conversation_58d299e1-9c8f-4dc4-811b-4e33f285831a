export const customStyle = `
    body {
        background: #292929;
        overflow: hidden;
    }
    .blip-application-loading-container, .blip-application-loading-container ~ #message-input {
        visibility: hidden;
    }
    #blip-chat-welcome-container {
        background-color: #292929;
        border-radius: 0 0 20px 20px;
    }
    #blip-chat-welcome-container > div {
        justify-content: space-around;
    }
    #blip-chat-welcome-container .blip-chat-bot-name {
        color: #daf2f4;
        margin-bottom: 1.5rem;
    }
    #blip-chat-welcome-container .blip-chat-bot-description, #blip-chat-welcome-container .blip-chat-bot-status, #blip-chat-welcome-container ~ #message-input {
        display: none;
    }
    #blip-chat-welcome-container .blip-chat-start {
        background-color: transparent !important;
        border: 1px solid #0096FA;
        font-weight: bold;
        border-radius: 8px;
        padding: .6rem 4rem;
    }
    .blip-chat-powered {
        display: none;
    }
    .blip-chat-bot-description {
        height: 3rem;
    }
    #blip-chat-header {
        display: none;
    }
    #app {
        padding-top: 0;
        height: 95%;
        background-color: transparent !important;
    }
    .blip-container {
        margin-bottom: 0;
    }
    .blip-card-container, .blip-card .bubble {
        font-size: 14px;
    }
    #message-input {
        box-sizing: border-box;
        border: 1px solid #0096FA;
        border-radius: 6px;
        background: #141414;
        width: 80%;
        position: relative;
        height: 57px;
        max-height: 57px;
    }
    #message-input textarea {
        background: #141414;
        font-size: 14px;
        color: #A3A3A3;
        width: 85%;
        max-height: 50px;
    }
    #message-input .message-options {
        display: none;
    }
`;
