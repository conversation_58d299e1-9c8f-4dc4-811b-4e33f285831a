import { I<PERSON><PERSON><PERSON>er, IScope } from 'angular';
import { BlipChat } from 'blip-chat-widget';

import { IStateService } from 'angular-ui-router';
import { ChatService } from 'modules/chat/ChatService';
import { ContextProcessorService, Contexts } from 'modules/core/ContextProcessorService';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { TenantService } from 'modules/application/tenant/TenantService';
import { InitializeUserService } from 'modules/core/InitializeUserService';
import { ApplicationCreationFeatures } from 'modules/application/create/ApplicationCreationFeatures';

import {
    APPLICATION_CREATION_TEMPLATE_CONFIRMED,
    APPLICATION_CREATION_TEMPLATE_TESTED,
    CREATE_CHATBOT_BACK_PREVIOUS_STEP
} from '../../CreateApplicationSegmentEvents';
import { customStyle } from './CustomStyle';
import * as uuid from 'uuid';

export class TestTemplateController implements IController {
    template: any;
    blipChat: BlipChat;
    templateLang: string;
    userName: string;
    customStyle: string;
    customerServiceTemplate: any;
    templateTestName: string;
    templateTestPristine: boolean = true;
    private getCustomerInactivityDateEnable: Date;
    isNewCustomer: boolean = false;

    constructor(
        private $rootScope,
        private $scope: IScope,
        private $state: IStateService,
        private $translate,
        private ChatService: ChatService,
        private ContextProcessorService: ContextProcessorService,
        private SegmentService: SegmentService,
        private $window: Window,
        private AccountService2,
        private TenantService: TenantService,
        private InitializeUserService: InitializeUserService,
    ) {
        this.postMessageListener = this.postMessageListener.bind(this);
        this.template = 'blip_deskCustomerService';
        this.customStyle = customStyle;

        this.onInit();

        const deregisterDestroyEvent = this.$rootScope.$on(
            '$stateChangeStart', async (_, toState) => {
                if (!toState.name.includes('auth.application.create.test')) {
                    this.onDestroy();
                }
            });
        $scope.$on('$destroy', () => {
            deregisterDestroyEvent();
        });
    }

    async onInit() {
        await this.checkFeatures();
        this.$scope.$watch(() => {
            return this.templateLang;
        }, () => {
            this.createBlipChat();
        });

        this.$window.addEventListener('message', this.postMessageListener);
        this.getUserData();
        this.enableCustomerInactivityDescriptions();

        if (this.ChatService.built) {
            this.ChatService.destroy();
        }

        this.templateTestName = this.ContextProcessorService?.tenant?.name ||
            await this.$translate('createApplication.templates.chatNameFallback');
    }

    onDestroy() {
        this.$window.removeEventListener('message', this.postMessageListener);
        this.blipChat.destroy();
        this.blipChat = undefined;
    }

    private async checkFeatures() {
        if (!this.$rootScope.featureToggleInitialized) {
            await this.InitializeUserService.initFeatureToggle();
        }

        this.getCustomerInactivityDateEnable = await ApplicationCreationFeatures
            .valueOfCustomerInactivityDate();
    }

    async getUserData() {
        const user = await this.AccountService2.me();
        this.templateLang = user.culture;
        this.userName = user.fullName;
    }

    async createBlipChat() {
        const testContainer = document.getElementById('test-template-container');
        const loadingGif = document.getElementById('loading-template');
        if (testContainer) {
            testContainer.style.opacity = '0';
        }
        if (loadingGif) {
            loadingGif.style.visibility = 'visible';
        }

        this.blipChat = new BlipChat()
            .withAppKey('dGVtcGxhdGVhdGVuZGltZW50b2h1bWFubzExOmFlMjhjYmFjLTNhMjktNDI4NS1iNjY2LWNlYzcyMWNiNTQ2Yw==')
            .withButton({ 'color': '#2CC3D5' })
            .withTarget('test-template-container')
            .withCustomCommonUrl('https://take.chat.blip.ai/')
            .withAccount({
                fullName: this.userName
            })
            .withAuth({
                authType: BlipChat.DEV_AUTH,
                userIdentity: uuid.v4(),
                userPassword: 'password'
            })
            .withCustomStyle(this.customStyle);

        this.blipChat.build();
    }

    postMessageListener(message) {
        if (message.data.code === 'ChatConnected') {
            const testContainer = document.getElementById('test-template-container');
            const loadingGif = document.getElementById('loading-template');
            if (testContainer) {
                testContainer.style.opacity = '1';
            }
            if (loadingGif) {
                loadingGif.style.visibility = 'hidden';
            }
        }

        if (this.templateTestPristine && message.data.code === 'NewBotMessage') {
            this.templateTestPristine = false;
            this.SegmentService.createOrganizationTrack(APPLICATION_CREATION_TEMPLATE_TESTED, {
                template: this.template,
                templateLang: this.templateLang
            });
        }
    }

    continueToNameStep() {
        this.SegmentService.createOrganizationTrack(APPLICATION_CREATION_TEMPLATE_CONFIRMED, {
            template: this.template,
        });

        this.$state.go('^.name', {
            template: this.template,
            templateLang: this.templateLang
        });
    }

    backFromTestTemplateStep() {
        this.SegmentService.createOrganizationTrack(CREATE_CHATBOT_BACK_PREVIOUS_STEP, {
            currentStep: 'testTemplate',
            template: this.template,
        });

        this.$state.go('^.marketplace');
    }

    async getTenantCreationDate() {
        const tenantId = this.ContextProcessorService.context === Contexts.Tenants
        ? this.ContextProcessorService.tenant.id : undefined;
        if (tenantId) {
            const tenant = await this.TenantService.get(tenantId);
            if (tenant.creationDate) {
                return new Date(tenant.creationDate);
            }
        }
    }

    async enableCustomerInactivityDescriptions() {
        const tenantDate = await this.getTenantCreationDate();
        if (tenantDate) {
            this.isNewCustomer = tenantDate.toJSON() >= this.getCustomerInactivityDateEnable.toJSON();
        }
    }
}
