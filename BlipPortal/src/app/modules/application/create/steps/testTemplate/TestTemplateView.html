<div
    id="create-application-template-step"
    class="flex flex-column items-center"
>
    <div class="tagline-title-container">
        <bds-typo variant="fs-20" tag="h4" margin="false" translate
            >createApplication.tagline</bds-typo
        >
        <bds-typo variant="fs-32" tag="h2" translate
            >createApplication.templates.title</bds-typo
        >
    </div>
    <div class="template-content flex justify-between items-center w-100">
        <div class="template-content-description">
            <bds-typo variant="fs-24" tag="h3" bold="semi-bold" class="content-description-title" translate
                >createApplication.templates.customerService.title</bds-typo
            >
            <bds-typo variant="fs-14" class="content-detailed-description" translate
                >createApplication.templates.customerService.description</bds-typo
            >
            <div class="feature-description">
                <bds-icon name="checkball" size="small"></bds-icon>
                <bds-typo variant="fs-14" translate
                    >createApplication.templates.customerService.feature1</bds-typo
                >
            </div>
            <div class="feature-description">
                <bds-icon name="checkball" size="small"></bds-icon>
                <bds-typo variant="fs-14" translate
                    >createApplication.templates.customerService.feature2</bds-typo
                >
            </div>
            <div class="feature-description">
                <bds-icon name="checkball" size="small"></bds-icon>
                <bds-typo variant="fs-14" translate
                    >createApplication.templates.customerService.feature3</bds-typo
                >
            </div>
            <div class="feature-description">
                <bds-icon name="checkball" size="small"></bds-icon>
                <bds-typo variant="fs-14" translate
                    >createApplication.templates.customerService.feature4</bds-typo
                >
            </div>
            <div class="feature-description" ng-show="$ctrl.isNewCustomer">
                <bds-icon name="checkball" size="small"></bds-icon>
                <bds-typo variant="fs-14" translate
                    >createApplication.templates.customerService.feature5</bds-typo
                >
            </div>
            <div class="feature-description" ng-show="$ctrl.isNewCustomer">
                <bds-icon name="checkball" size="small"></bds-icon>
                <bds-typo variant="fs-14" translate
                    >createApplication.templates.customerService.feature6</bds-typo
                >
            </div>
            <div class="description-actions flex justify-between">
                <bds-button
                    variant="secondary"
                    icon="arrow-left"
                    ng-click="$ctrl.backFromTestTemplateStep()"
                    data-testid="create-chatbot-template-back-button"
                    translate
                    >createApplication.templates.back</bds-button
                >
                <bds-button
                    ng-click="$ctrl.continueToNameStep()"
                    data-testid="create-chatbot-template-continue-button"
                    translate
                    >createApplication.templates.useTemplate</bds-button
                >
            </div>
        </div>
        <div class="template-test overflow-hidden relative">
            <div class="template-test-header flex items-center">
                <img
                    class="br-100"
                    src="https://s3-sa-east-1.amazonaws.com/msging.net/Services/Images/ffcdb592-e9af-4a7c-9141-37d4bd123106"
                />
                <div>
                    <bds-typo variant="fs-14" bold="semi-bold"
                        >{{$ctrl.templateTestName}}</bds-typo
                    >
                    <bds-typo variant="fs-10" translate
                        >createApplication.templates.online</bds-typo
                    >
                </div>
            </div>
            <div id="test-template-container" class="w-100"></div>
            <div id="loading-template" class="absolute">
                <bds-loading-spinner
                    size="small"
                    color="light"
                ></bds-loading-spinner>
            </div>
        </div>
    </div>
</div>
