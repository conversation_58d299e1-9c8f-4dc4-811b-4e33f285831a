<div class="flex justify-center">
    <bds-grid gap="4" class="ai-agent-form-container flex flex-column">
        <bds-grid gap="2" padding="3" class="ai-agent-form-header">
            <div class="ai-agent-form-header__illustration">
                <bds-illustration
                    name="ai-blipinho"
                    type="blip-solid"
                ></bds-illustration>
            </div>
            <div class="ai-agent-form-header__text flex flex-column">
                <bds-typo variant="fs-20" bold="bold" margin="false" translate
                    >createApplication.aiAgent.create.title</bds-typo
                >
                <bds-typo variant="fs-16" translate
                    >createApplication.aiAgent.create.description</bds-typo
                >
            </div>
        </bds-grid>
        <bds-grid gap="3" padding="none" class="flex flex-column">
            <div class="ai-agent-form-section flex flex-column">
                <bds-typo variant="fs-16" bold="bold" translate
                    >createApplication.aiAgent.create.basicConfiguration.title</bds-typo
                >
                <div class="ai-agent-form-section__row flex">
                    <bds-input
                        type="text"
                        name="name"
                        value="{{$ctrl.aiAgent.name}}"
                        custom-events="[{ event:'bdsChange', cb: $ctrl.onInputNameChange }]"
                        label="{{ 'createApplication.aiAgent.create.basicConfiguration.assistantName.label' | translate }}"
                        placeholder="{{ 'createApplication.aiAgent.create.basicConfiguration.assistantName.placeholder' | translate }}"
                        required
                    ></bds-input>
                    <bds-input
                        type="text"
                        name="companyProductName"
                        value="{{$ctrl.aiAgent.companyProductName}}"
                        custom-events="[{ event:'bdsChange', cb: $ctrl.onInputCompanyProductNameChange }]"
                        label="{{ 'createApplication.aiAgent.create.basicConfiguration.companyProductName.label' | translate }}"
                        placeholder="{{ 'createApplication.aiAgent.create.basicConfiguration.companyProductName.placeholder' | translate }}"
                        required
                    ></bds-input>
                </div>
                <bds-select
                    name="toneAndVoice"
                    value="{{$ctrl.aiAgent.toneAndVoice}}"
                    custom-events="[{ event:'bdsChange', cb: $ctrl.onSelectToneAndVoiceChange }]"
                    label="{{ 'createApplication.aiAgent.create.basicConfiguration.toneAndVoice.label' | translate }}"
                    placeholder="{{ 'createApplication.aiAgent.create.basicConfiguration.toneAndVoice.placeholder' | translate }}"
                    options="{{$ctrl.toneAndVoiceOptions}}"
                    required
                ></bds-select>
            </div>
            <div class="ai-agent-form-section flex flex-column">
                <bds-typo variant="fs-16" bold="bold" translate
                    >createApplication.aiAgent.create.additionalResources.title</bds-typo
                >
                <div class="ai-agent-switch-container inline-flex items-center">
                    <bds-icon theme="outline" name="agent"></bds-icon>
                    <bds-typo
                        class="flex-auto"
                        variant="fs-14"
                        bold="bold"
                        translate
                        >createApplication.aiAgent.create.additionalResources.humanService</bds-typo
                    >
                    <bds-switch
                        ng-checked="$ctrl.aiAgent.hasHumanService"
                        custom-events="[{ event:'bdsChange', cb: $ctrl.onHasHumanServiceToggle }]"
                        size="short"
                    ></bds-switch>
                </div>
            </div>
        </bds-grid>
        <div class="flex justify-between">
            <bds-button
                variant="secondary"
                icon="arrow-left"
                ng-click="$ctrl.backFromCreateStep()"
                translate
                >createApplication.aiAgent.create.back</bds-button
            >
            <bds-button
                ng-click="$ctrl.createAiAgent()"
                variant="primary"
                ng-disabled="!$ctrl.validateForm()"
                translate
                >createApplication.aiAgent.create.create</bds-button
            >
        </div>
    </bds-grid>
</div>
