import { AiAgentFeature } from 'modules/application/misc/AiAgentFeature';
import { applicationCreateAiAgentStateName } from '../..';

export const aiAgentsCreateStateName = 'auth.application.aiAgents.create';
export const aiAgentsDiscoveryStateName = 'auth.application.aiAgents.about';

export const selectWhichCreationRouteToRedirect = async () => {
    return (await AiAgentFeature.allowCreatingAiAgentInMfeEnabled())
        ? aiAgentsCreateStateName
        : applicationCreateAiAgentStateName;
};
