import { I<PERSON><PERSON>roller, translate } from 'angular';
import { IStateService } from 'angular-ui-router';
import { CreateApplicationService } from 'modules/application/create/CreateApplicationService';
import { LoadingService } from 'modules/ui/LoadingService';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { AnswersService as AnswersServiceFromSubmodule } from 'modules/application/detail/portal-submodule-builder/templates/builder/services/AnswersService';
import { AnswersService } from 'modules/application/detail/templates/builder/services/AnswersService';
import { AssistantConfiguration as AssistantConfigurationFromSubmodule } from 'modules/application/detail/portal-submodule-builder/templates/builder/models/AssistantConfiguration';
import { AssistantConfiguration } from 'modules/application/detail/templates/builder/models/AssistantConfiguration';
import { Assistant as AssistantFromSubmodule } from 'modules/application/detail/portal-submodule-builder/templates/builder/models/Assistant';
import { Assistant } from 'modules/application/detail/templates/builder/models/Assistant';
import { BucketService } from 'modules/application/misc/BucketService';
import { aiAgentsHomeStateName } from 'modules/application/detail/aiAgents/index';
import AuthenticationService from 'modules/login/AuthenticationService';
import { ContextProcessorService } from 'modules/core/ContextProcessorService';
import { AccountService2 } from 'modules/account/AccountService2';
import { BlipToastService } from 'modules/shared/BlipToastService';
import { AiAgentsFeatures } from 'modules/application/detail/aiAgents/AiAgentsFeatures';

import './CreateAiAgent.scss';
import { useBuilderSubmodule } from 'app.constants';

const BLIP_AI_AGENT_PUBLISH_TEMPLATE = 'blip_aiAgent_publish';

const CLIENT_CREDENTIALS_BUCKET_KEY = 'blip_ai_suite:client_credentials';
const AI_AGENT_REDIRECT_BUCKET_KEY_PROD = 'blip_ai_suite_bot_config';
const AI_AGENT_REDIRECT_BUCKET_KEY_DEV = 'blip_ai_suite_bot_config_working';
const AI_AGENT_BUILDER_WORKING_FLOW = 'blip_portal:builder_working_flow';
const ASSISTANT_ID_BUCKET_KEY = 'blip_ai_suite_assistant_id';
const CREATE_CHATBOT = 'aiagent-create-chatbot-clicked';
const AI_AGENT_LAYOUT = 'AiAgent';

const BLIP_AI_AGENTS_ASSISTANT_ID = 'blip_ai_agents_assistant_id';
const BLIP_AI_AGENTS_PROFILE_CONFIG = 'blip_ai_agents_profile_config';
const BLIP_AI_AGENTS_PROFILE_CONFIG_WORKING = 'blip_ai_agents_profile_config_working';
const BLIP_AI_AGENTS_BOT_CONFIG = 'blip_ai_agents_bot_config';
const BLIP_AI_AGENTS_BOT_CONFIG_WORKING = 'blip_ai_agents_bot_config_working';

interface CreateAIAgentModel {
    name: string;
    companyProductName: string;
    toneAndVoice: string;
    hasHumanService: boolean;
}

interface ToneAndVoiceOption {
    label: string;
    value: string;
}

export class CreateAIAgentController implements IController {
    public aiAgent: CreateAIAgentModel;
    public toneAndVoiceOptions: ToneAndVoiceOption[];
    public isValidForm: boolean;
    constructor(
        private $state: IStateService,
        private $translate,
        private AuthenticationService: AuthenticationService,
        private ContextProcessorService: ContextProcessorService,
        private CreateApplicationService: CreateApplicationService,
        private LoadingService: LoadingService,
        private SegmentService: SegmentService,
        private AnswersService: AnswersService|AnswersServiceFromSubmodule,
        private BucketService: BucketService,
        private AccountService2: AccountService2,
        private BlipToastService: BlipToastService,
        private AI_AGENT_STORAGE_BASE_URL: string,
    ) {
        this.initialize();
    }

    async initialize() {
        this.isValidForm = false;

        this.aiAgent = {
            name: '',
            companyProductName: '',
            toneAndVoice: '',
            hasHumanService: false,
        };

        this.toneAndVoiceOptions = [
            { label: await this.getTranslation('friendly'), value: 'amigavel' },
            { label: await this.getTranslation('funny'), value: 'divertido' },
            { label: await this.getTranslation('technical'), value: 'tecnico' },
        ];
    }

    private async getTranslation(key: string): Promise<string> {
        return await this.$translate(`createApplication.aiAgent.create.basicConfiguration.toneAndVoice.options.${key}`);
    }

    public backFromCreateStep = () => {
        this.$state.go('auth.application.list');
    }

    public onInputNameChange = (event: CustomEvent) => {
        this.aiAgent.name = event.detail.value;
    }

    public onInputCompanyProductNameChange = (event: CustomEvent) => {
        this.aiAgent.companyProductName = event.detail.value;
    }

    public onSelectToneAndVoiceChange = (event: CustomEvent) => {
        this.aiAgent.toneAndVoice = event.detail.value;
    }

    public onHasHumanServiceToggle = (event: CustomEvent) => {
        this.aiAgent.hasHumanService = event.detail.checked;
    }

    public async createAiAgent() {
        try {
            this.LoadingService.startLoading(false);
            if (!this.validateForm()) {
                return;
            }

            const assistant: Assistant = {
                assistantName: this.aiAgent.name,
                tenantId: '',
                assistantId: '',
                configs: {},
                createdAt: '',
                updatedAt: '',
            };

            const assistantFromSubmodule: AssistantFromSubmodule = {
                assistantName: this.aiAgent.name,
                tenantId: '',
                assistantId: '',
                configs: {},
                createdAt: '',
                updatedAt: '',
            };

            const assistantConfiguration: AssistantConfiguration = {
                assistantId: '',
                invalidAnswer: '',
                guidelines: '',
                redirectGuidelines: '',
                assistantName: this.aiAgent.name,
                companyName: this.aiAgent.companyProductName,
                profile: this.aiAgent.toneAndVoice,
                agentType: ''
            };

            const AssistantConfigurationFromSubmodule: AssistantConfigurationFromSubmodule = {
                assistantId: '',
                invalidAnswer: '',
                guidelines: '',
                redirectGuidelines: '',
                assistantName: this.aiAgent.name,
                companyName: this.aiAgent.companyProductName,
                profile: this.aiAgent.toneAndVoice,
                agentType: ''
            };

            const assistantToUse = useBuilderSubmodule ? assistantFromSubmodule : assistant;
            const assistantConfigurationToUse = useBuilderSubmodule ? AssistantConfigurationFromSubmodule : assistantConfiguration;

            const application = await this.createApplicationAsync({ name: this.aiAgent.name, layout: AI_AGENT_LAYOUT }, BLIP_AI_AGENT_PUBLISH_TEMPLATE);
            const botCredentials = await this.generateAgentCredentialsAsync(application.shortName);
            const configuration = await this.createAgentAsync(assistantToUse, assistantConfigurationToUse, botCredentials);
            await this.createBucketAsync(this.aiAgent.hasHumanService, configuration.assistantId, botCredentials);
            this.createApplicationTrack(application);
            this.goToApplicationDetails(application);
        } catch (err) {
            const errorMessage = await this.$translate('createApplication.aiAgent.create.toast.error');
            const title = await this.$translate('createApplication.aiAgent.create.toast.tittle');
            this.BlipToastService.show('danger', {
                title: title,
                msg: errorMessage
            });
            this.createApplicationTrack({template: BLIP_AI_AGENT_PUBLISH_TEMPLATE }, false, err.message);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    private createApplicationTrack(application: any, success: boolean = true, error?: string) {
        this.SegmentService.createApplicationTrack({
            trackEvent: CREATE_CHATBOT,
            application: application,
            payload: {
                template: application?.template,
                marketplaceId: application?.marketplace?.id,
                voiceTone: this.aiAgent.toneAndVoice,
                hasHumanService: this.aiAgent.hasHumanService,
                companyName: this.aiAgent.companyProductName,
                assistantName: this.aiAgent.name,
                creationStatus: success ? 'success' : 'error',
                errorType: error
            }
        });
    }

    private async createBucketAsync(hasHumanService: boolean, assistantId: string, botCredentials: { client_id: string, client_secret: string }) {
        try {
            const aiAgentTemplateWorking = await this.getAiAgentTemplateAsync();
            const defaultLanguage = await this.getDefaultLanguageAsync();

            const aiAgentRedirect = {
                culture: defaultLanguage,
                redirect: {
                    human: hasHumanService,
                    service: false,
                    serviceName: ''
                }
            };

            const profileConfiguration = {
                culture: defaultLanguage
            };

            const redirectStrategy = {
                negativeRedirectStrategy: {
                    human: hasHumanService,
                    service: false,
                    serviceName: '',
                    retryLimit: 3
                },
                positiveRedirectStrategy: {
                    enable: false,
                    serviceName: ''
                },
                inactiveRedirectStrategy: {
                    enable: false,
                    serviceName: '',
                },
                unavailabilityRedirectStrategy: {
                    enable: true,
                    serviceName: '',
                },
                regexRedirectServiceStrategy: {
                    enable: false,
                    patterns: [],
                    serviceName: '',
                },
                regexRedirectHumanStrategy: {
                    enable: false,
                    patterns: []
                }
            };

            const isEnableAiAgentNewSettings = await AiAgentsFeatures.isEnableAiAgentNewSettings();
            //default configurations
            await this.BucketService.set(AI_AGENT_BUILDER_WORKING_FLOW, aiAgentTemplateWorking, 'application/json');
            await this.BucketService.set(CLIENT_CREDENTIALS_BUCKET_KEY, botCredentials, 'application/json');
            await this.BucketService.set(ASSISTANT_ID_BUCKET_KEY, assistantId, 'text/plain');

            if (isEnableAiAgentNewSettings) {
                //new configurations
                await this.BucketService.set(BLIP_AI_AGENTS_ASSISTANT_ID, assistantId, 'text/plain');
                await this.BucketService.set(BLIP_AI_AGENTS_BOT_CONFIG, redirectStrategy, 'application/json');
                await this.BucketService.set(BLIP_AI_AGENTS_BOT_CONFIG_WORKING, redirectStrategy, 'application/json');
                await this.BucketService.set(BLIP_AI_AGENTS_PROFILE_CONFIG, profileConfiguration, 'application/json');
                await this.BucketService.set(BLIP_AI_AGENTS_PROFILE_CONFIG_WORKING, profileConfiguration, 'application/json');
            } else {
                //obsolete configurations
                await this.BucketService.set(AI_AGENT_REDIRECT_BUCKET_KEY_DEV, aiAgentRedirect, 'application/json');
                await this.BucketService.set(AI_AGENT_REDIRECT_BUCKET_KEY_PROD, aiAgentRedirect, 'application/json');
            }
        } catch (err) {
            console.error('Failed to create bucket:', err);
            throw new Error('Failed to create bucket');
        }
    }

    private async getDefaultLanguageAsync() {
        const userLanguage = (await this.AccountService2.me()).culture;
        return userLanguage;
    }

    private async createAgentAsync(assistant: Assistant|AssistantFromSubmodule, configuration: AssistantConfiguration|AssistantConfigurationFromSubmodule, botCredentials: { client_id: string, client_secret: string }): Promise<AssistantConfiguration> {
        try {
            assistant = await this.AnswersService.createAssistant(botCredentials, assistant, configuration);

            configuration = {
                ...configuration,
                assistantId: assistant.assistantId,
                tenantId: assistant.tenantId,
                configurationId: assistant.configs.DEV,
                createdAt: assistant.createdAt,
                updatedAt: assistant.updatedAt,
            };

            await this.AnswersService.publishAssistant(configuration);
            return configuration;
        } catch (err) {
            console.error('Failed to create agent:', err);
            throw new Error('Failed to create agent');
        }
    }

    private async getAiAgentTemplateAsync(): Promise<any> {
        try {
            const aiAgentContainerBaseUrl = this.AI_AGENT_STORAGE_BASE_URL;
            const response = await fetch(`${aiAgentContainerBaseUrl}/aiagentstemplates/aiAgentTemplate_working.json`);
            if (!response.ok) {
                throw new Error('Failed to fetch AI agent template');
            }
            const aiAgentTemplate = await response.json();
            return aiAgentTemplate;
        } catch (err) {
            throw err;
        }
    }

    private async createApplicationAsync(application: any, template: string): Promise<any> {
        try {
            application = await this.CreateApplicationService.createApplication(application, template);
            return application;
        } catch (err) {
            console.error('An exception occurred while creating the application:', err);
            throw new Error('Failed to create the application');
        }
    }

    private async generateAgentCredentialsAsync(botId: string): Promise<{ client_id: string, client_secret: string }> {
        try {
            const tenantId = this.ContextProcessorService.tenant.id;
            await this.AnswersService.setRegistration(tenantId, botId, this.AuthenticationService.token);
            const accessToken = await this.AnswersService.replaceAccessToken(tenantId, botId, this.AuthenticationService.token);
            return await this.AnswersService.generateBotCredentials(tenantId, botId, accessToken);
        } catch (err) {
            console.error('Failed to generate agent credentials:', err);
            throw new Error('Failed to generate agent credentials');
        }
    }

    private async goToApplicationDetails(application: any): Promise<void> {
        this.$state.go(aiAgentsHomeStateName, {
            shortName: application.shortName,
        });
    }

    private validateForm = () => {
        const { name, companyProductName, toneAndVoice } = this.aiAgent;
        return name.length > 0 && companyProductName.length > 0 && toneAndVoice.length > 0;
    }
}
