<div id="create-application-router-step" class="flex flex-column items-center">
    <div class="tagline-title-container">
        <bds-typo variant="fs-20" tag="h4" margin="false" translate
            >createApplication.taglineRouter</bds-typo
        >
    </div>
    <div class="router-step-content flex items-center justify-between">
        <img src="/assets/img/templates/router.svg" />
        <div class="router-step-description">
            <bds-typo variant="fs-24" tag="h3" bold="semi-bold" translate
                >createApplication.router.title</bds-typo
            >
            <bds-typo variant="fs-14" translate
                >createApplication.router.description</bds-typo
            >
            <a ng-click="$ctrl.openRouterLearnMore()" class="flex"
                ><bds-icon name="external-file" size="small"></bds-icon>
                <bds-typo variant="fs-14" bold="bold" translate
                    >createApplication.router.learnMore</bds-typo
                ></a
            >
            <bds-button
                ng-click="$ctrl.selectTemplate('master')"
                data-testid="create-chatbot-router-continue-button"
                translate
                >createApplication.taglineRouter</bds-button
            >
        </div>
    </div>
</div>
