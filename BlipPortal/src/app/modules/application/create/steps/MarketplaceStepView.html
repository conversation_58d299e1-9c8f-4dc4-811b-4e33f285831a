<div id="create-application-marketplace-step" class="tc">
    <div class="tagline-title-container">
        <bds-typo variant="fs-20" tag="h4" margin="false" translate
            >createApplication.tagline</bds-typo
        >
        <bds-typo variant="fs-32" tag="h2" translate
            >createApplication.marketplace.title</bds-typo
        >
    </div>
    <div class="marketplace-step-options flex justify-center flex-wrap">
        <bds-paper
            class="option-card flex flex-column items-center pointer relative"
            ng-click="$ctrl.selectTemplate('blip_deskCustomerService')"
            data-testid="create-chatbot-use-template-card"
        >
            <bds-chip-tag color="success" translate
                >createApplication.marketplace.template.tag</bds-chip-tag
            >
            <bds-icon name="integration" size="brand"></bds-icon>
            <bds-typo
                variant="fs-20"
                bold="bold"
                margin="false"
                line-height="plus"
                translate
                >createApplication.marketplace.template.title</bds-typo
            >
            <bds-typo variant="fs-14" translate
                >createApplication.marketplace.template.description</bds-typo
            >
        </bds-paper>
        <bds-paper
            class="option-card flex flex-column items-center pointer relative"
            ng-click="$ctrl.selectTemplate('builder')"
            data-testid="create-chatbot-from-scratch-card"
        >
            <bds-icon name="file-empty-file" size="brand"></bds-icon>
            <bds-typo
                variant="fs-20"
                bold="bold"
                margin="false"
                line-height="plus"
                translate
                >createApplication.marketplace.scratch.title</bds-typo
            >
            <bds-typo variant="fs-14" translate
                >createApplication.marketplace.scratch.description</bds-typo
            >
        </bds-paper>
    </div>
</div>
