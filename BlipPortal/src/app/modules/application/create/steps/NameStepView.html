<div id="create-application-name-step" class="tc flex flex-column items-center">
    <div class="tagline-title-container">
        <bds-typo
            ng-if="$ctrl.template != 'master'"
            variant="fs-20"
            tag="h4"
            margin="false"
            translate
            >createApplication.tagline</bds-typo
        >
        <bds-typo
            ng-if="$ctrl.template == 'master'"
            variant="fs-20"
            tag="h4"
            margin="false"
            translate
            >createApplication.taglineRouter</bds-typo
        >
        <bds-typo
            variant="fs-32"
            tag="h2"
            data-testid="create-chatbot-name-title-typo"
            >{{$ctrl.provideANameText}}</bds-typo
        >
    </div>
    <upload-button
        id="image"
        name="image"
        ng-model="$ctrl.application.image"
        ng-mime-type="image/png, image/jpg, image/jpeg, image/gif"
        accept="'.gif, .png, .jpeg, .jpg'"
        data-testid="create-chatbot-image-input"
    >
    </upload-button>
    <material-input initial-value="$ctrl.application.name">
        <input
            type="text"
            id="name"
            name="shortName"
            ng-model="$ctrl.application.name"
            ng-change="$ctrl.validateSpecialCharacter()"
            class="u-full-width"
            required
            ng-minlength="2"
            ng-maxlength="30"
            ng-disabled="editing"
            data-testid="create-chatbot-name-input"
        />
        <label ng-if="$ctrl.template != 'master'" for="name" translate
            >createApplication.name.name</label
        >
        <label ng-if="$ctrl.template == 'master'" for="name" translate
            >createApplication.name.nameRouter</label
        >
        <span
            counter-for="$ctrl.applicationForm.shortName"
            ng-maxlength="30"
            class="input-right bp-c-rooftop"
        ></span>
    </material-input>
    <div class="create-application-actions flex justify-between">
        <bds-button
            variant="secondary"
            icon="arrow-left"
            ng-click="$ctrl.backFromNameStep()"
            data-testid="create-chatbot-name-back-button"
            translate
            >createApplication.name.back</bds-button
        >
        <bds-button
            ng-if="$ctrl.template != 'master'"
            type="submit"
            data-testid="create-chatbot-name-submit-button"
            translate
            >createApplication.tagline</bds-button
        >
        <bds-button
            ng-if="$ctrl.template == 'master'"
            type="submit"
            data-testid="create-chatbot-name-submit-button"
            translate
            >createApplication.taglineRouter</bds-button
        >
    </div>
</div>
