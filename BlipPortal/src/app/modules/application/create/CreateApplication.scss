@import '~assets/scss/main';

#create-application-container {
    background-color: $color-surface-3;
    color: $color-content-default;
    padding-top: 3rem;

    .logo-container {
        margin-bottom: 48px;
        margin-left: 48px;
    }
    .logo-image {
        margin-top: 0.5rem;
        margin-bottom: 3rem;
        width: 5rem;
    }

    .right-logo-image {
        width: 121px;
        margin-left: 48px;
    }

    .close-icon {
        margin-right: 3rem;
    }

    .create-application-form {
        flex-grow: 1;
        margin-bottom: 3rem;
    }

    .create-application-footer {
        width: 54rem;
        border-top: 1px solid $color-neutral-dark-rooftop;
        padding: 1.5rem;
        a > bds-typo {
            color: $color-brand;
        }
    }

    .tagline-title-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

#create-application-marketplace-step {
    .marketplace-step-options {
        gap: 2.5rem;
        margin-top: 3.5rem;

        .option-card {
            background-color: rgba(255, 255, 255, 0.07);
            box-shadow: 0 0.25rem 1rem rgba(10, 15, 26, 0.3);
            padding: 3.75rem 1.5rem;
            width: 20rem;
            gap: 0.75rem;

            bds-chip-tag {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: -0.75rem;
            }
        }
    }
}

#create-application-name-step {
    .upload-button {
        margin: 5rem 0 1.25rem 0;
    }

    material-input {
        width: 28rem;
        max-width: 90%;
        input {
            color: $color-content-default;
        }
    }

    .create-application-actions {
        width: 28rem;
        margin-top: 3rem;
        max-width: 90%;
    }
}

#create-application-template-step {
    .template-content {
        max-width: 52.5rem;
        gap: 2.5rem;
        margin-top: 3.5rem;

        .template-content-description {
            max-width: 26rem;

            .feature-description {
                display: flex;
                gap: 0.25rem;
                align-items: center;
                margin-bottom: 0.75rem;

                bds-icon {
                    color: $color-success;
                }
            }

            .feature-description:nth-of-type(1) {
                margin-top: 1.75rem;
            }

            .description-actions {
                margin-top: 3rem;
            }
        }

        .template-test {
            box-shadow: 0px 4px 16px rgba(10, 15, 26, 0.3);
            background-color: $color-surface-2;
            border-radius: 20px;
            min-width: 20rem;

            .template-test-header {
                background-color: rgba(255, 255, 255, 0.08);
                padding: 0.5rem 1rem;

                img {
                    height: 1.75rem;
                    margin-right: 0.75rem;
                }
            }

            #test-template-container {
                height: 26rem;
            }

            #loading-template {
                top: calc(50% + 25px);
                left: 50%;
                transform: translateX(-50%) translateY(-50%);
            }
        }
    }
}

#create-application-router-step {
    .router-step-content {
        max-width: 52.5rem;
        width: 100%;
        gap: 2.5rem;
        margin-top: 7rem;

        img {
            max-width: 18.75rem;
            width: 40%;
        }

        .router-step-description {
            max-width: 27.5rem;

            a {
                gap: 0.5rem;
                margin-top: 1rem;
                margin-bottom: 3rem;
            }
        }
    }
}
