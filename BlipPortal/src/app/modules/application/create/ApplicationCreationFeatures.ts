import { FeatureToggleClientService } from 'feature-toggle-client';

const CUSTOMER_INACTIVITY_AS_DEFAULT_INITIAL_DATE: string = 'customer-inactivity-as-default-initial-date';
const NEW_PUBLISH_FLOW_AI_AGENT: string = 'enable-new-publish-flow-ai-agent';
const AI_AGENT_TEMPLATE_HOSTING: string = 'ai-agent-template-hosting';

export class ApplicationCreationFeatures {
    private static _customerInactivityAsDefaulDate: Date;

    static async valueOfCustomerInactivityDate() {
        const stringDate = await FeatureToggleClientService.getInstance().isUserFeatureEnabled(
            CUSTOMER_INACTIVITY_AS_DEFAULT_INITIAL_DATE,
        );

        this._customerInactivityAsDefaulDate = new Date(stringDate);
        return this._customerInactivityAsDefaulDate;
    }

    static async newPublishFlowAiAgentIsEnabled() {
        return await FeatureToggleClientService.getInstance().isUserFeatureEnabled(
            NEW_PUBLISH_FLOW_AI_AGENT,
            false,
        );
    }

    static async aiAgentTemplateHostingIsEnabled() {
        return FeatureToggleClientService.getInstance().isUserFeatureEnabled(
            AI_AGENT_TEMPLATE_HOSTING,
            false
        );
    }
}
