{"id": "blip_aiAgent_publish", "steps": [{"type": "SendCommand", "arguments": {}}, {"type": "PublishFlow", "arguments": {"settings": {"builder:minimumIntentScore": "0.5", "dateTimeOffset": "-3", "startDate": "08:30", "endDate": "18:50", "workDays": "0,1,2,3,4,5,6"}, "flow": {}, "globalActions": {"$contentActions": [], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "isAiGenerated": false, "$tags": [], "id": "global-actions", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}}}, {"type": "PublishHelpdesks", "arguments": ["Lime"]}, {"type": "AddAgent", "arguments": ["owner"]}, {"type": "AddReport", "arguments": [{"name": "Atendimentos", "charts": [{"name": "Total de Solicitações", "dimension": "events", "id": "8113081b-b606-48c1-bae0-6cb07d72c0ce", "category": "AtendimentosTota<PERSON>", "chartType": "counter", "order": 0}, {"name": "Total de Solicitações Atendidas", "dimension": "events", "id": "73de4cb4-23cf-4c44-ad49-1f5dcd83e41a", "category": "AtendimentosRealizados", "chartType": "counter", "order": 1}, {"name": "Total de Solicitações Não Atendidas", "dimension": "events", "id": "7c852e26-930c-44d6-8457-7e23b1cb817c", "category": "Solicitação", "chartType": "counter", "order": 2}, {"name": "Causa das Solicitações Não Atendidas", "dimension": "events", "id": "4e266e50-2511-4cc5-9747-bdc347956ab6", "category": "Solicitação", "chartType": "pie", "order": 3}]}, {"name": "Qualidade dos Atendimentos", "charts": [{"name": "Avaliações", "dimension": "events", "id": "235e9b3c-72e4-48c8-8b90-e0f26b2b5a12", "category": "Score", "chartType": "list", "order": 0}]}]}]}