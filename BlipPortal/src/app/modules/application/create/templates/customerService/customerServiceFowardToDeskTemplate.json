{"id": "blip_deskCustomerService", "steps": [{"type": "SendCommand", "arguments": {}}, {"type": "PublishFlow", "arguments": {"settings": {"builder:minimumIntentScore": "0.5"}, "flow": {"onboarding": {"$contentActions": [{"input": {"bypass": false, "$cardContent": {"document": {"id": "a5f855b2-7344-44ad-bf8b-75bccd63d288", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": false, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "welcome", "conditions": [{"source": "input", "comparison": "matches", "values": [".*"]}], "$connId": "con_3", "$invalid": false, "$id": "b4fbe0db-50a9-48f6-86d9-129e059650b1"}], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "onboarding", "root": true, "$position": {"top": "310px", "left": "211px"}, "$title": "Início", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "fallback": {"$contentActions": [{"input": {"bypass": true, "$cardContent": {"document": {"id": "95b10ba8-e351-4d16-9b10-ce14961cb1fd", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "error", "conditions": [{"source": "input", "comparison": "matches", "values": [".*"]}], "$connId": "con_8", "$invalid": false, "$id": "813a02d5-663b-433a-b0b6-36ea3ce2b858"}], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false}, "id": "fallback", "$position": {"top": "712px", "left": "210px"}, "$title": "Exceções", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "welcome": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000000", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000000", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000001", "type": "text/plain", "content": "Olá! É um prazer falar com você 😊", "metadata": {}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000001", "type": "text/plain", "content": "Olá! É um prazer falar com você 😊"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "cf8c9892-92a7-4a39-b169-9b8f5f7ef7a4", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "4c0d9be0-bd3d-4e72-b105-a2ac741e5149", "typeOfStateId": "state", "$connId": "con_13", "$id": "0699baf9-6d1d-4fed-896d-e7621bc95827", "conditions": [{"source": "input", "comparison": "exists", "values": []}], "$invalid": false}], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Atendimentos <PERSON>", "$invalid": false, "settings": {"extras": {}, "category": "AtendimentosTota<PERSON>", "action": "AtendimentosTota<PERSON>"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "welcome", "$position": {"top": "310px", "left": "440px"}, "$title": "1.0 - <PERSON><PERSON> vindas", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "error": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000002", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000002", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000003", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não entendi sua mensagem!", "metadata": {}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000003", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não entendi sua mensagem!"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "a1f34699-dd5c-43b6-8345-fde58dc32c0d", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false}, "id": "error", "$position": {"top": "712px", "left": "449px"}, "$title": "0.0 - <PERSON><PERSON>", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "3c555d39-68eb-466b-9063-6adeb01132fc": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "aa0d29d3-bab1-427d-9188-93b3638acbcd", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "aa0d29d3-bab1-427d-9188-93b3638acbcd", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "504721a0-3d44-4e95-82cc-dd33718b906a", "type": "text/plain", "content": "Infelizmente estamos fora do nosso horário de atendimento.", "metadata": {}}, "$cardContent": {"document": {"id": "504721a0-3d44-4e95-82cc-dd33718b906a", "type": "text/plain", "content": "Infelizmente estamos fora do nosso horário de atendimento."}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "fd1c385b-7adc-4efb-b829-3b137d4e97c0", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Solicitação Fora do Horário", "$invalid": false, "settings": {"extras": {}, "category": "Solicitação", "action": "Fora Horario Atendimento"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": ["First option", "Second option"], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "id": "3c555d39-68eb-466b-9063-6adeb01132fc", "root": false, "$title": "3.1 - Fora do Horário de Atendimento", "$position": {"top": "544px", "left": "1013px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "2b9bfb62-6346-4762-99ab-733d0c00d1a3": {"$contentActions": [{"action": {"$id": "aaee9794-11d3-4d29-8fcc-7e2ebe0ab74d", "$typeOfContent": "chat-state", "type": "SendMessage", "settings": {"id": "7504417a-d926-4372-907c-a109fd928b7b", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "7504417a-d926-4372-907c-a109fd928b7b", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "90ec55b8-aadd-4f92-bf1a-dec84eb60244", "type": "text/plain", "content": "Atendimento finalizado!"}, "$cardContent": {"document": {"id": "90ec55b8-aadd-4f92-bf1a-dec84eb60244", "type": "text/plain", "content": "Atendimento finalizado!"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "8b33aca6-bda4-4dec-8a22-5b5f51a2ec53", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "8b33aca6-bda4-4dec-8a22-5b5f51a2ec53", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "6bfaa60e-ce61-43fb-978e-80b1c5a15d8d", "type": "text/plain", "content": "De 0 a 10, qual nota você dá para nosso atendimento?", "metadata": {}}, "$cardContent": {"document": {"id": "6bfaa60e-ce61-43fb-978e-80b1c5a15d8d", "type": "text/plain", "content": "De 0 a 10, qual nota você dá para nosso atendimento?"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": false, "$cardContent": {"document": {"id": "64b7d83d-e2f4-4df4-8ad4-c9489c0f15f9", "type": "text/plain", "textContent": "Entrada do usuário", "content": "avaliacao"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false, "variable": "avaliacao"}, "$invalid": false}], "$conditionOutputs": [{"typeOfStateId": "state", "$id": "7340cfea-930a-4770-912f-00701767064f", "conditions": [{"source": "input", "comparison": "exists", "values": []}], "$invalid": false, "stateId": "97c535d6-09f8-46ee-9529-3d86e973bc66", "$connId": "con_18"}], "$enteringCustomActions": [{"$id": "29780b21-18ce-4ade-ab2f-16dd7dac5a83", "$typeOfContent": "", "type": "TrackEvent", "$title": "Registrar Atendimento Realizado", "$invalid": false, "settings": {"extras": {}, "category": "AtendimentosRealizados", "action": "AtendimentosRealizados"}, "conditions": []}, {"$id": "dcce298c-0d50-488b-af55-6c59b79a9893", "$typeOfContent": "", "type": "SetVariable", "$title": "Buscar Atendente", "$invalid": false, "settings": {"variable": "atendente", "value": "{{input.content@agentIdentity}}"}, "conditions": []}, {"$id": "b819279c-d819-4a9d-aa04-e23cafc59af0", "$typeOfContent": "", "type": "ExecuteScript", "$title": "Buscar Email do Atendente", "$invalid": false, "settings": {"function": "run", "source": "function run(atendente) {\n    return decodeURIComponent(atendente.split('@')[0]); \n}", "inputVariables": ["atendente"], "outputVariable": "emailAtendente", "LocalTimeZoneEnabled": true}, "conditions": []}], "$leavingCustomActions": [{"type": "ExecuteScript", "$title": "Padronizar Avaliação de 1 a 10", "$invalid": false, "settings": {"function": "run", "source": "function run(score) {\n  if(eUmNomero(score)){\n    score = parseInt(score);\n    if(score < 0) return 0;\n    if(score > 10) return 10;\n    return score;\n  }\n  return \"Outro\";\n}\n\n// Valida se entrada é um número\nfunction eUmNomero(num){\n  return !isNaN(num)\n}", "inputVariables": ["avaliacao"], "outputVariable": "avaliacao", "LocalTimeZoneEnabled": true}, "conditions": []}, {"type": "TrackEvent", "$title": "Registrar Ava<PERSON>ção", "$invalid": false, "settings": {"extras": {}, "category": "Score", "action": "{{avaliacao}}"}, "conditions": []}, {"type": "TrackEvent", "$title": "Registrar Ava<PERSON> por Atendente", "$invalid": false, "settings": {"extras": {}, "category": "ScoreAgent", "action": "{{emailAtendente}} | {{avaliacao}}"}, "conditions": [{"source": "context", "comparison": "endsWith", "variable": "atendente", "values": ["@blip.ai"]}]}], "$inputSuggestions": [], "$defaultOutput": {"$invalid": false, "typeOfStateId": "state", "stateId": "fallback"}, "id": "2b9bfb62-6346-4762-99ab-733d0c00d1a3", "root": false, "$title": "4.0 - Solicitar Avaliação de Atendimento", "$position": {"top": "220px", "left": "1131px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "143e0124-ec4e-462d-8369-efc61791afe6": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "95011b31-5279-4d98-968d-b5af3c6d6b26", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "95011b31-5279-4d98-968d-b5af3c6d6b26", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "2d2d011a-e3d5-4807-a347-d6cd9b3f43c8", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não temos atendentes disponíveis no momento. Por favor, tente novamente mais tarde.", "metadata": {}}, "$cardContent": {"document": {"id": "2d2d011a-e3d5-4807-a347-d6cd9b3f43c8", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não temos atendentes disponíveis no momento. Por favor, tente novamente mais tarde."}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "76137ac2-42ab-4ee9-aef7-743cb9160a98", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Solicitação sem Atendente", "$invalid": false, "settings": {"extras": {}, "category": "Solicitação", "action": "<PERSON><PERSON>"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "id": "143e0124-ec4e-462d-8369-efc61791afe6", "root": false, "$title": "3.2 - <PERSON><PERSON><PERSON>oní<PERSON>", "$position": {"top": "544px", "left": "792px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "4c0d9be0-bd3d-4e72-b105-a2ac741e5149": {"$contentActions": [{"input": {"bypass": true, "$cardContent": {"document": {"id": "86667929-c0a7-473d-b066-fbed10260d73", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}, {"action": {"$id": "be910120-abed-4e32-b75f-8dd2e2155120", "$typeOfContent": "chat-state", "type": "SendMessage", "settings": {"id": "f008a81f-658e-4f04-a0c4-3bc29e50fcec", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}, "metadata": {"#stateName": "{{state.name}}", "#stateId": "{{state.id}}", "#messageId": "{{input.message@id}}", "#previousStateId": "{{state.previous.id}}", "#previousStateName": "{{state.previous.name}}"}}, "$cardContent": {"document": {"id": "f008a81f-658e-4f04-a0c4-3bc29e50fcec", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "04835382-935c-4372-8778-69b4b0912d60", "type": "text/plain", "content": "Iremos buscar um de nossos atendentes para você!\n\nCaso prefira encerrar o atendimento, digite <b>#sair</b>.", "metadata": {"#stateName": "{{state.name}}", "#stateId": "{{state.id}}", "#messageId": "{{input.message@id}}", "#previousStateId": "{{state.previous.id}}", "#previousStateName": "{{state.previous.name}}"}}, "$cardContent": {"document": {"id": "04835382-935c-4372-8778-69b4b0912d60", "type": "text/plain", "content": "Iremos buscar um de nossos atendentes para você!\n\nCaso prefira encerrar o atendimento, digite <b>#sair</b>."}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}], "$conditionOutputs": [{"stateId": "desk:7ded1bfa-c9d8-4416-86e9-3f37ffd842e9", "typeOfStateId": "state", "$connId": "con_23", "$id": "8bbeeb5f-c08d-452b-b02d-44d30e46b2e2", "conditions": [{"source": "input", "comparison": "exists", "values": []}], "$invalid": false}], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"$invalid": false, "typeOfStateId": "state", "stateId": "fallback"}, "id": "4c0d9be0-bd3d-4e72-b105-a2ac741e5149", "root": false, "$title": "2.0 - Direcionar para Atendimento", "$position": {"top": "299px", "left": "669px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "97c535d6-09f8-46ee-9529-3d86e973bc66": {"$contentActions": [{"action": {"$id": "cebead6b-24f3-431d-8add-9916c72b0841", "$typeOfContent": "chat-state", "type": "SendMessage", "settings": {"id": "08ed321b-40f9-49fe-8c45-3af785a283ea", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "08ed321b-40f9-49fe-8c45-3af785a283ea", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"$id": "9c2f283b-16ee-4809-9880-16e84a0ee1a4", "$typeOfContent": "text", "type": "SendMessage", "settings": {"id": "a7f39eb8-3365-49d8-b625-e7530df3f7ca", "type": "text/plain", "content": "Agradecemos pelo contato!\n\nSe quiser falar com a gente novamente, reinicie a conversa! 😊", "metadata": {}}, "$cardContent": {"document": {"id": "a7f39eb8-3365-49d8-b625-e7530df3f7ca", "type": "text/plain", "content": "Agradecemos pelo contato!\n\nSe quiser falar com a gente novamente, reinicie a conversa! 😊"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "04a71e79-862d-4992-8778-84a2cdd120cf", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right", "editing": false}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "$tags": [], "id": "97c535d6-09f8-46ee-9529-3d86e973bc66", "root": false, "$title": "5.0 - Agradecimento Encerrado pelo Atendente", "$position": {"top": "220px", "left": "1360px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "9eeec57e-6f72-4959-a76d-baf822859307": {"$contentActions": [{"action": {"$id": "429d221d-815c-44ec-9c2a-c974c16b4317", "$typeOfContent": "chat-state", "type": "SendMessage", "settings": {"id": "42b7e5b2-a92c-4bba-ae59-a246db1d2e39", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}, "metadata": {"#stateName": "{{state.name}}", "#stateId": "{{state.id}}", "#messageId": "{{input.message@id}}", "#previousStateId": "{{state.previous.id}}", "#previousStateName": "{{state.previous.name}}"}}, "$cardContent": {"document": {"id": "42b7e5b2-a92c-4bba-ae59-a246db1d2e39", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:950"}, {"action": {"$id": "8a51f2af-bd93-428b-b68b-db91ed2eb470", "$typeOfContent": "text", "type": "SendMessage", "settings": {"id": "cc627296-589e-4bb7-9bd9-aa9162615fe9", "type": "text/plain", "content": "Seu atendimento foi finalizado.\n\nSe precisar de mais alguma coisa, inicie a conversa novamente! Obrigado!🤝", "metadata": {"#stateName": "{{state.name}}", "#stateId": "{{state.id}}", "#messageId": "{{input.message@id}}", "#previousStateId": "{{state.previous.id}}", "#previousStateName": "{{state.previous.name}}"}}, "$cardContent": {"document": {"id": "cc627296-589e-4bb7-9bd9-aa9162615fe9", "type": "text/plain", "content": "Seu atendimento foi finalizado.\n\nSe precisar de mais alguma coisa, inicie a conversa novamente! Obrigado!🤝"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:951"}], "$conditionOutputs": [], "$enteringCustomActions": [{"$id": "c832600a-0324-4b5c-964f-aead9a6b6efb", "$typeOfContent": "", "type": "TrackEvent", "$title": "Registrar Atendimento Realizado", "$invalid": false, "settings": {"extras": {}, "category": "AtendimentosRealizados", "action": "AtendimentosRealizados"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "$tags": [], "id": "9eeec57e-6f72-4959-a76d-baf822859307", "root": false, "$title": "5.1 - Agradecimento Encerrado pelo Cliente", "$position": {"top": "380px", "left": "1359px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "desk:7ded1bfa-c9d8-4416-86e9-3f37ffd842e9": {"$contentActions": [{"input": {"bypass": false, "conditions": [{"source": "context", "variable": "desk_forwardToDeskState_status", "comparison": "equals", "values": ["Success"]}], "$cardContent": {"document": {"id": "aceaf2fb-f78f-4e19-a1f0-89629fc33715", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": false, "position": "right", "editing": false}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"$isDeskOutput": true, "$isDeskCustomOutput": true, "conditions": [{"source": "context", "variable": "desk_forwardToDeskState_status", "comparison": "equals", "values": ["OutOfAttendanceHour"]}], "$invalid": false, "stateId": "3c555d39-68eb-466b-9063-6adeb01132fc", "$connId": "con_28", "$id": "ea265d85-e66c-46fa-b5ec-8ef3bb6eab95"}, {"$isDeskOutput": true, "$isDeskCustomOutput": true, "conditions": [{"source": "context", "variable": "desk_forwardToDeskState_status", "comparison": "equals", "values": ["NoAgentAvailable"]}], "$invalid": false, "stateId": "143e0124-ec4e-462d-8369-efc61791afe6", "$connId": "con_33", "$id": "d1bf5939-f6e2-4550-b4e4-6879a555fc29"}, {"$isDeskOutput": true, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedAttendant"]}], "$invalid": false, "stateId": "2b9bfb62-6346-4762-99ab-733d0c00d1a3", "$connId": "con_38", "$id": "fe46b01b-bba0-4a7a-baab-8069fb7728d4"}, {"$isDeskOutput": true, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedClient"]}], "$invalid": false, "stateId": "9eeec57e-6f72-4959-a76d-baf822859307", "$connId": "con_43", "$id": "b743ff96-f7c9-4293-961f-4407c921a1d6"}, {"$isDeskOutput": true, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedClientInactivity"]}], "$invalid": false, "stateId": "9eeec57e-6f72-4959-a76d-baf822859307", "$connId": "con_48", "$id": "0fd12f62-55ea-4137-a432-2ab6292fb505"}, {"$isDeskOutput": true, "$isDeskDefaultOutput": true, "conditions": [{"source": "context", "variable": "desk_forwardToDeskState_status", "comparison": "equals", "values": ["Error"]}], "stateId": "fallback", "$invalid": false, "typeOfStateId": "state"}, {"stateId": "9eeec57e-6f72-4959-a76d-baf822859307", "typeOfStateId": "state", "$connId": "con_53", "$id": "a10596ac-f547-4710-8cc3-164d6df2ae88", "conditions": [{"source": "input", "comparison": "equals", "values": ["#sair"]}], "$invalid": false}], "$enteringCustomActions": [{"$id": "7d978620-3a60-4c21-81c5-2549e3e15323", "$typeOfContent": "", "type": "ForwardToDesk", "conditions": [], "settings": {}, "$invalid": false}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "desk:7ded1bfa-c9d8-4416-86e9-3f37ffd842e9", "$invalid": false}, "$tags": [], "id": "desk:7ded1bfa-c9d8-4416-86e9-3f37ffd842e9", "deskStateVersion": "3.0.0", "root": false, "$title": "3.0 - Atendimento humano", "$afterStateChangedActions": [{"$id": "1806e697-90f4-4ede-b297-709e7068aa44", "$typeOfContent": "", "type": "LeavingFromDesk", "conditions": [], "settings": {}}], "$position": {"top": "299px", "left": "898px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}}, "globalActions": {"$contentActions": [], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "$tags": [], "id": "global-actions", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}}}, {"type": "PublishHelpdesks", "arguments": ["Lime"]}, {"type": "AddAgent", "arguments": ["owner"]}, {"type": "AddReport", "arguments": [{"name": "Atendimentos", "charts": [{"name": "Total de Solicitações", "dimension": "events", "id": "8113081b-b606-48c1-bae0-6cb07d72c0ce", "category": "AtendimentosTota<PERSON>", "chartType": "counter", "order": 0}, {"name": "Total de Solicitações Atendidas", "dimension": "events", "id": "73de4cb4-23cf-4c44-ad49-1f5dcd83e41a", "category": "AtendimentosRealizados", "chartType": "counter", "order": 1}, {"name": "Total de Solicitações Não Atendidas", "dimension": "events", "id": "7c852e26-930c-44d6-8457-7e23b1cb817c", "category": "Solicitação", "chartType": "counter", "order": 2}, {"name": "Causa das Solicitações Não Atendidas", "dimension": "events", "id": "4e266e50-2511-4cc5-9747-bdc347956ab6", "category": "Solicitação", "chartType": "pie", "order": 3}]}, {"name": "Qualidade dos Atendimentos", "charts": [{"name": "Avaliações", "dimension": "events", "id": "235e9b3c-72e4-48c8-8b90-e0f26b2b5a12", "category": "Score", "chartType": "list", "order": 0}]}, {"name": "Qualidade dos Atendimentos por Atendente", "charts": [{"name": "Avaliações", "dimension": "events", "id": "c66ae8f8-219b-4236-bac3-dc36ff3cb197", "category": "ScoreAgent", "chartType": "list", "order": 0}]}]}]}