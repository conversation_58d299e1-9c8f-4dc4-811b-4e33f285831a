{"id": "blip_deskCustomerService", "steps": [{"type": "SendCommand", "arguments": {}}, {"type": "PublishFlow", "arguments": {"settings": {"builder:minimumIntentScore": "0.5", "dateTimeOffset": "-3", "startDate": "08:30", "endDate": "18:50", "workDays": "0,1,2,3,4,5,6"}, "flow": {"onboarding": {"$contentActions": [{"input": {"bypass": false, "$cardContent": {"document": {"id": "a5f855b2-7344-44ad-bf8b-75bccd63d288", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": false, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "welcome", "conditions": [{"source": "input", "comparison": "matches", "values": [".*"]}], "$connId": "con_3", "$invalid": false, "$id": "b4fbe0db-50a9-48f6-86d9-129e059650b1"}], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "onboarding", "root": true, "$position": {"top": "106px", "left": "344px"}, "$title": "Início", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "fallback": {"$contentActions": [{"input": {"bypass": true, "$cardContent": {"document": {"id": "95b10ba8-e351-4d16-9b10-ce14961cb1fd", "type": "text/plain"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "error", "conditions": [{"source": "input", "comparison": "matches", "values": [".*"]}], "$connId": "con_8", "$invalid": false, "$id": "813a02d5-663b-433a-b0b6-36ea3ce2b858"}], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false}, "id": "fallback", "$position": {"top": "587px", "left": "358px"}, "$title": "Exceções", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "welcome": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000000", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000000", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000001", "type": "text/plain", "content": "Olá! É um prazer falar com você 😊", "metadata": {}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000001", "type": "text/plain", "content": "Olá! É um prazer falar com você 😊"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "cf8c9892-92a7-4a39-b169-9b8f5f7ef7a4", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "270fb951-7f0c-43fa-be0d-87c2d45a9edf", "typeOfStateId": "state", "$connId": "con_13", "$id": "0699baf9-6d1d-4fed-896d-e7621bc95827", "conditions": [{"source": "input", "comparison": "exists", "values": []}], "$invalid": false}], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Atendimentos <PERSON>", "$invalid": false, "settings": {"extras": {}, "category": "AtendimentosTota<PERSON>", "action": "AtendimentosTota<PERSON>"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "welcome", "$position": {"top": "240px", "left": "344px"}, "$title": "1.0 - <PERSON><PERSON> vindas", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "error": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000002", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000002", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "00000000-0000-0000-0000-000000000003", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não entendi sua mensagem!", "metadata": {}}, "$cardContent": {"document": {"id": "00000000-0000-0000-0000-000000000003", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não entendi sua mensagem!"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "a1f34699-dd5c-43b6-8345-fde58dc32c0d", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false}, "id": "error", "$position": {"top": "587px", "left": "592px"}, "$title": "0.0 - <PERSON><PERSON>", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "95118c04-1f32-40df-b1b0-54fb24fa2fc7": {"$contentActions": [{"input": {"bypass": true, "$cardContent": {"document": {"id": "425aa5d2-fcf9-4094-a52c-73917f47875c", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false, "$$hashKey": "object:34957"}], "$conditionOutputs": [{"stateId": "4c0d9be0-bd3d-4e72-b105-a2ac741e5149", "$connId": "con_18", "conditions": [{"source": "context", "comparison": "equals", "values": ["true"], "variable": "atendenteDisponivel", "$$hashKey": "object:34980"}], "$invalid": false, "$id": "3134a746-5d21-45e2-a564-0323a4b4b538", "$$hashKey": "object:34972"}, {"stateId": "143e0124-ec4e-462d-8369-efc61791afe6", "$connId": "con_23", "conditions": [{"source": "context", "comparison": "equals", "values": ["false"], "variable": "atendenteDisponivel", "$$hashKey": "object:34992"}], "$invalid": false, "$id": "5dc97100-50a9-434f-a574-7107b153ce94", "$$hashKey": "object:34973"}], "$enteringCustomActions": [{"type": "ProcessCommand", "$title": "Buscar Atendentes", "$invalid": false, "settings": {"from": "{{application.identity}}", "to": "<EMAIL>", "uri": "/attendants", "variable": "atendentes", "method": "get"}, "conditions": [], "$id": "99b1cd26-a99a-4edb-9b9d-987a91d9ed59"}, {"type": "ExecuteScript", "$title": "Validar Se Há Atendente Disponível", "$invalid": false, "settings": {"function": "run", "source": "// Receive the variables as parameters\nfunction run(result) {\n\n    result = JSON.parse(result);\n    let hasAttendant = false;\n    \n    if(result.resource && result.resource.total >= 1){\n            hasAttendant = result.resource.items.some(function(attendant){\n                return attendant.status === 'Online';\n            });\n    }\n\n    return hasAttendant;\n}", "inputVariables": ["atendentes"], "outputVariable": "atendenteDisponivel", "LocalTimeZoneEnabled": false}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "95118c04-1f32-40df-b1b0-54fb24fa2fc7", "root": false, "$title": "3.0 - Verificar Disponibilidade de Atendentes", "$position": {"top": "217px", "left": "845px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "3c555d39-68eb-466b-9063-6adeb01132fc": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "aa0d29d3-bab1-427d-9188-93b3638acbcd", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "aa0d29d3-bab1-427d-9188-93b3638acbcd", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "504721a0-3d44-4e95-82cc-dd33718b906a", "type": "text/plain", "content": "Infelizmente estamos fora do nosso horário de atendimento.", "metadata": {}}, "$cardContent": {"document": {"id": "504721a0-3d44-4e95-82cc-dd33718b906a", "type": "text/plain", "content": "Infelizmente estamos fora do nosso horário de atendimento."}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "fd1c385b-7adc-4efb-b829-3b137d4e97c0", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Solicitação Fora do Horário", "$invalid": false, "settings": {"extras": {}, "category": "Solicitação", "action": "Fora Horario Atendimento"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "id": "3c555d39-68eb-466b-9063-6adeb01132fc", "root": false, "$title": "2.1 - Fora do Horário de Atendimento", "$position": {"top": "399px", "left": "592px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "2b9bfb62-6346-4762-99ab-733d0c00d1a3": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "b950d0ee-cd9b-4aec-949b-02b8242cbeea", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "b950d0ee-cd9b-4aec-949b-02b8242cbeea", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34741"}, {"action": {"type": "SendMessage", "settings": {"id": "90ec55b8-aadd-4f92-bf1a-dec84eb60244", "type": "text/plain", "content": "Atendimento finalizado!"}, "$cardContent": {"document": {"id": "90ec55b8-aadd-4f92-bf1a-dec84eb60244", "type": "text/plain", "content": "Atendimento finalizado!"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34742"}, {"action": {"type": "SendMessage", "settings": {"id": "8b33aca6-bda4-4dec-8a22-5b5f51a2ec53", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "8b33aca6-bda4-4dec-8a22-5b5f51a2ec53", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34743"}, {"action": {"type": "SendMessage", "settings": {"id": "6bfaa60e-ce61-43fb-978e-80b1c5a15d8d", "type": "text/plain", "content": "De 0 a 10, qual nota você dá para nosso atendimento?", "metadata": {}}, "$cardContent": {"document": {"id": "6bfaa60e-ce61-43fb-978e-80b1c5a15d8d", "type": "text/plain", "content": "De 0 a 10, qual nota você dá para nosso atendimento?"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34744"}, {"input": {"bypass": false, "$cardContent": {"document": {"id": "64b7d83d-e2f4-4df4-8ad4-c9489c0f15f9", "type": "text/plain", "textContent": "Entrada do usuário", "content": "avaliacao"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false, "variable": "avaliacao"}, "$invalid": false, "$$hashKey": "object:34745"}], "$conditionOutputs": [{"stateId": "97c535d6-09f8-46ee-9529-3d86e973bc66", "typeOfStateId": "state", "$connId": "con_28", "$id": "7340cfea-930a-4770-912f-00701767064f", "conditions": [{"source": "input", "comparison": "exists", "values": [], "$$hashKey": "object:34775"}], "$invalid": false, "$$hashKey": "object:34769"}], "$enteringCustomActions": [], "$leavingCustomActions": [{"type": "ExecuteScript", "$title": "Padronizar Avaliação de 1 a 10", "$invalid": false, "settings": {"function": "run", "source": "function run(score) {\n    if(isNumeric(score)){\n       score = parseInt(score);\n       if(score < 0) return 0;\n       if(score > 10) return 10;\n       return score;\n    }\n    return \"Outro\";\n}\n\n// check if a string has a number\nfunction isNumeric(num){\n  return !isNaN(num)\n}", "inputVariables": ["avaliacao"], "outputVariable": "avaliacao"}, "conditions": []}, {"type": "TrackEvent", "$title": "Registrar <PERSON><PERSON>", "$invalid": false, "settings": {"extras": {}, "category": "Score", "action": "{{avaliacao}}"}, "conditions": []}], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false}, "id": "2b9bfb62-6346-4762-99ab-733d0c00d1a3", "root": false, "$title": "5.0 - Solicitar Avaliação de Atendimento", "$position": {"top": "218px", "left": "1596px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "143e0124-ec4e-462d-8369-efc61791afe6": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "95011b31-5279-4d98-968d-b5af3c6d6b26", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "95011b31-5279-4d98-968d-b5af3c6d6b26", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"action": {"type": "SendMessage", "settings": {"id": "2d2d011a-e3d5-4807-a347-d6cd9b3f43c8", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não temos atendentes disponíveis no momento. Por favor, tente novamente mais tarde.", "metadata": {}}, "$cardContent": {"document": {"id": "2d2d011a-e3d5-4807-a347-d6cd9b3f43c8", "type": "text/plain", "content": "<PERSON><PERSON><PERSON><PERSON>, não temos atendentes disponíveis no momento. Por favor, tente novamente mais tarde."}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "76137ac2-42ab-4ee9-aef7-743cb9160a98", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Atendimento Sem Atendente", "$invalid": false, "settings": {"extras": {}, "category": "Atendentes Indisponíveis", "action": "Atendentes Indisponíveis"}, "conditions": []}, {"type": "TrackEvent", "$title": "Registrar Solicitação sem Atendente", "$invalid": false, "settings": {"extras": {}, "category": "Solicitação", "action": "<PERSON><PERSON>"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "id": "143e0124-ec4e-462d-8369-efc61791afe6", "root": false, "$title": "3.2 - <PERSON><PERSON><PERSON>oní<PERSON>", "$position": {"top": "397px", "left": "845px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "4c0d9be0-bd3d-4e72-b105-a2ac741e5149": {"$contentActions": [{"action": {"type": "SendMessage", "settings": {"id": "adaea5c6-03e0-4ba5-9ef6-e1e3f9e9067e", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "adaea5c6-03e0-4ba5-9ef6-e1e3f9e9067e", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34531"}, {"action": {"type": "SendMessage", "settings": {"id": "04835382-935c-4372-8778-69b4b0912d60", "type": "text/plain", "content": "Um de nossos atendentes irá conversar com você em breve!\n\nCaso prefira encerrar o atendimento, digite <b>#sair</b>.", "metadata": {}}, "$cardContent": {"document": {"id": "04835382-935c-4372-8778-69b4b0912d60", "type": "text/plain", "content": "Um de nossos atendentes irá conversar com você em breve!\n\nCaso prefira encerrar o atendimento, digite <b>#sair</b>."}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34532"}, {"action": {"type": "SendMessage", "settings": {"id": "96272fb2-b301-4611-921b-69aa74791395", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "96272fb2-b301-4611-921b-69aa74791395", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34533"}, {"action": {"type": "SendMessage", "settings": {"id": "8f63c111-4933-4e07-a9be-574ca9d0fbda", "type": "text/plain", "content": "Como podemos ajudá-lo?"}, "$cardContent": {"document": {"id": "8f63c111-4933-4e07-a9be-574ca9d0fbda", "type": "text/plain", "content": "Como podemos ajudá-lo?"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:34534"}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "86667929-c0a7-473d-b066-fbed10260d73", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false, "$$hashKey": "object:34535"}], "$conditionOutputs": [{"stateId": "desk:f2f21e06-6daa-4e6d-8423-54f699f6890c", "typeOfStateId": "state", "$connId": "con_33", "$id": "6f823455-3856-41fa-a1ee-baddd9f941f2", "conditions": [{"source": "input", "comparison": "exists", "values": [], "$$hashKey": "object:34564"}], "$invalid": false, "$$hashKey": "object:34558"}], "$enteringCustomActions": [{"type": "TrackEvent", "$title": "Registrar Atendimento Realizado", "$invalid": false, "settings": {"extras": {}, "category": "AtendimentosRealizados", "action": "AtendimentosRealizados"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "4c0d9be0-bd3d-4e72-b105-a2ac741e5149", "root": false, "$title": "3.1 - <PERSON><PERSON><PERSON>oní<PERSON>", "$position": {"top": "229px", "left": "1095px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "desk:f2f21e06-6daa-4e6d-8423-54f699f6890c": {"$contentActions": [{"input": {"bypass": false, "conditions": [{"source": "context", "variable": "helpDeskHasTicket", "comparison": "equals", "values": ["true"]}], "$cardContent": {"document": {"id": "c7a83716-91b5-4c1f-8091-646c2512939b", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right", "editing": false}, "$invalid": false}, "$invalid": false, "$$hashKey": "object:35414"}], "$conditionOutputs": [{"$isDeskOutput": true, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedAttendant"]}], "$invalid": false, "stateId": "2b9bfb62-6346-4762-99ab-733d0c00d1a3", "$connId": "con_38", "$id": "16d670e1-58c0-4002-850a-ff8e6ad1dffd", "$$hashKey": "object:35427"}, {"$isDeskOutput": true, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedClient"]}], "$invalid": false, "stateId": "2b9bfb62-6346-4762-99ab-733d0c00d1a3", "$connId": "con_73", "$id": "0e1371cc-ce54-465c-ac06-d96f2cf330b8", "$$hashKey": "object:35428"}, {"stateId": "bdbb5e3b-c316-42f6-8f12-11a73341b054", "typeOfStateId": "state", "$connId": "con_48", "$id": "beaff2c0-e84c-4e16-9d65-1ddd7ba71c6d", "conditions": [{"source": "input", "comparison": "equals", "values": ["#sair"], "$$hashKey": "object:35449"}], "$invalid": false, "$$hashKey": "object:35429"}, {"$isDeskOutput": true, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedClientInactivity"]}], "$invalid": false, "stateId": "2b9bfb62-6346-4762-99ab-733d0c00d1a3", "$connId": "con_78", "$id": "872148b3-615b-4ed1-8be7-a3e27de84143", "$$hashKey": "object:35430"}], "$enteringCustomActions": [{"$id": "41039876-6360-4894-bbc0-94d15602c8a3", "$typeOfContent": "", "type": "ExecuteScript", "$invalid": false, "conditions": [{"source": "context", "variable": "state.previous.id", "comparison": "notEquals", "values": ["desk:f2f21e06-6daa-4e6d-8423-54f699f6890c"]}, {"source": "context", "variable": "tunnel.identity", "comparison": "exists", "values": []}], "settings": {"function": "run", "source": "function run(contact, tunnel_identity) {\n    contact = JSON.parse(contact);\n    contact.identity = tunnel_identity;\n    return contact;\n}", "inputVariables": ["contact.serialized", "tunnel.identity"], "outputVariable": "deskUpdateTunnelContactBody", "LocalTimeZoneEnabled": false}}, {"$id": "e9f2def3-1b28-4bff-92eb-debc79e8ce97", "$typeOfContent": "", "type": "ProcessCommand", "$invalid": false, "conditions": [{"source": "context", "variable": "state.previous.id", "comparison": "notEquals", "values": ["desk:f2f21e06-6daa-4e6d-8423-54f699f6890c"]}, {"source": "context", "variable": "tunnel.identity", "comparison": "exists", "values": []}], "settings": {"to": "<EMAIL>", "from": "{{application.identity}}", "method": "merge", "uri": "/contacts", "type": "application/vnd.lime.contact+json", "resource": "{{deskUpdateTunnelContactBody}}", "variable": "mergeRouterContactWithTunnelCommandResponse"}}, {"$id": "642c0b75-dc35-4da0-b3c5-ca5c65755182", "$typeOfContent": "", "type": "ExecuteScript", "$title": "GetEncodedContactIdentity", "$invalid": false, "conditions": [{"source": "context", "variable": "state.previous.id", "comparison": "notEquals", "values": ["desk:f2f21e06-6daa-4e6d-8423-54f699f6890c"]}], "settings": {"function": "run", "source": "function run(identity) {\n return (identity !== undefined) ? encodeURIComponent(identity) : identity;\n}", "inputVariables": ["input.message.fromidentity"], "outputVariable": "helpDeskEncodedFromIdentity", "LocalTimeZoneEnabled": false}}, {"$id": "e2c92210-c6bd-4635-b0e2-a1ba9c853f7c", "$typeOfContent": "", "type": "ProcessCommand", "$title": "GetOpenTicket", "$invalid": false, "conditions": [{"source": "context", "variable": "state.previous.id", "comparison": "notEquals", "values": ["desk:f2f21e06-6daa-4e6d-8423-54f699f6890c"]}], "settings": {"from": "{{application.identifier}}@{{application.domain}}", "to": "<EMAIL>", "method": "get", "uri": "/tickets?$filter=customerIdentity%20eq%20%27{{helpDeskEncodedFromIdentity}}%27%20and%20%28status%20eq%20%27Open%27%20or%20status%20eq%20%27Waiting%27%20or%20status%20eq%20%27Assigned%27%29", "metadata": {"server.shouldStore": true}, "variable": "helpDeskGetOpenTicketCommandResponse"}}, {"$id": "287449a7-73c2-4c13-b2a3-8dd07cc1cf8b", "$typeOfContent": "", "type": "ExecuteScript", "$title": "TreatGetOpenTicketResponse", "$invalid": false, "conditions": [{"source": "context", "variable": "state.previous.id", "comparison": "notEquals", "values": ["desk:f2f21e06-6daa-4e6d-8423-54f699f6890c"]}], "settings": {"function": "run", "source": "function run(commandResponse) {\n    var response = JSON.parse(commandResponse)\n    var ticketExists = (response && response.resource && response.resource.items && response.resource.items.length > 0);\n  return ticketExists ? \"true\" : \"false\";\n}", "inputVariables": ["helpDeskGetOpenTicketCommandResponse"], "outputVariable": "helpDeskHasTicket", "LocalTimeZoneEnabled": false}}, {"$id": "cc877178-a4fc-4fae-b312-ff053723e057", "$typeOfContent": "", "type": "ProcessCommand", "$title": "OpenTicketIfNotFound", "$invalid": false, "conditions": [{"source": "context", "variable": "helpDeskHasTicket", "comparison": "notEquals", "values": ["true"]}], "settings": {"from": "{{application.identifier}}@{{application.domain}}", "to": "<EMAIL>", "method": "set", "uri": "/tickets/{{helpDeskEncodedFromIdentity}}", "variable": "helpDeskOpenTicketCommandResponse", "type": "text/plain", "resource": "{{input.content}}", "metadata": {"server.shouldStore": true}}}, {"$id": "44b5420b-0d8f-4c4c-aa5f-1b5a49a70952", "$typeOfContent": "", "type": "ExecuteScript", "$title": "TreatOpenTicketResponse", "$invalid": false, "conditions": [{"source": "context", "variable": "helpDeskHasTicket", "comparison": "notEquals", "values": ["true"]}], "settings": {"function": "run", "source": "function run(commandResponse) {\n    var response = commandResponse ? JSON.parse(commandResponse) : {}\n    var ticketExists = response && response.type === \"application/vnd.iris.ticket+json\" && response.resource \n    return ticketExists ? \"true\" : \"false\";\n}", "inputVariables": ["helpDeskOpenTicketCommandResponse"], "outputVariable": "helpDeskHasTicket", "LocalTimeZoneEnabled": false}}, {"$id": "e14d0b2b-5c83-4eda-847a-52f7d315878b", "$typeOfContent": "", "type": "ForwardMessageToDesk", "conditions": [{"source": "context", "variable": "helpDeskHasTicket", "comparison": "equals", "values": ["true"]}], "settings": {}, "$invalid": false}], "$leavingCustomActions": [{"$id": "98f96531-8985-4ccb-80de-d82ec80743e0", "$typeOfContent": "", "type": "SetVariable", "$title": "ResetHasTicket", "$invalid": false, "conditions": [{"source": "context", "variable": "input.type", "comparison": "equals", "values": ["application/vnd.iris.ticket+json"]}, {"source": "context", "variable": "input.content@status", "comparison": "equals", "values": ["ClosedAttendant", "ClosedClient", "ClosedClientInactivity"]}], "settings": {"variable": "helpDeskHasTicket", "value": "false", "$invalid": false}}], "$inputSuggestions": [], "$defaultOutput": {"stateId": "desk:f2f21e06-6daa-4e6d-8423-54f699f6890c", "$invalid": false}, "$tags": [], "id": "desk:f2f21e06-6daa-4e6d-8423-54f699f6890c", "deskStateVersion": "2.0.0", "root": false, "$title": "4.0 - Atendimento Humano", "$position": {"top": "229px", "left": "1346px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "270fb951-7f0c-43fa-be0d-87c2d45a9edf": {"$contentActions": [{"input": {"bypass": true, "$cardContent": {"document": {"id": "7fb24b66-f182-4a6b-82ec-17cd0d87cf16", "type": "text/plain", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right"}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "95118c04-1f32-40df-b1b0-54fb24fa2fc7", "typeOfStateId": "state", "$connId": "con_58", "$id": "d15c1fce-9b2f-4003-8702-c8a98def87ae", "conditions": [{"source": "context", "comparison": "equals", "values": ["true"], "variable": "horarioDisponivel"}], "$invalid": false}, {"stateId": "3c555d39-68eb-466b-9063-6adeb01132fc", "typeOfStateId": "state", "$connId": "con_63", "$id": "b1bfc2c9-4388-4423-921a-16b67c2ddda7", "conditions": [{"source": "context", "comparison": "equals", "values": ["false"], "variable": "horarioDisponivel"}], "$invalid": false}], "$enteringCustomActions": [{"$id": "ebc6cb64-ed73-44db-8fbb-768326e44be2", "$typeOfContent": "", "type": "ExecuteScript", "$title": "Buscar Horários e Dias de Atendimento", "$invalid": false, "settings": {"function": "run", "source": "// O horário de atendimento padrão é de segunda a sexta-feira de 08:00 as 18:00.\n// Caso seu horário de atendimento seja diferente, acesse o link https://customer-service-template-generator.netlify.app/\n// configure os horários de atendimento e clique em generate script.\n// Apague tudo abaixo e cole o novo script gerado pela ferramenta. Não esqueça de salvar e publicar o fluxo do seu chatbot. ;)\n\nfunction run() {\n    let workSchedule = [\n        {\n            \"num\": 1,\n            \"name\": \"Monday\",\n            \"portugueseName\": \"Segunda\",\n            \"workTime\": [\n                {\n                    \"start\": \"08:00\",\n                    \"end\": \"18:00\"\n                }\n            ]\n        },\n        {\n            \"num\": 2,\n            \"name\": \"Tuesday\",\n            \"portugueseName\": \"Terça\",\n            \"workTime\": [\n                {\n                    \"start\": \"08:00\",\n                    \"end\": \"18:00\"\n                }\n            ]\n        },\n        {\n            \"num\": 3,\n            \"name\": \"Wednesday\",\n            \"portugueseName\": \"Quarta\",\n            \"workTime\": [\n                {\n                    \"start\": \"08:00\",\n                    \"end\": \"18:00\"\n                }\n            ]\n        },\n        {\n            \"num\": 4,\n            \"name\": \"Thursday\",\n            \"portugueseName\": \"Quinta\",\n            \"workTime\": [\n                {\n                    \"start\": \"08:00\",\n                    \"end\": \"18:00\"\n                }\n            ]\n        },\n        {\n            \"num\": 5,\n            \"name\": \"Friday\",\n            \"portugueseName\": \"Sexta\",\n            \"workTime\": [\n                {\n                    \"start\": \"08:00\",\n                    \"end\": \"18:00\"\n                }\n            ]\n        }\n    ];\n    \n    return JSON.stringify(workSchedule);\n }", "inputVariables": [], "outputVariable": "agenda", "LocalTimeZoneEnabled": false}, "conditions": []}, {"type": "ExecuteScript", "$title": "Validar Horário de Atendimento", "$invalid": false, "settings": {"function": "run", "source": "// Receive the variables as parameters\nfunction run(offset, weekSchedule) {\noffset = parseInt(offset);\n\nweekSchedule = JSON.parse(weekSchedule);\n\nlet today = nowUTC(offset);\n\nif (isWorkDay(today, weekSchedule)) {\nlet todaySchedule = getTodaySchedule(weekSchedule, today);\nlet intervalTime = getIntervalTime(todaySchedule, today);\n\nreturn checkTime(intervalTime, today);\n}\n\nreturn false;\n}\n\nfunction getIntervalTime(todaySchedule, today) {\nlet intervalTime = [];\nfor (let i = 0; i < todaySchedule.workTime.length; i++) {\nintervalTime.push({\nstart: utcDate(todaySchedule.workTime[i].start, today),\nend: utcDate(todaySchedule.workTime[i].end, today)\n});\n}\n\nreturn intervalTime;\n}\n\nfunction checkTime(intervalTime, today) {\nfor (let i = 0; i < intervalTime.length; i++) {\nif (today - intervalTime[i].start > 0 && intervalTime[i].end - today > 0)\n\nreturn true;\n}\n\nreturn false;\n}\n\nfunction getTodaySchedule(weekSchedule, today) {\nfor (let i = 0; i < weekSchedule.length; i++) {\nif (weekSchedule[i].num == today.getUTCDay()) return weekSchedule[i];\n}\n}\n\n//Get now UTC Date\nfunction nowUTC(offset) {\nlet now = new Date();\nlet utc_timestamp = Date.UTC(\nnow.getUTCFullYear(),\nnow.getUTCMonth(),\nnow.getUTCDate(),\nnow.getUTCHours(),\nnow.getUTCMinutes(),\nnow.getUTCSeconds(),\nnow.getUTCMilliseconds()\n);\n\nreturn new Date(utc_timestamp + offset * 3600 * 1000);\n}\n\n//Get UTC Date\nfunction utcDate(timeString, today) {\n\nlet hour = getValueByString(\"hour\", timeString);\nlet minutes = getValueByString(\"minutes\", timeString);\n\nlet utc_timestamp = Date.UTC(\ntoday.getUTCFullYear(),\ntoday.getUTCMonth(),\ntoday.getUTCDate(),\nhour,\nminutes,\n0,\n0\n);\nreturn new Date(utc_timestamp);\n}\n\n//Get hour/minute by string with pattern HH:mm\nfunction getValueByString(type, timeString) {\nif (type === \"hour\") {\nreturn parseInt(timeString.substring(0, timeString.indexOf(\":\")));\n} else if (type === \"minutes\") {\nreturn parseInt(\ntimeString.substring(timeString.indexOf(\":\") + 1, timeString.length)\n);\n}\n\nreturn 0;\n}\n\n//Get if today is a work day\nfunction isWorkDay(today, workDays) {\nfor (let i = 0; i < workDays.length; i++) {\nif (workDays[i].num == today.getDay().toString()) return true;\n}\nreturn false;\n}\n", "inputVariables": ["config.dateTimeOffset", "agenda"], "outputVariable": "horarioDisponivel", "LocalTimeZoneEnabled": false}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "id": "270fb951-7f0c-43fa-be0d-87c2d45a9edf", "root": false, "$title": "2.0 - Verificar Horário de Atendimento", "$position": {"top": "229px", "left": "593px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false, "$tags": [], "invalidOutputs": true}, "97c535d6-09f8-46ee-9529-3d86e973bc66": {"$contentActions": [{"action": {"$id": "cebead6b-24f3-431d-8add-9916c72b0841", "$typeOfContent": "chat-state", "type": "SendMessage", "settings": {"id": "08ed321b-40f9-49fe-8c45-3af785a283ea", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "$cardContent": {"document": {"id": "08ed321b-40f9-49fe-8c45-3af785a283ea", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:35881"}, {"action": {"$id": "9c2f283b-16ee-4809-9880-16e84a0ee1a4", "$typeOfContent": "text", "type": "SendMessage", "settings": {"id": "a7f39eb8-3365-49d8-b625-e7530df3f7ca", "type": "text/plain", "content": "Agradecemos pelo contato!\n\nSe quiser falar com a gente novamente, reinicie a conversa! 😊", "metadata": {}}, "$cardContent": {"document": {"id": "a7f39eb8-3365-49d8-b625-e7530df3f7ca", "type": "text/plain", "content": "Agradecemos pelo contato!\n\nSe quiser falar com a gente novamente, reinicie a conversa! 😊"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:35882"}, {"input": {"bypass": true, "$cardContent": {"document": {"id": "04a71e79-862d-4992-8778-84a2cdd120cf", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right", "editing": false}, "$invalid": false}, "$invalid": false, "$$hashKey": "object:35883"}], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "$tags": [], "id": "97c535d6-09f8-46ee-9529-3d86e973bc66", "root": false, "$title": "6.0 - Agradecimento Encerrado pelo Atendente", "$position": {"top": "218px", "left": "1848px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "bdbb5e3b-c316-42f6-8f12-11a73341b054": {"$contentActions": [{"input": {"bypass": true, "$cardContent": {"document": {"id": "e45196c8-2e04-4db5-84de-e8bc90fef8d2", "type": "text/plain", "textContent": "Entrada do usuário", "content": "Entrada do usuário"}, "editable": false, "deletable": true, "position": "right", "editing": false}, "$invalid": false}, "$invalid": false}], "$conditionOutputs": [{"stateId": "9eeec57e-6f72-4959-a76d-baf822859307", "typeOfStateId": "state", "$connId": "con_68", "$id": "0b79d905-162a-4519-b7dd-633a84f923cd", "conditions": [{"source": "input", "comparison": "exists", "values": []}], "$invalid": false}], "$enteringCustomActions": [{"$id": "9eadee7a-c419-4ae3-a0e9-c9a084da37a8", "$typeOfContent": "", "type": "ProcessCommand", "$title": "Buscar Ticket no Desk", "$invalid": false, "settings": {"from": "{{application.identity}}", "to": "<EMAIL>", "method": "get", "uri": "/tickets?$take=1&customerIdentity={{contact.identity}}", "variable": "buscaTickets"}, "conditions": []}, {"$id": "1958f45a-f7e7-46a0-af61-a0b8f9e9ef33", "$typeOfContent": "", "type": "ExecuteScript", "$title": "Extrair ID da Resposta do Comando", "$invalid": false, "settings": {"function": "run", "source": "function run(response){\n\n    response = JSON.parse(response);\n    ticketID = response.resource.items[0].id;\n\n    return ticketID;\n}", "inputVariables": ["buscaTickets"], "outputVariable": "IDTicket", "LocalTimeZoneEnabled": false}, "conditions": []}, {"$id": "fc34ba2a-3c40-4c5a-8286-3e775ef8d8f7", "$typeOfContent": "", "type": "ProcessCommand", "$title": "<PERSON><PERSON><PERSON> Ticket <PERSON> Cliente", "$invalid": false, "settings": {"from": "{{application.identity}}", "to": "<EMAIL>", "method": "set", "resource": {"id": "{{IDTicket}}", "status": "ClosedClient"}, "type": "application/vnd.iris.ticket+json", "uri": "/tickets/change-status-without-redirect", "variable": "ignorar"}, "conditions": []}, {"$id": "d70b8206-2413-4429-a357-e223976951f5", "$typeOfContent": "", "type": "ProcessCommand", "$title": "Buscar Ticket Atualizado", "$invalid": false, "settings": {"from": "{{application.identity}}", "to": "<EMAIL>", "method": "get", "uri": "/ticket/{{IDTicket}}", "variable": "ignorar"}, "conditions": []}], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "$tags": [], "id": "bdbb5e3b-c316-42f6-8f12-11a73341b054", "root": false, "$title": "4.1 - <PERSON><PERSON><PERSON> Atendimento Cliente", "$position": {"top": "407px", "left": "1346px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}, "9eeec57e-6f72-4959-a76d-baf822859307": {"$contentActions": [{"action": {"$id": "429d221d-815c-44ec-9c2a-c974c16b4317", "$typeOfContent": "chat-state", "type": "SendMessage", "settings": {"id": "42b7e5b2-a92c-4bba-ae59-a246db1d2e39", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}, "metadata": {"#stateName": "{{state.name}}", "#stateId": "{{state.id}}", "#messageId": "{{input.message@id}}", "#previousStateId": "{{state.previous.id}}", "#previousStateName": "{{state.previous.name}}"}}, "$cardContent": {"document": {"id": "42b7e5b2-a92c-4bba-ae59-a246db1d2e39", "type": "application/vnd.lime.chatstate+json", "content": {"state": "composing", "interval": 1000}}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:35509"}, {"action": {"$id": "8a51f2af-bd93-428b-b68b-db91ed2eb470", "$typeOfContent": "text", "type": "SendMessage", "settings": {"id": "cc627296-589e-4bb7-9bd9-aa9162615fe9", "type": "text/plain", "content": "Seu atendimento foi finalizado.\n\nSe precisar de mais alguma coisa, inicie a conversa novamente! Obrigado!🤝", "metadata": {"#stateName": "{{state.name}}", "#stateId": "{{state.id}}", "#messageId": "{{input.message@id}}", "#previousStateId": "{{state.previous.id}}", "#previousStateName": "{{state.previous.name}}"}}, "$cardContent": {"document": {"id": "cc627296-589e-4bb7-9bd9-aa9162615fe9", "type": "text/plain", "content": "Seu atendimento foi finalizado.\n\nSe precisar de mais alguma coisa, inicie a conversa novamente! Obrigado!🤝"}, "editable": true, "deletable": true, "position": "left"}}, "$invalid": false, "$$hashKey": "object:35510"}], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "onboarding", "$invalid": false, "typeOfStateId": "state"}, "$tags": [], "id": "9eeec57e-6f72-4959-a76d-baf822859307", "root": false, "$title": "6.1 - Agradecimento Encerrado pelo Cliente", "$position": {"top": "395px", "left": "1593px"}, "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}}, "globalActions": {"$contentActions": [], "$conditionOutputs": [], "$enteringCustomActions": [], "$leavingCustomActions": [], "$inputSuggestions": [], "$defaultOutput": {"stateId": "fallback", "$invalid": false}, "$tags": [], "id": "global-actions", "$invalidContentActions": false, "$invalidOutputs": false, "$invalidCustomActions": false, "$invalid": false}}}, {"type": "PublishHelpdesks", "arguments": ["Lime"]}, {"type": "AddAgent", "arguments": ["owner"]}, {"type": "AddReport", "arguments": [{"name": "Atendimentos", "charts": [{"name": "Total de Solicitações", "dimension": "events", "id": "8113081b-b606-48c1-bae0-6cb07d72c0ce", "category": "AtendimentosTota<PERSON>", "chartType": "counter", "order": 0}, {"name": "Total de Solicitações Atendidas", "dimension": "events", "id": "73de4cb4-23cf-4c44-ad49-1f5dcd83e41a", "category": "AtendimentosRealizados", "chartType": "counter", "order": 1}, {"name": "Total de Solicitações Não Atendidas", "dimension": "events", "id": "7c852e26-930c-44d6-8457-7e23b1cb817c", "category": "Solicitação", "chartType": "counter", "order": 2}, {"name": "Causa das Solicitações Não Atendidas", "dimension": "events", "id": "4e266e50-2511-4cc5-9747-bdc347956ab6", "category": "Solicitação", "chartType": "pie", "order": 3}]}, {"name": "Qualidade dos Atendimentos", "charts": [{"name": "Avaliações", "dimension": "events", "id": "235e9b3c-72e4-48c8-8b90-e0f26b2b5a12", "category": "Score", "chartType": "list", "order": 0}]}]}]}