import { I<PERSON><PERSON>roller, IDocumentService, IScope, translate } from 'angular';

import { IStateService } from 'angular-ui-router';
import { AccountService2, UserBlipAccount } from 'modules/account/AccountService2';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { TenantRoles, TenantService } from 'modules/application/tenant/TenantService';
import { ContextProcessorService, Contexts } from 'modules/core/ContextProcessorService';
import { BlipToastService } from 'modules/shared/BlipToastService';
import { ApplicationType, CreateApplicationError, CreateApplicationService, CreateApplicationSteps } from './CreateApplicationService';
import { LoadingService } from 'modules/ui/LoadingService';
import { homeName } from '../detail/home';
import { removeSpecialCharacter } from 'modules/shared/validators/ApplicationNameValidation';
import {
    APPLICATION_CREATION_REQUEST_QUOTE,
    APPLICATION_CREATION_ROUTER_HELP_CLICKED,
    APPLICATION_CREATION_TEMPLATE_SELECTED,
    CREATE_CHATBOT,
    CREATE_CHATBOT_BACK_PREVIOUS_STEP,
    CREATE_CHATBOT_CLOSE,
    CREATE_CHATBOT_FLOW_TEST
} from './CreateApplicationSegmentEvents';

import './CreateApplication.scss';

interface ICreateApplicationScope extends IScope {
    MarketplaceStepDescriptions: Record<string, string>;
    MarketplaceStepTitles: Record<string, string>;
}

export class CreateApplicationController implements IController {
    applicationForm: any;
    template: any;
    application: any;
    beforeNameStep: string;
    provideANameText: string;
    templateLang: string;
    canCreateChatbot: boolean;
    blipAccount: UserBlipAccount;
    isBuilderHasAccessed: Boolean;
    localStorage: Storage;
    isValidApplicationFormError: boolean = false;
    showFooter: boolean = true;
    logoCenter: boolean = true;
    constructor(
        private $scope: ICreateApplicationScope,
        private $state: IStateService,
        private $document: IDocumentService,
        private $translate: translate.ITranslateService,
        private $window: Window,
        private LoadingService: LoadingService,
        private BlipToastService: BlipToastService,
        ApplicationModel,
        private SegmentService: SegmentService,
        private ContextProcessorService: ContextProcessorService,
        private AccountService2: AccountService2,
        private BLIP_DOMAIN: string,
        private TenantService: TenantService,
        private CreateApplicationService: CreateApplicationService,
    ) {
        'ngInject';

        this.showFooter = this.$state.current.data.showFooter ?? true;
        this.logoCenter = this.$state.current.data.logoCenter ?? true;
        this.application = new ApplicationModel();
        this.template = $state.params.template;

        if (
            $state.current.name === 'auth.application.create.select' &&
            $state.params.template
        ) {
            this.selectTemplate($state.params.template);
        }

        if ($state.current.name === 'auth.application.create.name') {
            this.template = $state.params.template;
            this.templateLang = $state.params.templateLang;
            this.getProvideANameText(this.template);
            if (!this.template) {
                this.$state.go('auth.application.list');
            }
        }

        this.$scope.$watch(() => {
            return $state.current.name;
        }, (newVal, oldVal) => {
            if (newVal === 'auth.application.create.name') {
                this.beforeNameStep = newVal != oldVal ? oldVal : 'auth.application.create.marketplace';
            }
        });
    }

    async $onInit(): Promise<void> {
        this.LoadingService.startLoading(false);
        this.localStorage = window.localStorage;
        this.isBuilderHasAccessed = (this.localStorage.getItem('builder_has_accessed')) === 'true';
        this.canCreateChatbot = await this.checkIfCanCreateChatbot();

        if (!this.canCreateChatbot) {
            this.$state.go('auth.application.list');
        }

        this.LoadingService.stopLoading();

        this.$document.on('keydown', (event) => {
            if (event.keyCode == 27) {
                this.close();
            }
        });
    }

    $onDestroy(): void {
        this.$document.off('keydown');
    }

    close() {
        this.SegmentService.createOrganizationTrack(CREATE_CHATBOT_CLOSE);

        this.$onDestroy();
        this.$state.go('auth.application.list');
    }

    validateSpecialCharacter() {
        const withoutSpecialCharacter = removeSpecialCharacter(this.application.name);
        this.application.name = withoutSpecialCharacter;
     }

    async createApplication() {
        try {
            if (!this.validApplicationFormErrors()) {
                return;
            }

            this.LoadingService.startLoading(false);
            this.application = await this.CreateApplicationService.createApplication(this.application, this.template);
            this.SegmentService.createApplicationTrack({
                trackEvent: CREATE_CHATBOT,
                application: this.application,
                payload: {
                    template: this.application.template,
                    marketplaceId: this.application.marketplace.id,
                }
            });
            this.goToApplicationDetails();
        } catch (err) {
            const errorTitle = await this.$translate('createApplication.errorMsg.title');
            let errorMessage = await this.$translate('createApplication.errorMsg.1');

            const errStep = (err as CreateApplicationError).step;
            switch (errStep) {
                case CreateApplicationSteps.AttendanceActivation:
                    errorMessage = await this.$translate('createApplication.errorMsg.deskSettingsError');
                    break;
                case CreateApplicationSteps.DataValidation:
                    errorMessage = await this.$translate('createApplication.errorMsg.2');
                    break;
                default:
                    errorMessage = await this.$translate('createApplication.errorMsg.1');
                    break;
            }

            this.$state.go('^.name');
            this.BlipToastService.show('danger', {
                title: errorTitle,
                msg: errorMessage
            });
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async selectTemplate(template) {
        await this.sendTemplateSelectionToSegment(template);
        this.template = template;

        await this.getProvideANameText(template);
        if (template === 'blip_deskCustomerService') {
            this.$state.go('^.test', { ...template });
            return;
        }
        this.$state.go('^.name', { ...template });
    }

    validApplicationFormErrors() {
        for (const el in this.applicationForm.$$controls) {
            if (this.applicationForm.$$controls[el].errors) {
                return this.isValidApplicationFormError;
            }
        }
        return this.isValidApplicationFormError = true;
    }

    backFromNameStep() {
        this.SegmentService.createOrganizationTrack(CREATE_CHATBOT_BACK_PREVIOUS_STEP, {
            currentStep: 'name'
        });

        this.$state.go(this.beforeNameStep);
    }

    async openQuoteRequest() {
        this.SegmentService.createOrganizationTrack(APPLICATION_CREATION_REQUEST_QUOTE);

        const localizedUrl = await this.$translate('createApplication.needHelpUrl');
        this.$window.open(localizedUrl, '_blank');
    }

    async openRouterLearnMore() {
        this.SegmentService.createOrganizationTrack(APPLICATION_CREATION_ROUTER_HELP_CLICKED);

        const localizedUrl = await this.$translate('createApplication.router.learnMoreUrl');
        this.$window.open(localizedUrl, '_blank');
    }

    private async sendTemplateSelectionToSegment(template: string) {
        await this.SegmentService.createOrganizationTrack(
            APPLICATION_CREATION_TEMPLATE_SELECTED, {
            template,
        });
    }

    private async goToApplicationDetails(): Promise<void> {
        if (this.application.template !== ApplicationType.Master) {
            this.SegmentService.createTrack(CREATE_CHATBOT_FLOW_TEST, {
                template: this.application.template,
                botName: this.application.name,
                isBuilderHasAccessed: this.isBuilderHasAccessed
            });
        }

        this.$state.go(homeName, {
            shortName: this.application.shortName,
        });
    }

    private async getProvideANameText(template) {
        switch (template) {
            case 'blip_deskCustomerService':
                this.provideANameText = await this.$translate('createApplication.name.titleTemplate');
                break;
            case 'master':
                this.provideANameText = await this.$translate('createApplication.name.titleRouter');
                break;
            default:
                this.provideANameText = await this.$translate('createApplication.name.titleScratch');
                break;
        }
    }

    private async checkIfCanCreateChatbot() {
        await this.ContextProcessorService.waitForInitialization();

        if (this.ContextProcessorService.context === Contexts.Default) {
            return true;
        }

        this.blipAccount = await this.AccountService2.me();
        const tenantId = this.ContextProcessorService.tenant.id;
        const userIdentity = `${encodeURIComponent(this.blipAccount.email)}@${this.BLIP_DOMAIN}`;

        return this.TenantService.hasRequiredTenantRole(userIdentity, tenantId, [TenantRoles.Admin, TenantRoles.Member]);
    }
}
