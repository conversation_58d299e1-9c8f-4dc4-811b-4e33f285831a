{"createApplication": {"needHelp": "Need help developing your company's chatbot?", "requestAQuote": "Request a quote", "needHelpUrl": "https://www.take.net/en/contact-us/", "tagline": "Create chatbot", "taglineRouter": "Create router", "marketplace": {"title": "Choose how to create your chatbot", "template": {"title": "Use template", "description": "Build your chatbot from a template with preconfigured features and customer service, speeding up its development.", "tag": "Most fitting to start"}, "scratch": {"title": "Create from scratch", "description": "Build your chatbot from scratch and manage all settings manually. Recommended for those who already have experience with Blip."}}, "name": {"titleScratch": "Provide a name for your chatbot", "titleTemplate": "Provide a name for your preconfigured chatbot", "titleRouter": "Provide a name for your router", "name": "Chatbot name", "nameRouter": "Router name", "setImage": "Set image", "back": "Back"}, "templates": {"title": "Use template", "useTemplate": "Choose template", "back": "Back", "online": "Online", "chatNameFallback": "Your company", "customerService": {"title": "Preconfigured chatbot template", "description": "Great starting point to build your professional chatbot. Preconfigured features and customer service for you to save time and start using it.", "feature1": "Check for the business hours", "feature2": "Transfer to customer service", "feature3": "Service evaluation", "feature4": "Check for available operators", "feature5": "Automatic closing of tickets by customer inactivity", "feature6": "Customer and agents inactivity alerts"}}, "router": {"title": "How the router works", "description": "The router allows the encapsulation of multiple chatbots into one chatbot. That way, you can conduct the conversation according to each chatbot's specialty.", "learnMore": "Know more", "learnMoreUrl": "https://help.blip.ai/hc/en-us/articles/4474398386711-Undestanding-bot-and-subbot-hierarchy-or-architecture-"}, "errorMsg": {"title": "Oops... something's wrong!", "1": "There has been an error during your chatbot creation. Try using another name.", "2": "Invalid name.", "invalidName": "Your chatbot name cannot begin with numbers or special characters.", "noSubBots": "The selected router doesn't have any skill.", "deskSettingsError": "There was an error saving the Desk settings."}, "attendanceHourDefault": {"title": "Regular Hours", "description": "Default attendance hour"}, "customerInactivity": {"InactivityMessage": "Due to the lack of response, within 10 minutes, your service will be terminated.", "inactivityTag": "Closed due to customer inactivity"}, "aiAgent": {"discover": {"title": "AI Agent", "subtitle": "Use AI to answer customers", "description": "With it, you provide a knowledge base that will contextualize the AI to answer customer questions.", "new": "New", "back": "Back", "create": "Create agent", "chatName": "AI Agent", "feature1": {"title": "Effectiveness", "description": "Reduce human assistance through more effective responses"}, "feature2": {"title": "Easy and fast to build", "description": "By uploading files and making a few configurations, your agent is ready to interact with customers"}, "feature3": {"title": "Natural language", "description": "Use the natural language processing power of AI to interpret and respond as your customers do"}}, "create": {"title": "Create your AI agent", "description": "The agent will use AI and a knowledge base provided by you to answer your customers' main questions.", "basicConfiguration": {"title": "Basic settings", "assistantName": {"label": "Agent name", "placeholder": "What will your agent be called?"}, "companyProductName": {"label": "Company or product name", "placeholder": "Who the agent represents"}, "toneAndVoice": {"label": "Conversational tone and voice", "placeholder": "What will the communication style be like", "options": {"friendly": "Friendly", "funny": "Funny", "technical": "Technical"}}}, "toast": {"success": "AI agent created successfully", "error": "There was an error creating the AI agent. Do you want to try again?", "tittle": "Try again!"}, "additionalResources": {"title": "Additional features", "humanService": "Customer service"}, "back": "Back", "create": "Create"}}}}