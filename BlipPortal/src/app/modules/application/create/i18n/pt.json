{"createApplication": {"needHelp": "Precisa de ajuda para desenvolver o chatbot da sua empresa?", "requestAQuote": "Solicite um orçamento", "needHelpUrl": "https://www.take.net/contato/", "tagline": "<PERSON><PERSON><PERSON> chatbot", "taglineRouter": "<PERSON><PERSON><PERSON> roteador", "marketplace": {"title": "Escolha como começar seu chatbot", "template": {"title": "Usar template", "description": "Construa seu chatbot a partir de um modelo com funcionalidades pré-configuradas e atendimento humano, simplificando o desenvolvimento.", "tag": "Ideal para começar"}, "scratch": {"title": "Construir do zero", "description": "Construa seu chatbot desde o início e faça todas as configurações manualmente. Recomendado para quem já tem experiência com o Blip."}}, "name": {"titleScratch": "Dê um nome ao seu chatbot", "titleTemplate": "Dê um nome ao seu chatbot pré-configurado", "titleRouter": "Dê um nome ao seu roteador", "name": "Nome do chatbot", "nameRouter": "Nome do roteador", "setImage": "Definir imagem", "back": "Voltar"}, "templates": {"title": "Usar template", "useTemplate": "Escolher esse template", "back": "Voltar", "online": "Online", "chatNameFallback": "Sua empresa", "customerService": {"title": "<PERSON><PERSON>bot pré-configurado", "description": "Ótimo ponto de partida para construir o seu chatbot profissional. Funcionalidades pré-configuradas e atendimento humano para você começar a usar e poupar tempo no desenvolvimento.", "feature1": "Verificação do horário de atendimento", "feature2": "Transbordo para atendimento humano", "feature3": "Avaliação do atendimento", "feature4": "Verificação de atendentes disponíveis", "feature5": "Encerramento de tickets por inatividade do cliente", "feature6": "Alertas de inatividade do cliente e do atendente"}}, "router": {"title": "Como funciona o roteador", "description": "O roteador te ajuda a reunir vários chatbots em um só. Dessa forma, seu cliente terá acesso a múltiplos serviços conversando com um único chatbot.", "learnMore": "<PERSON>ro saber mais", "learnMoreUrl": "https://help.blip.ai/hc/pt-br/articles/4474398386711-Hiera<PERSON><PERSON><PERSON>-ou-arquitetura-de-bots-e-subbots"}, "errorMsg": {"title": "Ops... algo estranho a<PERSON>...", "1": "Houve um erro na criação do seu chatbot. Experimente usar outro nome.", "2": "Este nome não é válido.", "invalidName": "O nome de seu chatbot não pode começar com números ou caracteres especiais.", "noSubBots": "O roteador selecionado não possui nenhuma skill.", "deskSettingsError": "Houve um erro ao salvar as configurações do Desk."}, "attendanceHourDefault": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Horário de atendimento padrão"}, "customerInactivity": {"InactivityMessage": "Por falta de resposta, dentro de 10 minutos, seu atendimento será encerrado.", "inactivityTag": "Encerrado por inatividade do cliente"}, "aiAgent": {"discover": {"title": "Agente de IA", "subtitle": "Use IA para responder clientes", "description": "<PERSON>m ele, você fornece uma base de conhecimento que vai contextualizar a IA para tirar dúvidas de clientes.", "new": "Novo", "back": "Voltar", "create": "Criar agente", "chatName": "Agente de IA", "feature1": {"title": "Resolutividade", "description": "Reduza o transbordo humano através de respostas mais eficazes"}, "feature2": {"title": "Facilidade e velocidade de construção", "description": "Ao carregar arquivos e fazer algumas configurações, seu agente já está pronto para interagir com clientes"}, "feature3": {"title": "Linguagem natural", "description": "Use o poder de processamento de linguagem natural da IA para interpretar e responder como seus clientes"}}, "create": {"title": "Crie seu agente de IA", "description": "O agente vai usar IA e uma base de conhecimento fornecida por você para responder as principais dúvidas de seus clientes.", "basicConfiguration": {"title": "Configurações básicas", "assistantName": {"label": "Nome do agente", "placeholder": "Como seu agente vai se chamar?"}, "companyProductName": {"label": "Nome da empresa ou do produto", "placeholder": "Quem o agente representa"}, "toneAndVoice": {"label": "<PERSON> e voz da conversa", "placeholder": "Como será o estilo de comunicação", "options": {"friendly": "Amigável", "funny": "Divertido", "technical": "Técnico"}}}, "toast": {"success": "Agente de IA criado com sucesso", "error": "Houve um erro ao criar o agente de IA. Vamos tentar novamente?", "tittle": "Tente novamente!"}, "additionalResources": {"title": "Recursos adicionais", "humanService": "Atendimento humano"}, "back": "Voltar", "create": "<PERSON><PERSON><PERSON>"}}}}