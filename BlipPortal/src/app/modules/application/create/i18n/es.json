{"createApplication": {"needHelp": "¿Necesita ayuda para desarrollar el chatbot de su empresa?", "requestAQuote": "Solicitar presupuesto", "needHelpUrl": "https://www.take.net/es/contacto/", "tagline": "<PERSON><PERSON>r chatbot", "taglineRouter": "<PERSON><PERSON><PERSON>", "marketplace": {"title": "<PERSON><PERSON> cómo iniciar tu chatbot", "template": {"title": "Usar plantilla", "description": "Construye tu chatbot a partir de una plantilla con funcionalidades previamente configuradas y servicio humano, lo que simplifica el desarrollo.", "tag": "Ideal para empezar"}, "scratch": {"title": "Construir desde cero", "description": "Construye su chatbot desde cero y realiza todas las configuraciones manualmente. Recomendado para aquellos que ya tienen experiencia con Blip."}}, "name": {"titleScratch": "Nombre su chatbot", "titleTemplate": "Nombre su chatbot de la plantilla preconfigurada", "titleRouter": "Nombre su enrutador", "name": "Nombre del chatbot", "nameRouter": "Nombre del enrutador", "setImage": "Estabelecer image", "back": "Volver"}, "templates": {"title": "Usar plantilla", "useTemplate": "Elegir la plantilla", "back": "Volver", "online": "De conexión", "chatNameFallback": "Su empresa", "customerService": {"title": "Plantilla de chatbot preconfigurada", "description": "Gran punto de partida para construir tu chatbot profesional. Funciones preconfiguradas y servicio humano para ahorrar tiempo y empezar a utilizarlo.", "feature1": "Verifica los horarios de apertura", "feature2": "Transferencia a servicio humano", "feature3": "Evaluación del servicio", "feature4": "Verifica la disponibilidad de los asistentes", "feature5": "Cierre automático de tickets por inactividad del cliente", "feature6": "Alertas de inactividad de clientes y asistentes"}}, "router": {"title": "Cómo funciona el enrutador", "description": "El enrutador le ayuda a combinar varios chatbots en uno. De esa manera, su cliente tendrá acceso a múltiples servicios hablando con un solo chatbot.", "learnMore": "<PERSON><PERSON>ero saber más", "learnMoreUrl": "https://help.blip.ai/hc/es-mx/articles/4474398386711-Jerarqu%C3%ADa-o-arquitectura-de-bots-e-subbots"}, "errorMsg": {"title": "Ups... suced<PERSON>ó algo extraño ...", "1": "<PERSON>bo un error al crear tu chatbot. Intente usar otro nombre.", "2": "Este nombre no es valido.", "invalidName": "El nombre de tu chatbot no puede empezar con números o caracteres especiales.", "noSubBots": "El enrutador seleccionado no tiene ninguna skill.", "deskSettingsError": "Hubo un error al guardar la configuración del Desk."}, "attendanceHourDefault": {"title": "Horario regular", "description": "<PERSON><PERSON><PERSON> de servicio predeterminado"}, "customerInactivity": {"InactivityMessage": "Debido a la falta de respuesta, en un plazo de 10 minutos, se cerrará esta conversación.", "inactivityTag": "Cerrado por inactividad del cliente"}, "aiAgent": {"discover": {"title": "Agente de IA", "subtitle": "Deixe a IA responder seus clientes", "description": "Com o AI Agent, você contextualiza a IA com uma base de conhecimento para responder seus clientes.", "new": "Novo", "back": "Voltar", "create": "Criar agente de IA", "chatName": "Agente de IA", "feature1": {"title": "Resolutividade", "description": "Responda dúvidas de forma humanizada a partir de documentos e bases de conhecimento privada da sua empresa"}, "feature2": {"title": "Facilidade e velocidade de construção", "description": "Com uma configuração inicial e o carregamento de arquivos, você já tem seu agente pronto para interagir com seus clientes"}, "feature3": {"title": "Linguagem natural", "description": "Use o poder de processamento de linguagem natural do AI Agent para interpretar e responder como seus clientes"}}, "create": {"title": "Criar agente de IA", "description": "Com o AI Agent, você fornece uma base de conhecimento que vai contextualizar a IA para responder as dúvidas de seus clientes.", "basicConfiguration": {"title": "Configurações básicas", "assistantName": {"label": "Nome do assistente", "placeholder": "Como seu assistente vai se chamar"}, "companyProductName": {"label": "Nome da empresa ou do produto", "placeholder": "Quem representa"}, "toneAndVoice": {"label": "<PERSON> e voz da conversa", "placeholder": "Como será a comunicação", "options": {"friendly": "Amigável", "funny": "Divertido", "technical": "Técnico"}}}, "toast": {"success": "Agente de IA creado exitosamente", "error": "Hubo un error al crear el agente de IA. ¿Quieres intentarlo de nuevo?", "tittle": "¡Inténtalo de nuevo!"}, "additionalResources": {"title": "Recursos adicionais", "humanService": "Atendimento humano"}, "back": "Voltar", "create": "<PERSON><PERSON><PERSON>"}}}}