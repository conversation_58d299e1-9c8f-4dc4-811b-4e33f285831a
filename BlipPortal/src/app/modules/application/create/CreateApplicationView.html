<bds-theme-provider theme="dark">
    <div class="full-screen-container flex flex-column items-center" id="create-application-container">
        <bds-icon
                name="close"
                size="xxx-large"
                ng-click="$ctrl.close()"
                class="close-icon absolute right-0"
            ></bds-icon>
            <bds-illustration ng-show="$ctrl.logoCenter" type="brand" name="blip-ballon-blue-white-horizontal" class="logo-image">
            </bds-illustration>
        <div ng-show="!$ctrl.logoCenter" class="logo-container">
            <bds-illustration  type="brand" name="blip-ballon-blue-white-horizontal" class="right-logo-image absolute left-0">
            </bds-illustration>
         </div>
        <form
            id="applicationForm"
            name="$ctrl.applicationForm"
            class="create-application-form w-100"
            ng-submit="$ctrl.createApplication()"
            novalidate
        >
            <div class="container" ui-view></div>
        </form>
        <div class="create-application-footer mw-100 tc" ng-show="$ctrl.showFooter">
            <bds-typo variant="fs-14" tag="span" translate
                >createApplication.needHelp</bds-typo
            >
            <a ng-click="$ctrl.openQuoteRequest()" class="pointer"
                ><bds-typo variant="fs-14" bold="bold" tag="span" translate
                    >createApplication.requestAQuote</bds-typo
                ></a
            >
        </div>
    </div>
</bds-theme-provider>
