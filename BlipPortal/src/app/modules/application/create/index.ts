import * as angular from 'angular';
import 'angular-ui-router';

import { CreateApplicationController } from './CreateApplicationController';
import { TestTemplateController } from './steps/testTemplate/TestTemplateController';
import { CreateAIAgentController } from './steps/aiAgent/create/CreateAiAgentController';

import CreateApplicationView from './CreateApplicationView.html';
import MarketplaceStepView from './steps/MarketplaceStepView.html';
import NameStepView from './steps/NameStepView.html';
import TestTemplateView from './steps/testTemplate/TestTemplateView.html';
import RouterStepView from './steps/RouterStepView.html';
import CreateAiAgentView from './steps/aiAgent/create/CreateAiAgentView.html';

import { NavbarFeatures } from 'modules/navbar/NavbarFeatures';
import { ContextProcessorService, Contexts } from 'modules/core/ContextProcessorService';
import { applicationListStateName } from 'modules/application/list';
import { IStateService } from 'angular-ui-router';
import { TenantService } from '../tenant/TenantService';

export const applicationCreateMarketplaceStateName = 'auth.application.create.marketplace';
export const applicationCreateAiAgentStateName = 'auth.application.create.aiagent';

export const applicationCreate = angular
    .module('application.create', ['ui.router'])
    .controller('CreateApplicationController', CreateApplicationController)
    .service('TenantService', TenantService)
    .config(($stateProvider) => {
        'ngInject';

        $stateProvider
            .state('auth.application.create', {
                abstract: true,
                views: {
                    '@': {
                        template: CreateApplicationView,
                        controller: CreateApplicationController,
                        controllerAs: '$ctrl',
                    },
                },
                resolve: {
                    checkSharedViewChatbotCreation,
                },
            })
            .state('auth.application.create.router', {
                url: '/application/create/router',
                template: RouterStepView,
                params: {
                    template: undefined,
                },
            })
            .state(applicationCreateAiAgentStateName, {
                url: '/application/aiagent/create',
                views: {
                    '@auth.application.create': {
                        template: CreateAiAgentView,
                        controller: CreateAIAgentController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    showFooter: false,
                    logoCenter: false,
                },
            })
            .state(applicationCreateMarketplaceStateName, {
                url: '/application/create/marketplace',
                template: MarketplaceStepView,
            })
            .state('auth.application.create.name', {
                url: '/application/create/name/{template}?{templateLang}',
                template: NameStepView,
            })
            .state('auth.application.create.test', {
                url: '/application/create/test/{template}',
                views: {
                    '@auth.application.create': {
                        template: TestTemplateView,
                        controller: TestTemplateController,
                        controllerAs: '$ctrl',
                    },
                },
            });
    }).name;

const checkSharedViewChatbotCreation = async (
    ContextProcessorService: ContextProcessorService,
    $state: IStateService,
    TenantService: TenantService
) => {
    await ContextProcessorService.waitForInitialization();
    const isOrganizationEnabled = await TenantService.isOrganizationEnabled();

    if (isOrganizationEnabled && ContextProcessorService.context === Contexts.Default) {
        const isCreateButtonsDisabled = await NavbarFeatures.isCreateButtonsOnSharedViewDisabled();

        if (isCreateButtonsDisabled) {
            $state.go(applicationListStateName);
        }
    }
};
