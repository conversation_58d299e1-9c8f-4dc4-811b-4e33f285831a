import { FixIntentController } from '../components/FixIntent/FixIntentController';
import FixIntentView from '../components/FixIntent/FixIntentView.html';

import { IDocumentService } from 'angular';
import { LoadingService } from 'modules/ui/LoadingService';
import { SidebarContentService } from 'modules/shared/SidebarContentService';
import { IPayload } from 'modules/eventReceiver/EventReceiver';

const ANALYSIS_DETAILS_ID = 'right-sidebar';
const CONTROLLER_ALIAS = '$ctrl';

export class FixIntentProcessor {
    constructor(
        private $document: IDocumentService,
        private SidebarContentService: SidebarContentService,
        private LoadingService: LoadingService
    ) { }

    public async open({ message, trackingProperties, source }: IPayload): Promise<any> {
        const sidenav = await this.SidebarContentService.showSidebar({
            controller: FixIntentController,
            controllerAs: CONTROLLER_ALIAS,
            template: FixIntentView,
            appendToElement: this.$document[0].getElementById(
                ANALYSIS_DETAILS_ID,
            ),
            inputs: {
                selectedAnalyses: message.content.selectedAnalyses,
                intents: message.content.intents,
                LoadingService: this.LoadingService,
            },
        });
        const response = await sidenav.closeDeferred.promise;

        source.postMessage({
            response,
            trackingProperties,
        }, '*');
    }
}
