import { AnalysisModalController } from '../components/AnalysisModal/AnalysisModalController';
import AnalysisModalView from '../components/AnalysisModal/AnalysisModalView.html';
import { getService } from 'data/function';

export class AnalysisModalProcessor {

    private get ModalService() {
        return getService('ModalService');
    }

    public open(analysis: Analysis) {
        this.ModalService.showModal({
            controller: AnalysisModalController,
            controllerAs: '$ctrl',
            template: AnalysisModalView,
            inputs: {
                analysis,
            },
        });
    }
}
