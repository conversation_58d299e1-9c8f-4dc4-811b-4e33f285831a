import { AnalysisDetailsController } from '../components/AnalysisDetails/AnalysisDetailsController';
import AnalysisDetailsView from '../components/AnalysisDetails/AnalysisDetailsView.html';

import { IDocumentService } from 'angular';
import { SidebarContentService, SidebarContent } from 'modules/shared/SidebarContentService';

const ANALYSIS_DETAILS_ID = 'right-sidebar';
const CONTROLLER_ALIAS = '$ctrl';

export class AnalysisDetailsProcessor {
    constructor(
        private $document: IDocumentService,
        private SidebarContentService: SidebarContentService
    ) { }

    public open(analysis): Promise<SidebarContent> {
        return this.SidebarContentService.showSidebar({
            controller: AnalysisDetailsController,
            controllerAs: CONTROLLER_ALIAS,
            template: AnalysisDetailsView,
            appendToElement: this.$document[0].getElementById(
                ANALYSIS_DETAILS_ID,
            ),
            inputs: {
                analysis: analysis,
            },
        });
    }

}
