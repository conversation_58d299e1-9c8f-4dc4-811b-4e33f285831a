import ModelTestView from '../components/ModelTest/ModelTestView.html';
import { ModelTestController } from '../components/ModelTest/ModelTestController';

import { IDocumentService } from 'angular';
import { ApplicationService2 } from 'modules/application/ApplicationService2';
import { SidebarContentService, SidebarContent } from 'modules/shared/SidebarContentService';

export class ModelTestProcessor {

    constructor(
        private $document: IDocumentService,
        private ApplicationService2: ApplicationService2,
        private SidebarContentService: SidebarContentService
    ) { }

    public open(): Promise<SidebarContent> {
        return this.SidebarContentService.showSidebar({
            template: ModelTestView,
            controller: ModelTestController,
            controllerAs: '$ctrl',
            appendToElement: this.$document[0].getElementById(
                'right-sidebar',
            ),
            inputs: {
                ApplicationService2: this.ApplicationService2,
            },
        });
    }

}
