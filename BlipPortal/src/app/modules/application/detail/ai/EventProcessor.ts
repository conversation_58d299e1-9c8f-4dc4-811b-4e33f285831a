import { getService } from 'data/function';
import { EventAction } from './EventActions';
import { ModelTestProcessor } from './processors/ModelTestProcessor';
import { AnalysisDetailsProcessor } from './processors/AnalysisDetailsProcessor';
import { FixIntentProcessor } from './processors/FixIntentProcessor';
import { AnalysisModalProcessor } from './processors/AnalysisModalProcessor';
import { EventSubprocessor } from 'modules/eventReceiver/EventSubprocessor';
import { IPayload } from 'modules/eventReceiver/EventReceiver';

// Processor defined prefix
export const PROCESSOR_PREFIX = 'ArtificialIntelligence';

// ArtificialIntelligence application event processor
export class ArtificialIntelligenceEventProcessor extends EventSubprocessor {

    private static get ModelTestProcessor(): ModelTestProcessor {
        return getService(nameof<ModelTestProcessor>());
    }

    private static get AnalysisDetailsProcessor(): AnalysisDetailsProcessor {
        return getService(nameof<AnalysisDetailsProcessor>());
    }

    private static get AnalysisModalProcessor(): AnalysisModalProcessor {
        return getService(nameof<AnalysisModalProcessor>());
    }

    private static get FixIntentProcessor(): FixIntentProcessor {
        return getService(nameof<FixIntentProcessor>());
    }

    public static async process(payload: IPayload): Promise<void> {
        const action = this.removeProcessorPrefix(payload.message.action, PROCESSOR_PREFIX);

        switch (action) {
            case EventAction.OpenModelTest:
                await this.ModelTestProcessor.open();
                break;

            case EventAction.DisplayAnalysisInformation:
                await this.AnalysisModalProcessor.open(payload.message.content);
                break;

            case EventAction.OpenAnalysisDetails:
                await this.AnalysisDetailsProcessor.open(payload.message.content);
                break;

            case EventAction.OpenFixIntent:
                await this.FixIntentProcessor.open(payload);
                break;

            default:
                throw `No processor available for action ${action}`;
        }
    }
}
