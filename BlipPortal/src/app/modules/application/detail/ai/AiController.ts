import { LoadingService } from 'modules/ui/LoadingService';
import { ISCEService, IScope } from 'angular';
import { IStateParamsService } from 'angular-ui-router';
import { TenantService } from 'modules/application/tenant/TenantService';
import { Application } from 'modules/shared/ApplicationTypings';
import { AiFeatures } from './AiFeatures';
import { isLocalhost } from 'data/http';
import {
    UserBlipAccount,
    AccountService2,
} from 'modules/account/AccountService2';
import TranslateService from 'modules/translate/TranslateService';
import {
    aiSettingsSource,
    blipAccountIssuer,
    messagingHubDomain,
    blipDomainUrl,
    messaginghubWebsocketHostName,
    messaginghubWebsocketHostNameTenant,
    messaginghubWebsocketPort,
    messaginghubWebsocketScheme
} from 'app.constants';

interface ApplicationData {
    shortName: string;
    accessKey: string;
}

interface MesssagingHubConfigurations {
    domainUrl: string;
    domain: string;
    accountIssuer: string;
    websocketPort: string;
    websocketScheme: string;
    websocketHostname: string;
    websocketHostnameTenant: string;
}

export class AiController {
    frameUrl: string;
    errorMessage: string;
    isReactViewEnabled: boolean;
    listEnabledReactPages: Array<string>[];
    settingsSource: string = aiSettingsSource;
    language: string;
    user: UserBlipAccount;
    applicationData: ApplicationData;
    messagingHubConfigurations: MesssagingHubConfigurations;

    constructor(
        private $stateParams: IStateParamsService,
        private $scope: IScope,
        private $sce: ISCEService,
        private LoadingService: LoadingService,
        private AI_FRAME_URL: string,
        private AI_IS_MFE_ENABLED: boolean,
        private $location: any,
        private DEFAULT_FRAGMENT_PREFIX: string,
        private application: Application,
        private TenantService: TenantService,
        private TranslateService: TranslateService,
        private AccountService2: AccountService2
    ) { }

    async $onInit() {
        this.user = await this.getUserCopy();
        this.language = this.TranslateService.getCurrentLanguage();

        const { shortName, accessKey } = this.application;
        this.applicationData = { shortName, accessKey };
        this.messagingHubConfigurations = {
            domainUrl: blipDomainUrl,
            domain: messagingHubDomain,
            accountIssuer: blipAccountIssuer,
            websocketPort: messaginghubWebsocketPort,
            websocketScheme: messaginghubWebsocketScheme,
            websocketHostname: messaginghubWebsocketHostName,
            websocketHostnameTenant: messaginghubWebsocketHostNameTenant,
        };

        this.isReactViewEnabled = await AiFeatures.isMicrofrontAiReactEnabled();
        this.listEnabledReactPages = await AiFeatures.getReactPagesList();

        if (!this.isCurrentPathAvailableInMFE) {
            this.LoadingService.startLoading();
        }

        const params = this.$stateParams.path
            ? `${this.$stateParams.path.replace(/&/g, '/')}`
            : '';
        const aiFrameUrl = this.application.tenantId // To fix cases where a proxy is needed for debugging
            ? this.TenantService.addTenantPrefixToUrl(this.AI_FRAME_URL, this.application.tenantId)
            : this.TenantService.addTenantPrefixToUrl(this.DEFAULT_FRAGMENT_PREFIX, this.application.tenantId);

        this.frameUrl = this.$sce.trustAsResourceUrl(`${aiFrameUrl}${params}`);

        this.errorMessage =
            'Error while loading Artificial Intelligence module. Refresh the page or try again later';

        this.$scope.$on('$destroy', this.LoadingService.stopLoading);
    }

    async getUserCopy(): Promise<UserBlipAccount> {
        const user = await this.AccountService2.me();
        return JSON.parse(JSON.stringify(user));
    }

    get isCurrentPathAvailableInMFE(): boolean {
        const isMFEAvailable = this.AI_IS_MFE_ENABLED && this.isReactViewEnabled;

        if (!isMFEAvailable) {
            return false;
        }

        const basepath = `/application/detail/${this.application.shortName}/artificial-intelligence/`;
        const currentPath = this.$location.$$path.replace(basepath, '').split('/')[0];
        const isPathEnabledOnReact = this.listEnabledReactPages.includes(currentPath);

        return isPathEnabledOnReact;
    }

    onFrontendLoad() {
        this.LoadingService.stopLoading();
    }

    onFrontendError() {
        this.LoadingService.stopLoading();
    }
}
