{"ai-publication": {"newTitle": "Publication", "newSubtitle": "Train, test and publish your AI model", "title": "Publish AI model", "subtitle": "Train and publish your AI model", "publishedVersion": "Published version", "trainedVersion": "Training version", "instructions": "Before you upgrade your AI model, resolve:", "aiProvider": "Artificial Intelligence provider active", "userIntentions": "User intents", "config": "Resolve", "createIntention": "Resolve", "chatbotReady": "All set to upgrade your AI model", "chatbotReady2": "Remeber this action is subject to charges of your AI provider.", "versions": "Versions", "checkList": "Checklist", "numIntentions": "Number of intents", "minimum": "Minimum number of examples", "balancing": "Balancing", "lessThanTenSingular": "intent from your model has less than 10 examples", "lessThanTenPlural": "intents from your model have less than 10 examples", "moreThanTen": "Congratulations! Your intents have at least 10 examples", "belowAvg": "below average", "onAvg": "on average", "aboveAvg": "above average", "median": "Try balancing your intents to aprox.", "medianFinal": "examples", "congrats": "Congrats! The intents from your model are balanced", "lastModification": "Last modification", "modelVersions": "AI model versions", "testModel": "Test AI model", "startTrain": "Train AI model", "publish": "Publish", "errors": {"tryAgain": "An error has occurred, please try again", "noIntents": "No intents with questions found", "trainingInProgress": "The training is in progress on the AI provider", "notPublished": "An error has occurred while publishing", "notConfigured": "Please configure your Artificial intelligence provider", "invalidName": "Watson error: intent name can only contain word, underscore, hyphen or dot characters, and must not start with a prefix of \"sys\"", "workspaceLimit": "Watson error: the workspace limit has exceeded. Remove at least 3 workspaces and try again", "invalidConfig": "Watson error: check your configurations and try again", "notTrained": "An error has occurred while training"}, "success": {"published": "Published successfully!", "trained": "Trained successfully!"}, "testSidbar": {"testModel": "Test AI model", "testPlaceholderMessage": "Type your message here", "testIntentionLabel": "Intention", "testEntityLabel": "Entities", "testContentLabel": "Contents", "filter": "Filter", "subtitle": "Tags", "tootip": "Show tag filter", "placeholder": "Select a value", "errors": {"defaultProviderNotDefined": "Default provider is not defined", "trainingInProgress": "The training is in progress on the AI provider", "modelNotFound": "No available model found", "generic": "Your model is not yet available for analysis"}}}, "publication": {"testModel": "Test AI model", "testPlaceholderMessage": "Type your message here", "testIntentionLabel": "Intention", "testEntityLabel": "Entities", "noModelError": "Your model is not yet available for analysis"}}