{"ai-publication": {"newTitle": "Publicação", "newSubtitle": "<PERSON><PERSON><PERSON><PERSON>, testar e publicar modelo de IA", "title": "Publicar modelo de IA", "subtitle": "Faça o treinamento e publique o seu modelo de IA", "publishedVersion": "Versão publicada", "trainedVersion": "Versão de treinamento", "instructions": "Antes de atualizar o seu modelo de IA, resolva:", "aiProvider": "Configuração do provedor de IA", "userIntentions": "Adicionar intenções do usuário", "config": "Resolver", "createIntention": "Resolver", "chatbotReady": "Tudo pronto para atualizar seu modelo de IA!", "chatbotReady2": "Lembre-se que esta ação está sujeita a encargos do seu provedor de IA.", "versions": "Versõ<PERSON>", "checkList": "Checklist do modelo atual", "numIntentions": "Número de intenções", "minimum": "<PERSON><PERSON><PERSON>emplos", "balancing": "Balanceamento", "lessThanTenSingular": "intenção do seu modelo possui menos de 10 exemplos", "lessThanTenPlural": "intenções do seu modelo possuem menos de 10 exemplos", "moreThanTen": "Parabéns! As intenções do seu modelo possuem pelo menos 10 exemplos do usuário", "belowAvg": "a<PERSON><PERSON><PERSON> da média", "onAvg": "na média", "aboveAvg": "a<PERSON><PERSON> da média", "median": "Tente balancear as suas intenções para aprox.", "medianFinal": "exemplos", "congrats": "Parabéns! As intenções do seu modelo estão balanceadas", "lastModification": "Última modificação", "modelVersions": "Versões do modelo de IA", "testModel": "Testar modelo de IA", "startTrain": "Treinar modelo de IA", "publish": "Publicar", "errors": {"tryAgain": "Ocorreu um erro, por favor tente novamente", "noIntents": "Nenhuma intenção com perguntas foi encontrada", "trainingInProgress": "Treinamento em progresso no provedor de IA", "notPublished": "Ocorreu um erro na publicação", "notConfigured": "Por favor configure o seu provedor de inteligência artificial", "invalidName": "Erro no Watson: nome da intenção pode conter somente letras, underscore, hífen e pontos e não pode começar com \"sys\"", "workspaceLimit": "Erro no Watson: o limite de workspaces do seu plano foi alcançado. Remova pelo menos 3 workspaces e tente novamente", "invalidConfig": "Erro no Watson: verifique suas configurações e tente novamente", "notTrained": "Ocorreu um erro no treinamento"}, "success": {"published": "Publicado com sucesso!", "trained": "Treinado com sucesso!"}, "testSidbar": {"testModel": "Testar modelo de IA", "testPlaceholderMessage": "Digite sua mensagem aqui", "testIntentionLabel": "Intenção", "testEntityLabel": "Entidades", "testContentLabel": "Conte<PERSON><PERSON>", "filter": "Filtro", "subtitle": "Tags", "tootip": "Mostra o filtro da Tag", "placeholder": "Insira aqui os valores da Tag", "errors": {"defaultProviderNotDefined": "<PERSON><PERSON><PERSON> não definido", "trainingInProgress": "Treinamento em progresso no provedor de IA", "modelNotFound": "Nenhum modelo disponível encontrado", "generic": "O seu modelo ainda não está disponível para análise"}}}, "publication": {"testModel": "Testar modelo de IA", "testPlaceholderMessage": "Digite sua mensagem aqui", "testIntentionLabel": "Intenção", "testEntityLabel": "Entidades", "noModelError": "O seu modelo ainda não está disponível para análise"}}