{"ai-publication": {"newTitle": "Publicación", "newSubtitle": "Entrene, pruebe y publique el modelo de IA", "title": "Publicar modelo de IA", "subtitle": "Realice la capacitación y publique su modelo de IA", "publishedVersion": "Versión publicada", "trainedVersion": "Versión de entrenamiento", "instructions": "Antes de actualizar su modelo de IA, resuelva:", "aiProvider": "Configuración del proveedor de IA", "userIntentions": "Agregar intenciones de usuario", "config": "Resolver", "createIntention": "Resolver", "chatbotReady": "¡Todo listo para actualizar su modelo de IA!", "chatbotReady2": "Recuerde que esta acción está sujeta a cargos por parte de su proveedor de inteligencia artificial.", "versions": "Versiones", "checkList": "Lista de verificación del modelo actual", "numIntentions": "Numero de intenciones", "minimum": "<PERSON><PERSON><PERSON><PERSON>", "balancing": "Equilibrio", "lessThanTenSingular": "la intención de su modelo tiene menos de 10 ejemplos", "lessThanTenPlural": "las intenciones de su modelo tienen menos de 10 ejemplos", "moreThanTen": "¡Felicidades! Las intenciones de su modelo tienen al menos 10 ejemplos de usuario", "belowAvg": "por debajo del promedio", "onAvg": "promedio", "aboveAvg": "por encima del promedio", "median": "Intente equilibrar sus intenciones en aprox.", "medianFinal": "<PERSON><PERSON><PERSON><PERSON>", "congrats": "¡Felicidades! Las intenciones de tu modelo están equilibradas", "lastModification": "Última modificación", "modelVersions": "Versiones del modelo de IA", "testModel": "Probar el modelo de IA", "startTrain": "Entrenar modelo de IA", "publish": "Publicar", "errors": {"tryAgain": "Ha ocurrido un error. Por favor intente de nuevo", "noIntents": "Sin intención con preguntas encontradas", "trainingInProgress": "Formación continua en el proveedor de IA", "notPublished": "Ocurrió un error en la publicación", "notConfigured": "Configure su proveedor de inteligencia artificial", "invalidName": "Erro<PERSON> <PERSON>: el nombre de la intención solo puede contener letras, guiones bajos, guiones y puntos y no puede comenzar con \"sys\"", "workspaceLimit": "Error de <PERSON>: se alcanzó el límite de espacio de trabajo de su plan. Elimine al menos 3 espacios de trabajo y vuelva a intentarlo", "invalidConfig": "E<PERSON><PERSON> <PERSON>: verifique su configuración y vuelva a intentarlo", "notTrained": "Se produjo un error de entrenamiento"}, "success": {"published": "¡Publicado con éxito!", "trained": "¡Entrenado con éxito!"}, "testSidbar": {"testModel": "Probar el modelo de IA", "testPlaceholderMessage": "Escriba su mensaje aquí", "testIntentionLabel": "Intención", "testEntityLabel": "Entidades", "testContentLabel": "Contenidos", "filter": "Filter", "subtitle": "Tags", "tootip": "Mostrar filtro de etiqueta", "placeholder": "Seleccione un valor", "errors": {"defaultProviderNotDefined": "Proveedor predeterminado no definido", "trainingInProgress": "Formación continua en el proveedor de IA", "modelNotFound": "No se encontraron modelos disponibles", "generic": "Su modelo aún no está disponible para revisión"}}}, "publication": {"testModel": "Probar el modelo de IA", "testPlaceholderMessage": "Escriba su mensaje aquí", "testIntentionLabel": "Intención", "testEntityLabel": "Entidades", "noModelError": "Su modelo aún no está disponible para revisión"}}