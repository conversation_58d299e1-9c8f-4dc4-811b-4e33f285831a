import { FeatureToggleClientService } from 'feature-toggle-client';

const microfrontAi = 'microfront-ai';
const reactView = 'ai-enable-react-view';
const reactPagesList = 'ai-fragment-list-available-react-pages';

export class AiFeatures {
    static isMicrofrontAiEnabled() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(microfrontAi);
    }

    static getReactPagesList() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(reactPagesList);
    }

    static async isMicrofrontAiReactEnabled() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            reactView
        );
    }
}
