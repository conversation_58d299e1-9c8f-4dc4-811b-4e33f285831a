import * as moment from 'moment';
export class AnalysisModalController {
    constructor(
        private analysis: any,
        private close: Function
    ) { }
    get analysisDate() {
        return moment(
            new Date(this.analysis.requestDateTime),
        ).format('DD/MM/YYYY HH:mm');
    }
    get nameAndScore() {
        return (
            this.analysis.intention.name +
            '/' +
            this.analysis.displayScore
        );
    }
    get entities() {
        const entities = this.analysis.entities.map(el => el.name + ': ' + el.value).join(', ');
        return entities.length ? entities : '-';
    }
}
