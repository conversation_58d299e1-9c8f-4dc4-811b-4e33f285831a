<div id="ai-sidebar" class="sidebar-content-component position-right right-entrance-animation z-max">
    <div class="sidebar-header">
        <div class="sidebar-title">
            <bds-typo class="color-surface-1">{{ $ctrl.labels.title }}</bds-typo>
        </div>
        <div class="actions">
            <bds-icon class="color-surface-2" name="close" variant="secondary" ng-click="$ctrl.closesSidebar()"></bds-icon>
        </div>
    </div>
    <div class="sidebar-body">
        <span>
            <div id="phrase-content" class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.phrase }}
                </bds-typo>
                <bds-typo class="block-content">
                    {{ $ctrl.analysis.text }}
                </bds-typo>
            </div>
            <div id="intent-content" class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.intent }}
                </bds-typo>
                <bds-typo class="block-content">
                    {{ $ctrl.analysis.intentionName || $ctrl.labels.values.doNotIdentified }}
                </bds-typo>
            </div>
            <div ng-if="$ctrl.analysis.intentionName" id="confiability-content" class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.confiability }}
                </bds-typo>
                <div id="confiability-value-content" class="block-content card--mini-card">
                    <div id="confiability-progress-bar">
                        <bds-typo id="confiability-progress-bar-text">{{ $ctrl.analysis.percentage }}</bds-typo>
                        <round-progress
                        current="$ctrl.analysis.score"
                        max="1"
                        bgcolor="#C9DFE4"
                        radius="40"
                        rounded="true"
                        duration="1000"
                        stroke="7"
                        animation="easeInOutQuart">
                        </round-progress>
                    </div>
                    <bds-typo id="confiability-text" ng-bind-html="$ctrl.labels.values.confiability"></bds-typo>
                </div>
            </div>
            <div id="entities-content"
                class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.entities }}
                </bds-typo>
                <blip-table  ng-if="$ctrl.analysis.entities && $ctrl.analysis.entities.length > 0" table-data="$ctrl.analysis.entities">
                    <blip-column title="{{ $ctrl.labels.entitiesTableHeaders.entity }}" column-class="bp-c-cloud"
                        row-param="name" width="50%" sortable></blip-column>
                    <blip-column title="{{ $ctrl.labels.entitiesTableHeaders.value }}" column-class="bp-c-cloud"
                        row-param="value" width="50%" sortable></blip-column>
                </blip-table>
                <bds-typo ng-if="!$ctrl.analysis.entities || $ctrl.analysis.entities.length == 0" class="block-content">
                    {{ $ctrl.labels.values.doNotIdentified }}
                </bds-typo>
            </div>
            <div id="contents-content" class="block" ng-if="$ctrl.analysis.content">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.content }}
                </bds-typo>
                <bds-typo ng-if="!$ctrl.analysis.content" class="block-content">
                    {{ $ctrl.labels.values.doNotIdentified }}
                </bds-typo>
            </div>
            <bds-typo id="user-header" class="block-header">
                {{ $ctrl.labels.headers.user }}
            </bds-typo>

            <div id="idUser-content" class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.idUser }}
                </bds-typo>
                <bds-typo class="block-content">
                    {{ $ctrl.analysis.userIdentity || $ctrl.labels.values.unknown }}
                </bds-typo>
            </div>

            <div id="date-content" class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.date }}
                </bds-typo>
                <bds-typo class="block-content">
                    {{ $ctrl.analysis.completeDate }}
                </bds-typo>
            </div>
        </span>

        <div class="track-conversations-container">
            <bds-button ng-disabled="!$ctrl.analysis.userIdentity" ng-click="$ctrl.trackSeeConversationAndGoToUserDetails()">{{ $ctrl.labels.buttons.seeConversation }}</bds-button>
        </div>
    </div>
</div>
