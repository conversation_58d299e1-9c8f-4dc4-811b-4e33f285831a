import { AccountService2 } from 'modules/account/AccountService2';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { Application } from 'modules/shared/ApplicationTypings';
import { CurrentApplicationService } from 'modules/shared/CurrentApplicationService';
import * as uuid from 'uuid';
import { SegmentService } from '../../../../misc/SegmentService';
import './ModelTestStyle.scss';
import { ArtificialIntellingeceConfigurationsService } from '../../../../misc/ArtificialIntellingeceFeatureConfigurations';
import { ActionFeatures as ActionFeaturesFromSubmodule } from 'modules/application/detail/portal-submodule-builder/templates/builder/tabs/actions/ActionFeatures';
import { ActionFeatures } from 'modules/application/detail/templates/builder/tabs/actions/ActionFeatures';
import { Option } from 'blip-ds/dist/types/components/selects/select-interface';
import { useBuilderSubmodule } from 'app.constants';

const DEFAULT_PROVIDER_NOT_DEFINED_CODE_ERROR: Number = 1233;
const TRAINING_IN_PROGRESS_CODE_ERROR: Number = 1214;
const MODEL_NOT_FOUND_CODE_ERROR: Number = 1213;

const DEFAULT_PROVIDER_NOT_DEFINED_ERROR: string = 'ai-publication.testSidbar.errors.defaultProviderNotDefined';
const TRAINING_IN_PROGRESS_ERROR: string = 'ai-publication.testSidbar.errors.trainingInProgress';
const MODEL_NOT_FOUND_ERROR: string = 'ai-publication.testSidbar.errors.modelNotFound';
const GENERIC_ERROR: string = 'ai-publication.testSidbar.errors.generic';
const MESSAGE_AREA_PLACEHOLDER: string = 'ai-publication.testSidbar.testPlaceholderMessage';
const INTENTS_INPUT_LABEL: string = 'ai-publication.testSidbar.testIntentionLabel';
const ENTITIES_INPUT_LABEL: string = 'ai-publication.testSidbar.testEntityLabel';
const CONTENTS_INPUT_LABEL: string = 'ai-publication.testSidbar.testContentLabel';

const OPEN_STATUS_SUCCESS_TRACKING: string = 'ai-nlp-testmodel-click-success';
const OPEN_STATUS_FAILED_TRACKING: string = 'ai-nlp-testmodel-click-fail';
const ANALYSE_CONTENT_SUCCESS_TRACKING: string = 'ai-nlp-publication-model-testwindow-testsent';
const ANALYSE_CONTENT_FAILED_TRACKING: string = 'ai-nlp-publication-model-testwindow-testsent';
const INTENT_LIST_TRACKING = 'ai-nlp-publication-model-testwindow-testanalysis-intent-listed';
const INTENT_UNLIST_TRACKING = 'ai-nlp-publication-model-testwindow-testanalysis-intent-listed';
const ENTITY_LIST_TRACKING = 'ai-nlp-publication-model-testwindow-testanalysis-entity-listed';
const ENTITY_UNLIST_TRACKING = 'ai-nlp-publication-model-testwindow-testanalysis-entity-listed';

export class ModelTestController {

    private model: any;
    private application: Application;

    public analyses: { id: string, content: string, promise: Promise<Analysis> }[] = [];
    public entities: Entity[];
    public intentions: Intention[];
    public contents: Content[];
    public tagValues: Option[] = [];
    public optionSelect: Array<any> = [];

    public intentsLabel: string;
    public entitiesLabel: string;
    public contentsLabel: string;
    public placeholder: string;
    public V2Enabled: boolean = false;

    public canSendMessage: boolean = false;
    isConfigurationsApplicationGetEnabled: boolean = false;

    constructor(
        private close: any,
        private $translate,
        private SegmentService: SegmentService,
        private AccountService2: AccountService2,
        private MessagingHubService: MessagingHubService,
        private CurrentApplicationService: CurrentApplicationService,
        private ngToast: any,
        private ArtificialIntellingeceConfigurationsService: ArtificialIntellingeceConfigurationsService
    ) {
        this.init();
    }

    onChangeItem = (event: CustomEvent) => {
        const { detail: { data } } = event;
        this.optionSelect = data;
    }

    async init() {
        const actionFeatures = useBuilderSubmodule ? ActionFeaturesFromSubmodule : ActionFeatures;
        this.isConfigurationsApplicationGetEnabled = await actionFeatures.isUseConfigurationsDomainOnMiscOperationsGetEnabled();
        this.application = await this.CurrentApplicationService.fetchApplication();
        await this.checkModelStatus();

        this.V2Enabled = (await this.isContentAssistantV2Enabled()) == true;
        if (this.V2Enabled) {
            const result = await this.getTags();
            (result.items || []).forEach(item => {
                item.values.reduce((values, value) =>
                    values.concat(value.synonymous.map(v => {
                        const newtag = { label: v, value: v };
                        this.tagValues.push(newtag);
                    })), []
                );
            });
        }

        this.placeholder = await this.$translate(MESSAGE_AREA_PLACEHOLDER);
        this.intentsLabel = await this.$translate(INTENTS_INPUT_LABEL);
        this.entitiesLabel = await this.$translate(ENTITIES_INPUT_LABEL);
        this.contentsLabel = await this.$translate(CONTENTS_INPUT_LABEL);

        this.intentions = await this.getIntents();
        this.entities = await this.getEntities();
    }

    async checkModelStatus(): Promise<any> {
        this.model = await this.getLastModel();
        if (this.model) {
            this.canSendMessage = true;
            this.SegmentService.createApplicationTrack({trackEvent: OPEN_STATUS_SUCCESS_TRACKING});
        } else {
            this.SegmentService.createApplicationTrack({trackEvent: OPEN_STATUS_FAILED_TRACKING});
            this.close();
        }
    }

    async openFilterTag() {
        const modalElement = document.getElementById('card-filter');
        if (modalElement.style.display == 'none') {
            modalElement.style.display = 'block';
            modalElement.style.opacity = '1';
        } else {
            modalElement.style.display = 'none';
            modalElement.style.opacity = '0';
        }
    }

    async messageSubmit(content: string) {
        const currentUser = await this.AccountService2.me();

        this.analyses = this.analyses.concat({
            id: uuid.v4(),
            content: content,
            promise: new Promise<Analysis>(async (resolve, reject) => {
                try {
                    const analyses = await this.MessagingHubService.ArtificialIntelligence
                        .analyse({
                            text: content,
                            testingRequest: true,
                            modelId: this.model.id,
                            extras: {
                                UserIdentity: currentUser.identity,
                            },
                        });
                    const entities = analyses.entities.map(e => e.value);
                    const intent = analyses.intentions[0].id;
                    let result: any = {};
                    const resource = {
                        Intent: intent,
                        Entities: entities,
                        MinEntityMatch: entities.length,
                        results: [
                                    {
                                        tags: (this.optionSelect ?? [])
                                    }
                                ]

                    };

                    if (await this.isContentAssistantV2Enabled()) {
                        result = await this.getContentByCombination(resource);
                        if (result?.guid) {
                            analyses.content = result;
                        }
                    } else {
                        result = await this.MessagingHubService.ArtificialIntelligence
                        .matchContent(resource);
                        analyses.content = result;
                    }

                    this.SegmentService.createApplicationTrack({trackEvent: ANALYSE_CONTENT_SUCCESS_TRACKING});
                    resolve(analyses);
                } catch {
                    this.SegmentService.createApplicationTrack({trackEvent: ANALYSE_CONTENT_FAILED_TRACKING});
                    reject();
                }
            }),
        });
    }

    async getLastModel() {
        try {
            const lastTrainedOrPublishedModel = await this.MessagingHubService.ArtificialIntelligence.getLastTrainedOrPublishedModel();
            return lastTrainedOrPublishedModel;
        } catch (e) {
            const exeptionMessage = JSON.parse(e.message);
            let errorMessage;
            switch (exeptionMessage.reason.code) {
                case DEFAULT_PROVIDER_NOT_DEFINED_CODE_ERROR:
                    errorMessage = DEFAULT_PROVIDER_NOT_DEFINED_ERROR;
                    break;
                case MODEL_NOT_FOUND_CODE_ERROR:
                    errorMessage = MODEL_NOT_FOUND_ERROR;
                    break;
                case TRAINING_IN_PROGRESS_CODE_ERROR:
                    errorMessage = TRAINING_IN_PROGRESS_ERROR;
                    break;
                default:
                    errorMessage = GENERIC_ERROR;
                    break;
            }
            await this.displayErrorMessage(errorMessage);
        }
    }

    async onModelError(id: string) {
        await this.displayErrorMessage(GENERIC_ERROR);
        this.analyses = this.analyses.filter(a => a.id !== id);
    }

    async getIntents() {
        return await this.MessagingHubService.ArtificialIntelligence.getIntents();
    }

    async isContentAssistantV2Enabled() {
        return await this.ArtificialIntellingeceConfigurationsService.isV2ConfigurationEnabled(this.isConfigurationsApplicationGetEnabled);
    }

    async getEntities() {
        return await this.MessagingHubService.ArtificialIntelligence.getEntities();
    }

    async displayErrorMessage(errorMessage) {
        const modelError = await this.$translate(errorMessage);
        this.ngToast.warning(modelError);
    }

    onEntityToggle = (toggle: boolean) => {
        this.SegmentService.createApplicationTrack({trackEvent: toggle ? ENTITY_LIST_TRACKING : ENTITY_UNLIST_TRACKING});
    }

    onIntentFocus = () => {
        this.SegmentService.createApplicationTrack({trackEvent: INTENT_LIST_TRACKING});
    }

    onIntentBlur = () => {
        this.SegmentService.createApplicationTrack({trackEvent: INTENT_UNLIST_TRACKING});
    }
    async getContentByCombination(combination: any) {
        try {
            return await this.MessagingHubService.sendCommand({
                method: 'set',
                uri: '/assistant/analysis',
                resource: combination,
                to: '<EMAIL>',
                type: 'application/vnd.iris.ai.content-combination+json',
            });
        } catch (e) {
            if (e.code == 67) {
                return;
            }
        }
    }

    async getTags() {
        try {
            return await this.MessagingHubService.sendCommand({
                method: 'get',
                uri: '/content-tags?$take=100',
                to: '<EMAIL>'
            });
        } catch (e) {
            if (e.code == 67) {
                return;
            }
        }
    }
}
