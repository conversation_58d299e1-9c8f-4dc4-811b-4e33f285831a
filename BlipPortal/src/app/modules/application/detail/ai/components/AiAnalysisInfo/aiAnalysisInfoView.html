<div class="analysis-response">
    <div>
        <vue-component name="blipCard" vprops="{{ $ctrl.messageCardContent }}" ng-if="$ctrl.messageCardContent"/>
    </div>
    <div class="intention">
        <blip-select ng-model="$ctrl.selectedIntention"
            options="$ctrl.structuredIntents"
            label="{{ $ctrl.intentsLabel }}"
            ng-if="$ctrl.structuredIntents.length > 0"
            on-focus="$ctrl.onIntentFocus()"
            on-blur="$ctrl.onIntentBlur()">
        </blip-select>
        <div ng-if="!$ctrl.structuredIntents.length > 0" class="loading-area">
            <loading-icon/>
        </div>
    </div>
    <div class="hidden-content" ng-class="{ 'display': $ctrl.displayHiddencontent }" ng-if="$ctrl.displayEntity">
        <bds-typo class="title">{{ $ctrl.entitiesLabel }}</bds-typo>
        <div ng-repeat="(name, values) in $ctrl.structuredEntities" class="entity">
            <div class="name">
                {{ name }}
            </div>
            <div ng-repeat="value in values" class="value">
                {{ value }}
            </div>
        </div>
    </div>
    <div class="hidden-content divisor" ng-class="{ 'display': $ctrl.displayHiddencontent }" ng-if="$ctrl.displayEntity && $ctrl.displayHiddencontent && $ctrl.analysisResult.content.results.length > 0">
        <bds-typo class="title">
            {{ $ctrl.contentsLabel }}
        </bds-typo>
        <div class="name">
            {{ $ctrl.analysisResult.content.name }}
        </div>
        <div ng-repeat="result in $ctrl.analysisResult.content.results" class="content">
            <div class="value">
                {{ result.Content }}
            </div>
        </div>
    </div>
    <div class="hidden-content divisor" ng-class="{ 'display': $ctrl.displayHiddencontent }" ng-if="$ctrl.displayHiddencontent && $ctrl.analysisResult.content.result.content">
        <bds-typo class="title">
            {{ $ctrl.contentsLabel }}
        </bds-typo>
        <div class="name">
            {{ $ctrl.analysisResult.content.name }}
        </div>
        <div class="value">
            {{ $ctrl.analysisResult.content.result.content }}
        </div>
    </div>
    <div class="expand" ng-click="$ctrl.toggleContent()" ng-if="$ctrl.displayEntity || $ctrl.displayContent">
        <i class="icon icon-arrowdown" ng-if="!$ctrl.displayHiddencontent"></i>
        <i class="icon icon-arrowup" ng-if="$ctrl.displayHiddencontent"></i>
    </div>
</div>
