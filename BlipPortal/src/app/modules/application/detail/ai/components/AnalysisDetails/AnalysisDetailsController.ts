import * as angular from 'angular';

import './AnalysisDetails.scss';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { UsersService } from 'modules/application/detail/users/UsersService';
import { IStateService } from 'angular-ui-router';
import { IWindowService } from 'angular';

const ROOT_LABELS_PATH = 'ai-analysis-details';

const TITLE_LABEL_PATH = `${ROOT_LABELS_PATH}.title`;

const HEADERS_LABELS_PATH = `${ROOT_LABELS_PATH}.headers`;
const PHRASE_LABEL_PATH = `${HEADERS_LABELS_PATH}.phrase`;
const CONFIABILITY_LABEL_PATH = `${HEADERS_LABELS_PATH}.confiability`;
const ENTITIES_LABEL_PATH = `${HEADERS_LABELS_PATH}.entities`;
const ENTITY_LABEL_PATH = `${HEADERS_LABELS_PATH}.entity`;
const CONTENTS_LABEL_PATH = `${HEADERS_LABELS_PATH}.contents`;
const CONTENT_LABEL_PATH = `${HEADERS_LABELS_PATH}.content`;
const INTENT_LABEL_PATH = `${HEADERS_LABELS_PATH}.intent`;
const USER_LABEL_PATH = `${HEADERS_LABELS_PATH}.user`;
const ID_USER_LABEL_PATH = `${HEADERS_LABELS_PATH}.idUser`;
const DATE_LABEL_PATH = `${HEADERS_LABELS_PATH}.date`;

const ENTITES_TABLE_HEADER_LABELS_PATH = `${ROOT_LABELS_PATH}.entitiesTableHeaders`;
const ENTITY_TABLE_LABEL_PATH = `${ENTITES_TABLE_HEADER_LABELS_PATH}.entity`;
const VALUE_TABLE_LABEL_PATH = `${ENTITES_TABLE_HEADER_LABELS_PATH}.value`;

const BUTTONS_LABELS_PATH = `${ROOT_LABELS_PATH}.buttons`;
const SEE_CONVERSATION_LABEL_PATH = `${BUTTONS_LABELS_PATH}.seeConversation`;

const VALUES_LABELS_PATH = `${ROOT_LABELS_PATH}.values`;
const DO_NOT_INDENTIFIED_LABEL_PATH = `${VALUES_LABELS_PATH}.doNotIdentified`;
const UNKNOW_LABEL_PATH = `${VALUES_LABELS_PATH}.unknown`;
const CONFIABILITY_VALUE_LABEL_PATH = `${VALUES_LABELS_PATH}.confiability`;

export class AnalysisDetailsController implements angular.IController {
    private labels: any;

    constructor(
        private $translate: any,
        private close: any,
        private analysis: any,
        private SegmentService: SegmentService,
        private UsersService: UsersService,
        private $state: IStateService,
        private $window: IWindowService
    ) {
        this.$onInit();
    }

    $onInit() {
        this.setLabels();
    }

    async setLabels() {
        const confiabilityMark = '{confiability}';
        const intentNameMark = '{intentName}';
        this.labels = {
            title: await this.$translate(TITLE_LABEL_PATH),
            headers: {
                phrase: await this.$translate(PHRASE_LABEL_PATH),
                confiability: await this.$translate(CONFIABILITY_LABEL_PATH),
                entities: await this.$translate(ENTITIES_LABEL_PATH),
                entity: await this.$translate(ENTITY_LABEL_PATH),
                contents: await this.$translate(CONTENTS_LABEL_PATH),
                content: await this.$translate(CONTENT_LABEL_PATH),
                intent: await this.$translate(INTENT_LABEL_PATH),
                user: await this.$translate(USER_LABEL_PATH),
                idUser: await this.$translate(ID_USER_LABEL_PATH),
                date: await this.$translate(DATE_LABEL_PATH),
            },
            buttons: {
                seeConversation: await this.$translate(
                    SEE_CONVERSATION_LABEL_PATH,
                ),
            },
            entitiesTableHeaders: {
                entity: await this.$translate(ENTITY_TABLE_LABEL_PATH),
                value: await this.$translate(VALUE_TABLE_LABEL_PATH),
            },
            values: {
                doNotIdentified: await this.$translate(
                    DO_NOT_INDENTIFIED_LABEL_PATH,
                ),
                unknown: await this.$translate(UNKNOW_LABEL_PATH),
                confiability: (await this.$translate(
                    CONFIABILITY_VALUE_LABEL_PATH,
                ))
                    .replace(confiabilityMark, this.analysis.percentage)
                    .replace(intentNameMark, this.analysis.intentionName),
            },
        };
    }

    trackSeeConversationAndGoToUserDetails() {
        this.trackSeeConversationOnEnhancementPage();
        const identity = this.analysis.userIdentity;
        if (!identity) {
            return;
        }
        const id = this.UsersService.getDecodedUserIdentity(identity);
        const url = this.$state.href('auth.application.detail.users.user', {
            id,
        });
        this.$window.open(url, '_blank');
    }

    trackSeeConversationOnEnhancementPage() {
        this.SegmentService.createTrack('ai-nlp-enhancement-analysis-seeconversation');
    }

    closesSidebar = () => this.close();
}
