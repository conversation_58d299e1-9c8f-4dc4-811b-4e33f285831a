import * as angular from 'angular';

import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { ToastService, ToastType } from 'modules/application/misc/ToastService';
import './FixIntent.scss';
import foldTo<PERSON>CII from 'data/foldToASCII';

const FIXED_FEEDBACK_STATUS = 'fixed';

const ROOT_LABELS_PATH = 'ai-fix-intent';

const TITLE_LABEL_PATH = `${ROOT_LABELS_PATH}.title`;
const DESCRIPTION_LABEL_PATH = `${ROOT_LABELS_PATH}.description`;

const HEADERS_LABELS_PATH = `${ROOT_LABELS_PATH}.headers`;
const SELECTED_PHRASE_LABEL_PATH = `${HEADERS_LABELS_PATH}.selectedPhrases`;
const PHRASE_LABEL_PATH = `${HEADERS_LABELS_PATH}.phrase`;
const INTENT_LABEL_PATH = `${HEADERS_LABELS_PATH}.intent`;

const BUTTONS_LABELS_PATH = `${ROOT_LABELS_PATH}.buttons`;
const APPLY_CHANGES_LABEL_PATH = `${BUTTONS_LABELS_PATH}.applyChanges`;

const ERRORS_MESSAGES_PATH = `${ROOT_LABELS_PATH}.errors`;
const NOT_SAVED_MESSAGE_PATH = `${ERRORS_MESSAGES_PATH}.notSaved`;
const FILL_PROPRIATELY_MESSAGE_PATH = `${ERRORS_MESSAGES_PATH}.fillPropriately`;
const CHOOSE_ANALYSIS_MESSAGE_PATH = `${ERRORS_MESSAGES_PATH}.chooseAnalysis`;

const SUCCESS_MESSAGES_PATH = `${ROOT_LABELS_PATH}.success`;
const CHANGES_APPLIED_MESSAGE_PATH = `${SUCCESS_MESSAGES_PATH}.changesApplied`;

export class FixIntentController implements angular.IController {
    public labels: any;
    public selectedIntentId: string;
    public fixIntentForm: any;
    public searchableIntents: any[];

    constructor(
        private $translate: any,
        private close: any,
        private MessagingHubService: MessagingHubService,
        private LoadingService: any,
        private ToastService: ToastService,
        public selectedAnalyses: any[],
        public intents: any[],
    ) {
        this.$onInit();
    }

    $onInit() {
        if (!this.selectedAnalyses.length) {
            this.ToastService.toast(
                ToastType.Danger,
                CHOOSE_ANALYSIS_MESSAGE_PATH,
            );
        }
        this.selectedIntentId = this.selectedAnalyses[0].intention.id;
        this.setLabels();
        this.searchableIntents = this.intents.map(({ id, name }) => ({ label: name, value: id }));
    }

    async setLabels() {
        this.labels = {
            title: await this.$translate(TITLE_LABEL_PATH),
            description: await this.$translate(DESCRIPTION_LABEL_PATH),
            headers: {
                phrase: await this.$translate(PHRASE_LABEL_PATH),
                selectedPhrases: await this.$translate(
                    SELECTED_PHRASE_LABEL_PATH,
                ),
                intent: await this.$translate(INTENT_LABEL_PATH),
            },
            buttons: {
                applyChanges: await this.$translate(APPLY_CHANGES_LABEL_PATH),
            },
        };
    }

    deleteAnalysis = index => this.selectedAnalyses.splice(index, 1);

    loadTerms({ query, items }) {
        const search = foldToASCII(query.toLowerCase());

        return items.filter(x =>
            foldToASCII(x.value.toLowerCase()).includes(search),
        );
    }

    closesSidebar = async (hasChange: boolean = false) => await this.close(hasChange);

    async applyIntentFix() {
        if (
            !this.fixIntentForm.$invalid &&
            this.selectedIntentId &&
            this.selectedAnalyses.length
        ) {
            try {
                this.LoadingService.startLoading(false);

                const analysesFeedback = this.selectedAnalyses.map(analysis => ({
                    analysisId: analysis.id,
                    text: analysis.text,
                    feedback: FIXED_FEEDBACK_STATUS,
                    intentionId: this.selectedIntentId,
                }));

                await this.MessagingHubService.ArtificialIntelligence.setAnalysesFeedback(
                    analysesFeedback,
                );
                this.ToastService.toast(
                    ToastType.Success,
                    CHANGES_APPLIED_MESSAGE_PATH,
                );
                this.closesSidebar(true);
            } catch {
                this.ToastService.toast(
                    ToastType.Danger,
                    NOT_SAVED_MESSAGE_PATH,
                );
            } finally {
                this.LoadingService.stopLoading();
            }
        } else {
            this.ToastService.toast(
                ToastType.Danger,
                FILL_PROPRIATELY_MESSAGE_PATH,
            );
        }
    }
}
