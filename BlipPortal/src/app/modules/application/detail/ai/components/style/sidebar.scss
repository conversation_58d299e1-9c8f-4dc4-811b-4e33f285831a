@import '~blip-toolkit/dist/scss/variables/module';
@import '~blip-toolkit/dist/scss/mixins/module';
@import '~blip-toolkit/dist/scss/utilities/module';
@import '~assets/scss/main.scss';

$header-bar-size:140px;

#ai-sidebar {
    position: fixed;
    background-color: $color-surface-1;
    box-shadow: 0 0px 20px 0px #00000052;

    // remove those coments to make it fits inside iframe
    // top: $header-bar-size;
    // height: calc(100% - #{$header-bar-size});

    .sidebar-header {
        width: 100%;
        height: 62px;
        background-color: $color-surface-4;
        display: flex;
        align-items: center;
        padding: 0 2.4*$m;

        .sidebar-title {
            color: $color-surface-2;
        }

        .actions {
           margin: auto 0 auto auto;
        }
    }

    .sidebar-body {
        padding: 1.5*$m 2.4*$m;
        overflow-y: auto;
        max-height: 100%;
        height: calc(100% - 62px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &::-webkit-scrollbar {
            width: 1 * $u;
            height: 1 * $u;
        }

        &::-webkit-scrollbar-track {
            background: none;
        }

        .block-header {
            @extend .bp-fw-extra-bold;
            @extend .bp-lh-plus;

            color: $color-content-default;
        }

        .block-content {
            color: $color-content-disable;
        }

        .block {
            margin-bottom: 20px;
        }

        .unique-button {
            position: absolute;
            height: 62px;
            width: 100%;
            bottom: 0;
            right: 0;
        }
    }
}

.icon-close{
    padding-top: 6px;
    color: $bp-color-cloud
}
