<div class="modal">
    <div class="modal-overlay" ng-click="$ctrl.close()"></div>
    <div class="modal-dialog modal-sm modal-large-max layout-center-y">
        <div class="modal-toolbar">
            <button ng-click="$ctrl.close()" type="button" class="no-style">
                <i class="icon-close bp-c-rooftop"></i>
            </button>
        </div>
        <div class="modal-body row">
            <div class="eight columns offset-by-two">
                <div class="mh6 mb4-1">
                    <bds-typo variant="fs-32" class="mb3" translate>modules.application.detail.ai.intention.information</bds-typo>
                </div>
                <form-field>
                    <material-input initial-value="$ctrl.analysisDate">
                        <input type="text" name="date" ng-model="$ctrl.analysisDate" readonly>
                        <bds-typo for="date" translate>utils.misc.date</bds-typo>
                    </material-input>
                </form-field>
                <form-field>
                    <material-input initial-value="$ctrl.nameAndScore">
                        <input type="text" name="reliability" ng-model="$ctrl.nameAndScore" readonly>
                        <bds-typo for="reliability" translate>modules.application.detail.ai.intention.intentionReliability</bds-typo>
                    </material-input>
                </form-field>
                <form-field>
                    <material-input initial-value="$ctrl.analysis.text">
                        <input type="text" name="reliability" ng-model="$ctrl.analysis.text" readonly>
                        <bds-typo for="reliability" translate>modules.application.detail.ai.intention.userPhrase</bds-typo>
                    </material-input>
                </form-field>
                <form-field>
                    <material-input initial-value="$ctrl.entities">
                        <input type="text" name="entities" ng-model="$ctrl.entities" readonly>
                        <bds-typo for="entities" translate>modules.application.detail.ai.entities.title</bds-typo>
                    </material-input>
                </form-field>
            </div>
        </div>
        <div class="modal-footer">
            <button class="bp-btn bp-btn--bot bp-btn--small" ng-click="$ctrl.close()" type="button" translate>utils.forms.close</button>
        </div>
    </div>
</div>
