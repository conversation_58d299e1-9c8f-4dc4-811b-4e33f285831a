@import '~blip-toolkit/dist/scss/variables/module';
@import '../../../../../../assets/scss/layout/module';
@import '../../../../../../assets/scss/mixins';

textarea {
    background: none;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    height: auto;
    min-height: 1.9375rem;
    overflow: auto;
    resize: none;
}

.sidebar-helper-header__actions .icon-tag {
    color: var(--color-surface-2, #e0e0e0);
    height: 24px;
}

.filter-tag {
    z-index: 999;
    margin-top: -15px;
    padding-bottom: 15px;
    opacity: 0;
    transition: opacity 0.9s linear;
    -webkit-transition: opacity 0.9s linear;
    -moz-transition: opacity 0.9s linear;
    -o-transition: opacity 0.9s linear;
    -ms-transition: opacity 0.9s linear;
}

:host(.bds-icon) svg {
    fill: currentColor;
    width: 100%;
    min-width: 100%;
}

.message-area {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.test-message-thread {
    width: 100%;
    height: 100%;
    padding: 30px;
    padding-bottom: 65px;
    overflow-y: scroll;
    background-color: $color-surface-1;

    & .highlight {
        color: $color-surface-1;
        text-decoration: underline;
        font-weight: bold;
    }

    &::-webkit-scrollbar {
        display: none;
    }
}

#chat-container {
    height: calc(100% - 75px);
}

// Angular transitions
/* start 'enter' transition */
[ui-view="content"].ng-enter {
    /* transition on enter for .5s */
    transition: 1s;
    /* start with opacity 0 (invisible) */
    opacity: 0;
}

/* end 'enter' transition */
[ui-view="content"].ng-enter-active {
    /* end with opacity 1 (fade in) */
    opacity: 1;
}

/* Modals and content entrances */
.right-entrance-animation {
    @include transition( all 0.50s ease-in-out);

    &.ng-enter {
        margin-right: -3.0*$m;
        opacity: 0;
    }

    &.ng-enter.ng-enter-active,
    &.ng-show {
        margin-right: 0;
        opacity: 1;
    }

    &.ng-leave {
        margin-right: 0;
        opacity: 1;
    }

    &.ng-leave.ng-leave-active,
    &.ng-hide {
        visibility: hidden;
        margin-right: -3.0*$m;
        opacity: 0;
    }
}

.sidebar-content-component {
    position: absolute;
    top: 0;
    width: 445px;
    height: 100%;
    z-index: 1001;
    overflow: auto;
    overflow-x: hidden;

    @include scrollbar($color-content-ghost);

    &.position-right {
        right: 0;
    }

    &.position-left {
        left: 0;
    }
}

.real-time-message-bar.sidebar-content-component {
    overflow: hidden;
    box-shadow: 0 0px 20px 0px #00000052;

    .sidebar-content-header {
        height: 125px;

        .sidebar-helper-header__top {
            height: 31px;

            .realtime-close {
                height: 31px;
            }
        }
    }

    .thread-body {
        height: calc(100% - 125px);

        .messages {
            height: 100%;
            width: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 1 * $m 2 * $m 4 * $m 3 * $m;
        }
    }

    .collection .slideshow-container .slideshow-list {
        padding-left: 0;
        padding-right: 30px;
    }
}

.sidebar-helper-header {
    .list-divider {
        height: 0.1*$m;
        margin: 0 auto;
        width: 100%;
        margin-right: 4.1*$m;
    }

    .sidebar-helper-header__actions {
        .thread-button {
            width: 3.1*$m;
            height: 3.1*$m;
            cursor: pointer;

            &.thread-button-disabled {
                pointer-events: none;
                opacity: 0.5;
            }

            .transfer-icon,
            .check-icon {
                display: inline-block;
                width: 1.1*$m;
                height: 1.4*$m;
            }
        }

        .icon-close {
            height: 16px;
            padding: 0;
            color: $color-surface-2;
        }
    }
}

.builder-sidebar {
    material-input .material-wrapper input {
        color: #fff;
    }

    .sidebar-content-header {
        padding-top: 20px;
        position: relative;
    }
}

.sidebar-helper-header {
    position: relative;

    &__actions {
        position: absolute;
        top: 0;
        right: 0;

        span {
            margin-left: 10px;
            display: inline-block;
        }
    }
}

input[type='text'].sidebar-edit-input {
    border-bottom: 0;
}

#sidebar-title {
    color: $color-surface-2;
    padding: 0 50px 5px 0;
    margin-bottom: 13px;
    border-bottom: none;

    &[readonly] {
        background: transparent;
        border-bottom: none;
    }
}
