@import "~assets/scss/variables";
@import "~blip-ds/dist/collection/styles/_colors.scss";

.analysis-response {
    background-color: $color-surface-2;

    margin-bottom: 15px;
    border-radius: 15px;
    padding-top: 15px;

    & > div {
        padding: 0 20px;
    }

    & > .expand {
        padding: 5px 0;

        & > i {
            left: 50%;
            position: relative;
            transform: translate(-50%);
            font-size: 0.625rem;
        }
    }

    & .intention {
        padding-bottom: 15px;
    }

    & .loading-area {
        display: flex;
        justify-content: center;
    }
}

.hidden-content {
    height: 0;
    overflow: hidden;
    border-top: $color-content-ghost dashed 1.5px;

    &.display {
        height: auto;
    }

    & > div {
        margin-top: 15px;
        font-size: 15px;

        & .name {
            font-weight: bold;
        }

        & .value {
            margin-left: 15px;
        }

        & .title {
            font-size: 16px;
        }
    }
}

.hidden-content .name {
    font-weight: bold;
}

.divisor {
    padding-top: 10px !important;
    margin-top: 12px;
}