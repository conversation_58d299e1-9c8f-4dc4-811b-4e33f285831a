@import '../style/sidebar.scss';

#confiability-value-content{
    display: flex;
    align-items: center;
    padding: 20px;
    @extend .bp-fs-7;
    background-color: $color-surface-2;

    &:hover{
        box-shadow: 0 2px 20px 0 rgba(0, 0, 0, 0.05)
    }

    #confiability-progress-bar{
        position: relative;

        #confiability-progress-bar-text{
            top: 50%;
            bottom: auto;
            left: 50%;
            transform: translateY(-50%) translateX(-50%);
            position: absolute;
            @extend .block-header;
            @extend .bp-fs-6;
        }

        circle {
            stroke-width: 4 !important;
        }

        path {
            stroke: $color-brand !important;
        }
    }
    #confiability-text{
        padding-left: 10px;
        color: $color-content-default;
    }
}

#user-header {
    @extend .bp-c-bot;
}

.track-conversations-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 56px;
    align-self: center;
}
