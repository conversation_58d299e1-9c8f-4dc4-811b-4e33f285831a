<bds-paper id="node-content-tab" class="chatbot-test-bar sidebar-content-component right-entrance-animation position-right builder-sidebar fixed z-max">
    <div class="sidebar-content-header background-text-dark-header ph5 pt2">
        <div class="sidebar-helper-header">
            <input class="w-100 sidebar-title" id="sidebar-title" maxlength="50" type="text" name="nodeName" ng-readonly="true"
                value="{{'ai-publication.testSidbar.testModel' | translate}}">
            <div class="sidebar-helper-header__actions" style="margin-top: -8px;">
                <span ng-click="$ctrl.openFilterTag()" ng-if="$ctrl.V2Enabled" style="margin-top: 8px;">
                    <bds-tooltip position="bottom-center" tooltip-text="{{'ai-publication.testSidbar.tootip' | translate}}">
                        <bds-icon class="icon-tag" name="tag" />
                    </bds-tooltip>
                </span>
                <span ng-click="$ctrl.close()">
                    <i class="icon-close icon-close-header"></i>
                </span>
            </div>
        </div>
    </div>
    <div id="chat-container">
        <div class="test-message-thread">
            <bds-card id="card-filter" style="display: none;" class="filter-tag" clickable="true" width="100%" ng-if="$ctrl.V2Enabled">
                <bds-card-header align='space-between'>
                    <bds-typo variant="fs-20" tag="h4" bold="bold" margin="false" translate>
                        {{'ai-publication.testSidbar.filter' }} 
                    </bds-typo>
                </bds-card-header>
                <bds-select-chips 
                    class="mt-12"
                    id="new-chips-tags"
                    label="{{'ai-publication.testSidbar.subtitle' | translate}}"
                    placeholder="{{'ai-publication.testSidbar.placeholder' | translate}}"
                    custom-events="[{ event:'bdsChangeChips', cb: $ctrl.onChangeItem }]"
                    options="{{$ctrl.tagValues}}"
                    can-add-new="false"
                >
                </bds-select-chips>
                </bds-card>
            <ai-analysis-info
                ng-repeat="analysis in $ctrl.analyses"
                id="analysis_{{ analysis.id }}"
                analysis="analysis"
                entities="$ctrl.entities"
                intents="$ctrl.intentions"
                intents-label="{{ $ctrl.intentsLabel }}"
                entities-label="{{ $ctrl.entitiesLabel }}"
                contents-label="{{ $ctrl.contentsLabel }}"
                on-intent-focus="$ctrl.onIntentFocus()"
                on-intent-blur="$ctrl.onIntentBlur()"
                on-entity-toggle="$ctrl.onEntityToggle($toggle)"
                on-error="$ctrl.onModelError($id)"
            />
        </div>
        <message-area
            class="message-area"
            placeholder="{{ $ctrl.placeholder }}"
            on-submit="$ctrl.messageSubmit($content)"
            disabled="!$ctrl.canSendMessage"
        >
        </message-area>
    </div>
</bds-paper>
