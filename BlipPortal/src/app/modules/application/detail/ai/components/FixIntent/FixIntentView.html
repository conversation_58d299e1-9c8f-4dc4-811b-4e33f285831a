<div id="ai-sidebar" class="sidebar-content-component position-right right-entrance-animation">
    <div class="sidebar-header">
        <div class="sidebar-title">
            <bds-typo>{{ $ctrl.labels.title }}</bds-typo>
        </div>
        <div class="actions">
            <span ng-click="$ctrl.closesSidebar()">
                <i class="icon-close"></i>
            </span>
        </div>
    </div>
    <div class="sidebar-body">

        <form name="$ctrl.fixIntentForm" novalidate>
            <bds-typo class="block">
                {{ $ctrl.labels.description }}
            </bds-typo>
            <div class="block">
                <blip-select ng-model="$ctrl.selectedIntentId" mode="autocomplete" options="$ctrl.searchableIntents"
                    required="true" invalid="!$ctrl.selectedIntentId"/>
            </div>
            <div class="block">
                <bds-typo class="block-header">
                    {{ $ctrl.labels.headers.selectedPhrases }}
                </bds-typo>
                <div class="block-content flex items-center pv2" ng-repeat="(index, analysis) in $ctrl.selectedAnalyses">
                    <blip-input-dpr class="w-100" field-name="{{'analysisText'+index}}" parent-form="$ctrl.fixIntentForm"
                        required type="text" ng-model="analysis.text" label="{{$ctrl.labels.headers.phrase}}">
                    </blip-input-dpr>
                    <span ng-click="$ctrl.deleteAnalysis(index)">
                        <i class="icon-close pl3"></i>
                    </span>
                </div>
            </div>
            <button type="button" class="bp-btn bp-btn--bot unique-button" ng-click="$ctrl.applyIntentFix()"
                ng-disabled="$ctrl.fixIntentForm.$invalid || !$ctrl.selectedAnalyses.length || !$ctrl.selectedIntentId">
                {{ $ctrl.labels.buttons.applyChanges }}
            </button>
        </form>
    </div>
</div>
