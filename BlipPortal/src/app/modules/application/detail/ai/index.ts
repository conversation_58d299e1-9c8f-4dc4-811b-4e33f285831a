import * as angular from 'angular';
import 'angular-ui-router';
import <PERSON><PERSON>ie<PERSON> from './AiView.html';
import { Ai<PERSON>ontroller } from './AiController';
import { ModelTestProcessor } from './processors/ModelTestProcessor';
import { ConfigurationsService } from '../../misc/ConfigurationsService';
import { AiAgentService } from '../../misc/AiAgentService';
import { IStateProvider } from 'angular-ui-router';
import { AnalysisDetailsProcessor } from './processors/AnalysisDetailsProcessor';
import { FixIntentProcessor } from './processors/FixIntentProcessor';
import { AnalysisModalProcessor } from './processors/AnalysisModalProcessor';
import { AiAnalysisInfoComponent } from './components/AiAnalysisInfo/aiAnalysisInfo.component';

export const aiStateName = 'auth.application.detail.ai';
export const artificialIntelligenceStateName = 'auth.application.detail.artificialIntelligence';
export const providersStateName = 'auth.application.detail.ai.providers';
export const answersStateName = 'auth.application.detail.ai.answers';
export const dialogflowStateName = 'auth.application.detail.ai.providers.dialogflow';
export const watsonStateName = 'auth.application.detail.ai.providers.watson';
export const luisStateName = 'auth.application.detail.ai.providers.luis';

//Sidenav
export default angular
    .module('ai', ['ui.router', 'shared', 'app.constants'])
    .service('ConfigurationsService', ConfigurationsService)
    .service('ModelTestProcessor', ModelTestProcessor)
    .service('AnalysisModalProcessor', AnalysisModalProcessor)
    .service('AnalysisDetailsProcessor', AnalysisDetailsProcessor)
    .service('FixIntentProcessor', FixIntentProcessor)
    .component('aiAnalysisInfo', AiAnalysisInfoComponent)
    .config(async ($stateProvider: IStateProvider) => {
        'ngInject';

        $stateProvider
            .state(artificialIntelligenceStateName, {
                url: '/artificial-intelligence/{path:any}',
                reloadOnSearch: false,
                views: {
                    '<EMAIL>': {
                        template: AiView,
                        controller: AiController,
                        controllerAs: '$ctrl'
                    }
                },
                resolve: {
                    checkLayout: AiAgentService.blockAiAgentAccess
                },
                data: {
                    hasOwnFooter: true,
                }
            });
    }).name;
