import { LoadingService } from 'modules/ui/LoadingService';
import TranslateService from 'modules/translate/TranslateService';
import {
    blipWebsocketHostName,
    messaginghubWebsocketHostName,
    blipWebsocketHostNameTenant,
    aiAgentsSettingsSource,
} from 'app.constants';
import { messaginghubWebsocketHostNameTenant } from 'app.constants';
import {
    AccountService2,
} from 'modules/account/AccountService2';
import { Application } from 'modules/shared/ApplicationTypings';

export interface WebSocketParams {
    messaginghubWebsocketHostNameTenant: string;
    messaginghubWebsocketHostName: string;
    blipWebsocketHostName: string;
    blipWebsocketHostNameTenant: string;
}

export class AiAgentsController {
    language: string;
    ownerIdentity: string;
    settingsSource: string;
    canEditDesk: boolean;
    webSocketParams: WebSocketParams;
    user: string;

    constructor(
        private LoadingService: LoadingService,
        private TranslateService: TranslateService,
        private application: Application,
        private AccountService2: AccountService2,
    ) {
        this.LoadingService.startLoading();
        const setProps = async () => {
            const account = await this.AccountService2.me();
            this.language = this.TranslateService.getCurrentLanguage();
            this.ownerIdentity = this.application.shortName;
            this.webSocketParams = {
                messaginghubWebsocketHostNameTenant,
                messaginghubWebsocketHostName,
                blipWebsocketHostName,
                blipWebsocketHostNameTenant,
            };
            this.LoadingService.stopLoading();
            this.settingsSource = aiAgentsSettingsSource;
            this.user = account.identity;
        };
        setProps();
    }
}
