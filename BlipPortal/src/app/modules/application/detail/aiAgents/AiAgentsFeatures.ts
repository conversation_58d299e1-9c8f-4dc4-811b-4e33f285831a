import { FeatureToggleClientService } from 'feature-toggle-client';

const isAiAgentsAnalyticsMenuEnabled = 'ai-agents-analytics-menu';
const enableAiAgentNewSettings = 'enable_new_settings_ai_agent';
const aiAgentSkillsEnabled = 'ai-agent-skills-enabled';

export class AiAgentsFeatures {
    static isAiAgentsAnalyticsMenuEnabled(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isAiAgentsAnalyticsMenuEnabled,
        );
    }

    static isEnableAiAgentNewSettings(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            enableAiAgentNewSettings,
            false,
        );
    }

    static isAiAgentSkillsEnabled(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isUserFeatureEnabled(
            aiAgentSkillsEnabled,
            false,
        );
    }
}
