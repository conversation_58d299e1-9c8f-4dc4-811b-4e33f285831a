import * as angular from 'angular';
import 'angular-ui-router';
import AiAgentsView from './AiAgentsView.html';
import { AiAgentsController } from './AiAgentsController';
import { IStateProvider } from 'angular-ui-router';
import { aiAgentsScriptSource } from 'app.constants';
import { MicroFrontendService } from 'modules/shared/MicroFrontendService';
import { AiAgentsFeatures } from './AiAgentsFeatures';
import { applicationListStateName } from 'modules/application/list';

export const aiAgentsStateName = 'auth.application.detail.aiAgents';
export const aiAgentsHomeStateName = 'auth.application.detail.aiAgents.home';
export const aiAgentsAnalyticsStateName =
    'auth.application.detail.aiAgents.analytics';
const aiAgentsKnowledgeBaseStateName =
    'auth.application.detail.aiAgents.knowledgeBase';
const aiAgentsCreateStateName = 'auth.application.detail.aiAgents.create';
const aiAgentsProfileStateName = 'auth.application.detail.aiAgents.profile';
const aiAgentsRedirectStateName = 'auth.application.detail.aiAgents.redirect';
const aiAgentsIntegrationsStateName =
    'auth.application.detail.aiAgents.integrations';
const aiAgentsSkillsStateName = 'auth.application.detail.aiAgents.skills';
const aiAgentsConversationalExperienceStateName =
    'auth.application.detail.aiAgents.conversationalExperience';

export const aiAgents = angular
    .module('aiAgents', ['ui.router', 'shared'])
    .config(($stateProvider: IStateProvider) => {
        'ngInject';
        $stateProvider
            .state(aiAgentsStateName, {
                url: '/ai-agents',
                redirectTo: aiAgentsHomeStateName,
                views: {
                    '<EMAIL>': {
                        controller: AiAgentsController,
                        controllerAs: '$ctrl',
                        template: AiAgentsView,
                    },
                },
                onEnter: () => {
                    return MicroFrontendService.addSource(aiAgentsScriptSource);
                },
            })
            .state(aiAgentsHomeStateName, {
                url: '/home',
            })
            .state(aiAgentsKnowledgeBaseStateName, {
                url: '/knowledge-base',
            })
            .state(aiAgentsAnalyticsStateName, {
                url: '/analytics',
            })
            .state(aiAgentsCreateStateName, {
                url: '/create',
            })
            .state(aiAgentsProfileStateName, {
                url: '/profile',
            })
            .state(aiAgentsRedirectStateName, {
                url: '/redirect',
            })
            .state(aiAgentsIntegrationsStateName, {
                url: '/integrations',
            })
            .state(aiAgentsSkillsStateName, {
                url: '/skills/{path:any}',
                resolve: {
                    allowToAcessSkillsPath,
                },
            })
            .state(aiAgentsConversationalExperienceStateName, {
                url: '/conversational-experience/{path:any}',
            });
    }).name;

const allowToAcessSkillsPath = async $state => {
    const isSkillsEnabled = await AiAgentsFeatures.isAiAgentSkillsEnabled();

    if (!isSkillsEnabled) {
        $state.go(applicationListStateName);
    }
};
