import { blipDomainUrl, blipWebsocketHostName, blipWebsocketHostNameTenant } from 'app.constants';
import { UserBlipAccount, AccountService2 } from 'modules/account/AccountService2';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { LoadingService } from 'modules/ui/LoadingService';
import {
    ContextProcessorService,
    Contexts
} from 'modules/core/ContextProcessorService';
import { ISCEService } from 'angular';

export interface WebSocketParams {
    blipDomainUrl: string;
    blipWebsocketHostName: string;
    blipWebsocketHostNameTenant: string;
}

export class DataExtractorController {
    public user: UserBlipAccount;
    public webSocketParams: WebSocketParams;
    public isOnTenantContext: boolean;
    public frameUrl: string;

    constructor(
        private AccountService2: AccountService2,
        private ContextProcessorService: ContextProcessorService,
        private DATA_EXTRACTOR_FRAME_URL: string,
        private DATA_EXTRACTOR_REDIRECT_URL: string,
        private LoadingService: LoadingService,
        private $sce: ISCEService
    ) {
        this.user = this.AccountService2.meSync();

        this.webSocketParams = {
            blipDomainUrl,
            blipWebsocketHostName,
            blipWebsocketHostNameTenant
        };
        this.LoadingService.startLoading();
        this.checkTenantContext(ContextProcessorService);
    }

    checkTenantContext = async (ContextProcessorService: ContextProcessorService) => {
        await ContextProcessorService.waitForInitialization();
        const tenantId = this.ContextProcessorService.tenant.id;
        const tenantPlan = await this.AccountService2.getTenantPlan(tenantId);
        if (tenantPlan.name === 'standard') {
            window.location.replace(this.DATA_EXTRACTOR_REDIRECT_URL);
        } else {
            this.frameUrl = this.$sce.trustAsResourceUrl(this.DATA_EXTRACTOR_FRAME_URL);
            this.LoadingService.stopLoading();
        }
    }
}
