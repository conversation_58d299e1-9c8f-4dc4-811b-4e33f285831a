import { LoadingService } from 'modules/ui/LoadingService';
import {
    UserBlipAccount,
    AccountService2,
} from 'modules/account/AccountService2';
import TranslateService from 'modules/translate/TranslateService';
import { ApplicationService2 } from 'modules/application/ApplicationService2';
import {
    goodDataSettingsSource,
    blipDomainUrl,
    blipWebsocketHostName,
    blipWebsocketHostNameTenant,
    messaginghubWebsocketHostName,
    messaginghubWebsocketHostNameTenant,
} from 'app.constants';
import { FeatureToggleClientService } from 'feature-toggle-client';
import { BuilderService as BuilderServiceFromSubmodule } from 'modules/application/detail/portal-submodule-builder/templates/builder/services/BuilderService';
import { BuilderService } from 'modules/application/detail/templates/builder/services/BuilderService';
import AuthenticationService from 'modules/login/AuthenticationService';
import { MessagingHubFeatures } from 'modules/messaginghub/MessagingHubFeatures';

export class GoodDataController {
    public settingsSource: string = goodDataSettingsSource;
    public blipDomainUrl: string = blipDomainUrl;
    public blipWebsocketHostName: string = messaginghubWebsocketHostNameTenant;
    public blipWebsocketHostNameTenant: string = messaginghubWebsocketHostNameTenant;
    public language: string;
    public user: UserBlipAccount;
    public botIdentity: string;
    public authToken: string;
    public portalDomain: string;
    public readyToRender: boolean = false;
    public featureFlags: {};
    public isAutomaticTrackingPublished: boolean = false;
    public isMasterApplication: boolean = false;
    public authEmail: string;
    public page: string;
    public params?: string;

    constructor(
        private LoadingService: LoadingService,
        private TranslateService: TranslateService,
        private AccountService2: AccountService2,
        private AuthenticationService: AuthenticationService,
        private ApplicationService2: ApplicationService2,
        private BuilderService: BuilderService|BuilderServiceFromSubmodule,
    ) { }

    public async $onInit() {
        const useDelegation = await MessagingHubFeatures.GetMessagingHubCommandWithDelegation();
        this.featureFlags = FeatureToggleClientService.getInstance().getUserInstance().allFlags();
        this.user = await this.AccountService2.me();
        this.language = this.TranslateService.getCurrentLanguage();

        const account = await this.ApplicationService2.getAccount();
        this.botIdentity = account.identity;

        const application = await this.ApplicationService2.getApplication(this.botIdentity.split('@')[0]);
        this.isMasterApplication = this.ApplicationService2.isThisMasterApplication(application);

        this.authToken = useDelegation ? this.AuthenticationService.token : application.accessKey;
        this.blipWebsocketHostName = useDelegation ? blipWebsocketHostName : messaginghubWebsocketHostName;
        this.blipWebsocketHostNameTenant = useDelegation ? blipWebsocketHostNameTenant : messaginghubWebsocketHostNameTenant;

        try {
            this.isAutomaticTrackingPublished = await this.BuilderService.isAutomaticTrackingPublished();
        } catch {
            this.isAutomaticTrackingPublished = false;
        }

        this.authEmail = this.AuthenticationService.email;
        this.LoadingService.stopLoading();
        this.readyToRender = true;
    }
}
