@import '~blip-ds/dist/collection/styles/colors';
#reports-id {
  
  .truncate {
    width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .description {
    color: $color-content-default;
  }
  
  .label {
    color: $color-content-disable;
  }
  .icons {
    color: $color-content-default;
  }
  .cards {
    display: flex;
    width: 100%;
    flex-direction: row;
    margin-bottom: 5px;
    background-color: $color-surface-1 !important;
    padding: 25px;
    white-space: nowrap;
    justify-content: space-between;
  
    .card-icons {
      color: $color-content-default;
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      align-items: center;
      
      padding: 5px;
      margin-left: 5px;
      height: 35px;
  
       * {
          margin-left: 1.5 ;
          cursor: pointer;
      }
  
      &--hidden {
          visibility: hidden;
          opacity: 0;
          transition: opacity 0.5s linear;
      }
    }
    &:hover .card-icons {
        &--hidden {
            visibility: visible;
            opacity: 1;
            font-size: large;
        }
    }
  }
}