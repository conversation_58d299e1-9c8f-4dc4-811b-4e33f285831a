import foldToASCI<PERSON> from 'data/foldToASCII';
import * as angular from 'angular';
import { AccountService2 } from 'modules/account/AccountService2';
import { LoadingService } from 'modules/ui/LoadingService';
import * as moment from 'moment';
import { ConfirmationModal } from 'modules/ui';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { IStateService } from 'angular-ui-router';
import { analyticsReportStateName, reportStateName } from '..';
import { sortByModifiedAt } from 'modules/analytics/Utils';
import { ViewReport } from 'modules/analytics/models';
import { AnalyticsReportsService } from '../services/AnalyticsReportsService';
import { AnalyticsFeatures } from '../AnalyticsFeatures';
import { EVENTS } from '../utils/AnalyticsConstants';
import './Reports.scss';

export class ReportsController {
    search: any;
    searchUsed: boolean;
    bucketReportsCopy: { reports: ViewReport[] };
    untitled: any;
    email: string;
    public bucketReports: { reports: ViewReport[] };
    public helperLink: object;
    isDisplayingAnalyticsTabs: boolean = false;

    constructor(
        private $translate,
        private $rootScope,
        private ngToast,
        private ModalService,
        private AccountService2: AccountService2,
        private $state: IStateService,
        private LoadingService: LoadingService,
        private SegmentService: SegmentService,
        private AnalyticsReportsService: AnalyticsReportsService,
        private DEFAULT_PAGE_SIZE: number,
    ) {
        this.untitled = this.$translate.instant('utils.misc.untitled');
    }

    async $onInit() {
        await this.checkFeatures();
        const { email } = await this.AccountService2.me();
        this.email = email;
        this.setHelper();
        this.getReports();
        this.isDisplayingAnalyticsTabs && this.listenForReportsUpdates();
    }

    async getReports() {
        const resource = await this.getAllAnalyticsReports();

        const { reports } = resource;
        this.bucketReports = {
            reports: reports
                .reduce((array, report) => {
                    if (report.isPrivate) {
                        return report.owner.email === this.email
                            ? [this.handleReport(report, this.email), ...array]
                            : array;
                    }
                    return [this.handleReport(report, this.email), ...array];
                }, [])
                .sort(sortByModifiedAt),
        };
    }

    async getAllAnalyticsReports() {
        try {
            this.LoadingService.startLoading();

            const userIdentity = `${encodeURIComponent(this.email)}@blip.ai`;
            let count = 0;
            const analyticsReports = [];

            while (true) {
                const { reports } = await this.AnalyticsReportsService.getMany(
                    userIdentity,
                    count,
                    this.DEFAULT_PAGE_SIZE,
                );
                analyticsReports.push(...reports);

                count += this.DEFAULT_PAGE_SIZE;
                if (reports.length < this.DEFAULT_PAGE_SIZE) {
                    break;
                }
            }

            return { reports: analyticsReports };
        } catch (error) {
            if (error.code == 67) {
                return { reports: [] };
            }
            const errorGettingReports = await this.$translate(
                'reports.errorMsg.getReportsFailed',
            );
            console.error(error);
            this.ngToast.danger(errorGettingReports);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    handleReport(report: ViewReport, currentEmail: string) {
        const date = moment(report.modifiedAt);
        let showDate = undefined;

        if (report.modifiedAt) {
            showDate = date.isSame(moment.now(), 'day')
                ? moment(report.modifiedAt).fromNow()
                : date.format('DD/MM/YYYY - HH:mm');
        } else {
            showDate = 'N/A';
        }

        const fullName = report.owner.fullName || report.owner.email;
        const isOwner = report.owner.email === currentEmail;
        return { ...report, fullName, showDate, isOwner };
    }

    searchChange() {
        if (!this.bucketReportsCopy) {
            this.bucketReportsCopy = angular.copy(this.bucketReports);
        }

        if (this.search.length >= 1) {
            this.searchUsed = true;
            this.bucketReports.reports = this.bucketReportsCopy.reports.filter(
                (report) => {
                    if (report.name) {
                        const name = foldToASCII(report.name).toLowerCase();
                        const search = foldToASCII(this.search).toLowerCase();

                        return name.includes(search);
                    }

                    return false;
                },
            );
        } else {
            this.bucketReports.reports = this.bucketReportsCopy.reports;
        }
    }

    searchBlur() {
        if (this.searchUsed) {
            this.SegmentService.createBotTrack(
                'analytics-custom-reports-searched',
                this.$rootScope.application
            );
        }
        this.searchUsed = false;
    }

    async setHelper() {
        try {
            const text = await this.$translate('reports.helperLink.title');
            const link = await this.$translate('reports.helperLink.link');
            this.helperLink = { text, link };
        } catch (err) {
            console.error(err);
        }
    }

    async confirmDelete(report: ViewReport, $event): Promise<any> {
        $event.stopPropagation(); // Prevent navigate to report
        const modalTitle = await this.$translate('reports.modal.title');
        const modalBody = await this.$translate('reports.modal.body');
        const yes = await this.$translate('utils.misc.yes');
        const no = await this.$translate('utils.misc.no');

        const modal = await this.ModalService.showModal(
            ConfirmationModal({
                title: {
                    text: modalTitle,
                },
                body: modalBody,
                buttons: {
                    confirm: {
                        text: yes,
                    },
                    cancel: {
                        text: no,
                    },
                },
            }),
        );

        const confirmation = await modal.close;

        if (confirmation) {
            this.delete(report);
        }
    }

    async delete(report: ViewReport) {
        try {
            this.LoadingService.startLoading();
            const successMsg32 = await this.$translate('utils.successMsg.32');
            this.bucketReports.reports = this.bucketReports.reports.filter(
                (rep) => rep.id !== report.id,
            );

            await this.AnalyticsReportsService.delete(
                report.id,
            );

            this.SegmentService.createTrack('delete-analytics-report', {
                botName: this.$rootScope.application.name,
            });
            this.ngToast.success(successMsg32);
        } catch (e) {
            const errorMsg127 = await this.$translate('utils.errorMsg.127');
            this.ngToast.danger(errorMsg127);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    goToReport() {
        this.SegmentService.createBotTrack(
            'analytics-custom-reports-created',
            this.$rootScope.application,
            { Date: new Date().toISOString() }
        );
        this.isDisplayingAnalyticsTabs ? this.$state.go(analyticsReportStateName) : this.$state.go(reportStateName);
    }

    checkFeatures = async () => {
        this.isDisplayingAnalyticsTabs = !await AnalyticsFeatures.isHidingAnalyticsTabs();
    }

    listenForReportsUpdates = () => {
        this.$rootScope.$on(EVENTS.REPORTS_UPDATED, () => {
            this.getReports();
        });
    }
}
