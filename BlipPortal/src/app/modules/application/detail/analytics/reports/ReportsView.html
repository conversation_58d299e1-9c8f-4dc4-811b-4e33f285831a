<page-header page-title="{{'reports.title' | translate}}" helper-title="{{'reports.helperTitle' | translate}}"
    helper-body="{{'reports.helperBody' | translate}}" helper-doc="$ctrl.helperLink" class="page-header">
    <custom-content>
        <search-input class="flex mr3">
            <input ng-model="$ctrl.search" ng-change="$ctrl.searchChange()" ng-blur="$ctrl.searchBlur()" type="text" placeholder="{{'reports.searchReports' | translate}}">
        </search-input>
        <button ng-auth-write ng-click="$ctrl.goToReport()" class="bp-btn bp-btn--bot bp-btn--small" translate>reports.create</button>
    </custom-content>
</page-header>

<div class="container" id="reports-id">
    <div ng-show="$ctrl.bucketReports.reports.length > 0" ng-repeat="report in $ctrl.bucketReports.reports track by report.id">
        <bds-paper  elevation="Static" class="cards cursor-pointer" weight="400"
            ui-sref="{{$ctrl.isDisplayingAnalyticsTabs ? 'auth.application.detail.analytics.reports.report({ reportId: report.id })' : 'auth.application.detail.dashboard.reports.report({ reportId: report.id })'}}"
            ui-sparams="{ reportId: report.id }">
            <div class="w-25 truncate">
                <bds-typo variant="fs-12" class="label" translate>reports.reportName</bds-typo>
                <bds-typo variant="fs-14" class="description">{{report.name || $ctrl.untitled}}</bds-typo>
            </div>
            <div class="w-30 truncate">
                <bds-typo variant="fs-12" class="label" translate>reports.createdBy</bds-typo>
                <bds-typo variant="fs-14" class="description">{{report.fullName}}</bds-typo>
            </div>
            <div class="w-25">
                <bds-typo variant="fs-12" class="label" translate>reports.modifiedAt</bds-typo>
                <bds-typo variant="fs-14" class="description">{{report.showDate}}</bds-typo>
            </div>
            <div class="card-icons card-icons--hidden w-10 description">
                <a ng-if="report.isOwner" class="no-decoration" ng-auth-write 
                    ui-sref="{{$ctrl.isDisplayingAnalyticsTabs ? 'auth.application.detail.analytics.reports.report({ reportId: report.id })' : 'auth.application.detail.dashboard.reports.report({ reportId: report.id })'}}"
                    ui-sparams="{ reportId: report.id }">
                    <bds-icon type="icon" title="{{'utils.forms.edit' | translate }}" class="icon-edit icons" size="x-large"></bds-icon type="icon">
                </a>
                <bds-icon type="icon" ng-if="report.isOwner" title="{{'utils.forms.remove' | translate }}" class="icon-delete icons" size="x-large" ng-click="$ctrl.confirmDelete(report, $event)"></bds-icon type="icon">
            </div>
        </bds-paper>
    </div>
    <bds-typo ng-show="$ctrl.bucketReports.reports.length === 0" class="no-content-found" translate>
        reports.noReportsFound
    </bds-typo>
</div>