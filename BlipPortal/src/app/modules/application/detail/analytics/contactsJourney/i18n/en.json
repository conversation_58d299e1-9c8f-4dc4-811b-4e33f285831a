{"contactsJourney": {"title": "Contacts journey", "subtitle": "Analyze the behavior of your contacts when they interact with your chatbot", "contacts": "Contacts", "exit": "Exit", "othersNodeName": "Others", "startNodeName": "Start", "filters": {"firstNode": "Start from"}, "header": {"selectNodeForDetails": "Select a node for more details", "listContacts": "List contacts", "selectANode": "First, select a node"}, "infoModal": {"title": "What is the contacts journey?", "description": "How do your contacts interact with your chatbot? How do most of your contacts behave? How is your flow performing? Where did those people come from, and where did they go? In the contacts journey, you can have insights about your flows in real-time, so you may continuously improve your conversational flows.<br><br>To start using it, activate the “Automatic tracking” function there in Builder. The path is like this: <strong>Builder > General settings > Automatic tracking.</strong>", "documentationDescription": "View documentation", "link": "https://help.blip.ai/hc/pt-br/articles/1500004220481", "confirmationButton": "Ok, got it"}, "loading": {"title": "Your chatbot's contacts journey is being mapped...", "description": "Please wait for a few minutes. Longer flows may have a longer mapping time."}, "errorNoData": {"title": "The chatbot does not have enough data to map a journey in the selected period", "descriptionDefault": "You should republish your flow and wait a few hours until the data start to show up here.", "descriptionMaster": "To visualize how people are using your chatbot, you should enable router's context in your sub bots flows."}, "genericError": {"title": "Oops… There’s been an error while mapping your journey", "description": "Please, try again.", "buttonTitle": "Try again"}, "learnHow": {"enableMasterContext": {"title": "Learn how to enable the router context", "link": "https://help.blip.ai/hc/en-us/articles/360060548973"}}, "instructions": {"title": "Understanding the diagram", "description": "The title of each node contains the name of the block created in the builder, numeric signaling of the stage, and percentage of contacts that passed through the flow.", "nodes": {"default": {"title": "Default node", "description": "Represents each person who accessed a particular block during a session within the filtered period."}, "exit": {"title": "Exit node", "description": "It occurs when the contact did not perform any other action within the filtered period."}, "other": {"title": "Others node", "description": "A set of several other blocks with a smaller volume."}}}, "contactsSideBar": {"title": "Contacts listing", "export": {"button": "Export list", "error": "There was an error while exporting the contacts list", "tooltip": "Limited to the 1000 most recent contacts"}}}}