import { ContactsJourneyEdge, EdgeTypes } from 'modules/analytics/models/ContactsJourneyEdge';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { Application } from 'modules/shared/ApplicationTypings';
import { STATE_NAME_NODE_EXIT, STATE_NAME_NODE_OTHERS } from '../Constants';

export const getNodeNameAndPercentage = (nodeFullName: string) => {
    const selectedNodeName = nodeFullName.substr(0, nodeFullName.lastIndexOf(':')).trim();
    const selectedNodePercentage = nodeFullName.substr(nodeFullName.lastIndexOf(':') + 1, nodeFullName.length).trim();

    return {
        selectedNodeName, selectedNodePercentage
    };
};

export const getNodeName = (selectedJourneyEdge: ContactsJourneyEdge) => {
    let selectedNodeName = '';

    if (selectedJourneyEdge.type === EdgeTypes.End) {
        selectedNodeName = `${STATE_NAME_NODE_EXIT} [${selectedJourneyEdge.step}]`;
    } else if (selectedJourneyEdge.type === EdgeTypes.Other) {
        selectedNodeName = `${STATE_NAME_NODE_OTHERS} [${selectedJourneyEdge.step}]`;
    } else {
        selectedNodeName =  selectedJourneyEdge.name.substr(0, selectedJourneyEdge.name.lastIndexOf(':')).trim();
    }

    return { selectedNodeName };
};

export const createJourneyTrack = (segmentService: SegmentService, application: Application, trackEvent: string, payload: object = {}) => {
    segmentService.createApplicationTrack(
        {
            trackEvent,
            application,
            payload
        }
    );
};
