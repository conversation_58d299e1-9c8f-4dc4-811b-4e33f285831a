@import "~assets/scss/main";
@import '~blip-ds/dist/collection/styles/colors';

.page-content {
    overflow: auto;
}

#contacts-journey-header .container {
    width: 90%;
    color: $color-content-default!important;
}

#contacts-journey {
    min-width: 620px;

    .diagram-header {
        line-height: 40px;
        padding-bottom: 14px;

        .contacts-button-view {
            max-height: 40px;
        }
    }
}

#contacts-journey-container {
    margin-top: 14px;

    .communication-div {
        height: 550px;
    }
}
#contacts-side-bar-summary {
    .card-content {
        background-color: $color-surface-3 !important;
        color: $color-content-default !important;
        text-align: left;
        padding: 15px;
        min-height: 100px;
        border-radius: 8px;
    }
}

#contacts-journey-content {
    overflow: auto;
    user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    -webkit-touch-callout: none;
    -o-user-select: none;
    -moz-user-select: none;

    .card-content {
        padding: 0;
        color: $color-content-default !important;
    }

    .diagram-body {
        .google-visualization-tooltip {
            border: none;
            padding: 5px;
            border-radius: 5px;
            height: auto !important;
            width: 112px;
            z-index: 1;
        }

        .tooltip-node-values-text {
            color: $color-content-default !important;
        }

        .tooltip-node-names-text {
            color: $color-content-default !important;
        }

        svg {
            margin: 0px 20px 20px 20px;
            g {
                text[y^='6.'],
                text[y^='5.'],
                text[y^='4.'],
                text[y^='3.']
                 {
                    transform: translateY(1%);
                }

                rect {
                    cursor: pointer;
                }
            }
        }
    }

    .diagram-loading {
        animation: blinker 1.5s linear infinite;
        width: 40%;
    }

    .main-blip-color {
        color: $color-primary;
    }

}

#contacts-journey-filter-first-node {
    padding-right: 10px;
}

.color-surface-2 {
    color: $color-surface-2 !important;
} 

#contacts-journey-side-bar {
    position: fixed;
    z-index: 39999; //1 below header to allow preferences dropdown to be shown when bar is opened.
    max-width: 416px;
    
    border-left: 0px ;
    background-color: $color-surface-1 !important;
    height: 100%;
    .sidebar-content-header {
        background: $color-surface-4 !important;
        color: $color-surface-1 !important;
        padding-top: 15px;
        height: 65px;
        position: fixed;
        width: 416px;
        z-index: inherit;
    }

    .sidebar-content-body {
        margin-top: 12%;
        background-color: $color-surface-1 !important;
        height: 100%;
    }

    #summary-export-button {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-content: center;
        align-items: center;
        
        .area-button-summary-export {
            display: flex;
            justify-content: space-between;
            align-content: center;
            bds-typo {
                padding-right: 10px;
            }
        }
    }

    #contacts-list-view {
        .card-content {
            display: flex;
            align-items: center;
            background-color: $color-surface-1;
            color: $color-content-default !important;
            border-radius: 8px;
        }
    }
}
#contacts-journey-instructions {
    .card-content {
        color: $color-content-default !important;
    }
}

#contacts-side-bar-close-button {
    color: $color-surface-1 !important;
}

@keyframes blinker {
    50% {
        opacity: 0;
    }
}
