{"contactsJourney": {"title": "Jornada dos contatos", "subtitle": "Analise o comportamento dos contatos com seu chatbot", "contacts": "Contatos", "exit": "<PERSON><PERSON><PERSON>", "othersNodeName": "Outros", "startNodeName": "Início", "filters": {"firstNode": "Começar a partir de"}, "header": {"selectNodeForDetails": "Selecione um nó para mais detalhes", "listContacts": "Listar contatos", "selectANode": "Primeiro selecione um nó"}, "infoModal": {"title": "O que é a jornada dos contatos?", "description": "Como seus contatos interagem com seu chatbot? Qual o comportamento da maioria? Como está a performance de seu fluxo? De onde essas pessoas vieram e para onde elas foram? Na jornada dos contatos, você consegue obter insights sobre seus fluxos em tempo real, permitindo que você aprimore, constantemente, seus fluxos de conversa.", "documentationDescription": "Ver documentação", "link": "https://help.blip.ai/hc/pt-br/articles/1500004220481", "confirmationButton": "Ok, entendi"}, "loading": {"title": "A jornada dos contatos do seu chatbot está sendo mapeada...", "description": "Por favor, aguarde alguns minutos. Fluxos maiores podem ter um tempo de mapeamento maior."}, "errorNoData": {"title": "O chatbot não possui dados suficientes para mapeamento de uma jornada no período selecionado", "descriptionDefault": "É necessário republicar o seu fluxo e aguardar algumas horas para que os dados comecem a aparecer por aqui.", "descriptionMaster": "Para visualizar como as pessoas têm utilizado o seu chatbot, é necessário ativar o contexto do roteador no fluxo dos seus sub-bots."}, "genericError": {"title": "Ops… Houve um erro durante o mapeamento de sua jornada", "description": "Por favor, tente novamente.", "buttonTitle": "Tentar novamente"}, "learnHow": {"enableMasterContext": {"title": "Saiba como ativar o contexto do roteador", "link": "https://help.blip.ai/hc/pt-br/articles/360060548973"}}, "instructions": {"title": "Compreendendo o <PERSON>a", "description": "O título de cada nó é composto por: nome do bloco criado no builder, sinalização númerica da etapa e percentual de contatos que passaram pelo fluxo.", "nodes": {"default": {"title": "<PERSON><PERSON>", "description": "Representa cada pessoa que acessou um determinado bloco durante uma sessão, dentro do período filtrado."}, "exit": {"title": "<PERSON><PERSON>", "description": "Ocorre quando o contato não realizou nenhuma outra ação, dentro do período filtrado."}, "other": {"title": "<PERSON><PERSON>", "description": "Um conjunto de vários outros blocos de menor volume."}}}, "contactsSideBar": {"title": "Listagem de contatos", "export": {"button": "Exportar lista", "error": "Houve um erro ao exportar a lista de contatos.", "tooltip": "Limitado aos 1000 contatos mais recentes"}}}}