<div class="page-content" id="contacts-journey-view">
    <page-header id="contacts-journey-header" ng-if="$ctrl.isShowingContactsJourney">
        <custom-title>
            <div class="flex flex-items-center">
                <bds-typo class="pr2 title-analytics" tag="span" variant="fs-24" translate>
                    contactsJourney.title
                </bds-typo>
                <bds-icon theme="solid" name="info" class="pt1 " size="small" ng-click="$ctrl.showContactsJourneyInfoModalModal()"></bds-icon>
            </div>
        </custom-title>
    </page-header>
    <div id="contacts-journey" class="container w-90"  ng-if="$ctrl.isShowingContactsJourney">
        <div id="contacts-journey-filters" class="flex" ng-class="$ctrl.isShowingFirstNodeFilter ? 'justify-between' : 'justify-end'">
            <div class="flex justify-start items-center" ng-if="$ctrl.isShowingFirstNodeFilter">
                <bds-typo id="contacts-journey-filter-first-node" class="color-content-default" tag="span" variant="fs-16" bold="semi-bold" translate>
                    contactsJourney.filters.firstNode
                </bds-typo>
                <bds-autocomplete 
                    class="color-content-ghost"
                    placeholder="{{'contactsJourney.startNodeName' | translate}}"
                    options={{$ctrl.firstNodeFilterOptions}}
                    custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.applyFirstNodeFilter }]",
                    value="{{$ctrl.firstNode}}">
                </bds-autocomplete>
            </div>
            <div class="small-datepicker-input flex items-center">
                <blip-daterange-picker
                    ng-if="$ctrl.daterangePickerPeriod"
                    period="$ctrl.daterangePickerPeriod"
                    cancel-text="{{'datepicker.cancelText' | translate}}"
                    apply-text="{{'datepicker.applyText' | translate}}"
                    start-date-placeholder="{{'datepicker.startDatePlaceholder' | translate}}"
                    end-date-placeholder="{{'datepicker.endDatePlaceholder' | translate}}"
                    on-date-selection="$ctrl.applyDateRangeFilter()">
                </blip-daterange-picker>
            </div>
        </div>
        <card id="contacts-journey-container">
            <div ng-if="$ctrl.canShowDiagram()">
                <div class="diagram-header" ng-class="$ctrl.isShowingFirstNodeFilter ? 'flex justify-end' : ''">
                    <bds-typo class="color-content-default" tag="span" variant="fs-14" bold="bold" translate ng-if="!$ctrl.isShowingFirstNodeFilter">
                        contactsJourney.header.selectNodeForDetails
                    </bds-typo>
                    <div class="fr contacts-button-view" ng-if="$ctrl.canCurrentUserViewContacts()">
                        <div
                            tooltips
                            tooltip-template="{{$ctrl.isContactsButtonAvailable ? '' : 'contactsJourney.header.selectANode' | translate}}"
                            tooltip-side="bottom"
                            tooltip-class="medium"
                            class="f3  pl2 tooltip-icon-hover">
                            <bds-button
                                variant="primary"
                                id="open-contacts-button"
                                ng-click="$ctrl.isContactsButtonAvailable && $ctrl.openContactsSideBar()"
                                ng-disabled="!$ctrl.isContactsButtonAvailable">
                                    {{'contactsJourney.header.listContacts' | translate}}
                            </bds-button>
                        </div>
                    </div>
                </div>
                <div id="contacts-journey-content">
                    <div class="diagram-body pv5" id="diagram-body">
                        <sankey-diagram
                            displayed-edges="$ctrl.displayedJourneyEdges"
                            change-event="$ctrl.changeEvent"
                            options="$ctrl.sankeyOptions"
                            contacts-button-available="$ctrl.isContactsButtonAvailable"
                            selected-edge="$ctrl.selectedEdge"
                            selected-node-users-count="$ctrl.selectedNodeUsersCount"
                            id="sankey-chart">
                        </sankey-diagram>
                    </div>
                </div>
            </div>
            <div class="pa3 flex flex-column items-center justify-center communication-div" ng-if="$ctrl.canShowLoading()">
                <img src="/assets/img/sankey-loading.svg" class="diagram-loading pa3" />
                <bds-typo tag="span" variant="fs-20" bold="semi-bold" translate>
                    contactsJourney.loading.title
                </bds-typo>
                <bds-typo tag="span" variant="fs-14" bold="regular" translate>
                    contactsJourney.loading.description
                </bds-typo>
            </div>
            <div class="pa3 mh4 flex flex-column items-center justify-center communication-div" id="diagram-body" ng-if="$ctrl.canShowNoDataView()">
                <img src="/assets/img/clock.svg" class="pa3 pb4"/>
                <bds-typo class="ma3 tc" tag="span" variant="fs-24" bold="bold" translate>
                    contactsJourney.errorNoData.title
                </bds-typo>
                <bds-typo class="bp-c-rooftop ph7 tc" tag="span" variant="fs-16" bold="regular" translate>
                    {{$ctrl.noDataDescription}}
                </bds-typo>
                <div ng-if="$ctrl.canShowEmptyMasterApplicationMessage()">
                    <div class="flex flex-row mt4">
                        <a href="{{ 'contactsJourney.learnHow.enableMasterContext.link' | translate }}" target="_blank">
                            <bds-typo class="underline" tag="span" variant="fs-14" bold="normal" translate>
                                contactsJourney.learnHow.enableMasterContext.title
                            </bds-typo>
                        </a>
                        <bds-icon size="small" name="external-file" role="img" class="bds-icon hydrated main-blip-color"></bds-icon>
                    </div>
                </div>
            </div>
            <div class="pa3 flex flex-column items-center justify-center communication-div" ng-if="$ctrl.canShowGenericError()">
                <img src="/assets/img/empty.svg" class="pa3"/>
                <bds-typo class="ma3" tag="span" variant="fs-24" bold="bold" translate>
                    contactsJourney.genericError.title
                </bds-typo>
                <bds-typo tag="span" variant="fs-16" bold="regular" translate>
                    contactsJourney.genericError.description
                </bds-typo>
                <div class="flex flex-row mt4">
                    <bds-button variant="primary" icon="refresh" size="standard" class="hydrated mh3"  ng-click="$ctrl.applyDateRangeFilter()" translate>
                        contactsJourney.genericError.buttonTitle
                    </bds-button>
                </div>
            </div>
        </card>

        <card id="contacts-journey-instructions"  ng-if="$ctrl.canShowDiagram()">
            <div  class="pa3 flex flex-column justify-center items-stretch">
                <div class="flex flex-column communication-div">
                    <bds-typo  tag="span" variant="fs-16" bold="bold" translate>
                        contactsJourney.instructions.title
                    </bds-typo>
                    <bds-typo  tag="span" variant="fs-14" translate>
                        contactsJourney.instructions.description
                    </bds-typo>
                </div>
                <div class="tl flex justify-between pt4 mh0">
                    <labeled-color-card ng-repeat="info in $ctrl.labeledColorCardsInfo" class="w-33 mr4"
                        title="info.title"
                        color="info.color"
                        text="info.text">
                    </labeled-color-card>
                </div>
            </div>
        </card>
    </div>
</div>
