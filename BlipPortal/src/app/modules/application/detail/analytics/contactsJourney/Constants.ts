import { COLOR_SECONDARY, COLOR_SURFACE_3 } from '../Colors';

export const SANKEY_OPTIONS = {
    tooltip: { isHtml: true, trigger: 'selection' },
    legend: { position: 'labeled', colors: [COLOR_SECONDARY]},
    width: 1000,
    height: 500,
    sankey: {
        node: {
            colors: [],
            colorMode: 'unique',
            label: {
                fontName: 'Nunito Sans',
                fontSize: 10,
                color: COLOR_SECONDARY,
                bold: true,
                position: 'center',
                fillOpacity: 1,
                background: 'none',
            },
            interactivity: true,
            nodePadding: 30,
            width: 15,
        },
        iterations: 0,
        link: {
            color: {
                fill: COLOR_SURFACE_3,
                fillOpacity: 1,
                stroke: COLOR_SURFACE_3,
                strokeWidth: 2,
            },
        },
    },
};

export const CONTACTS_SIDE_BAR_ID = 'CONTACTS_SIDE_BAR';
export const CONTACTS_USERS_PER_TAKE = 20;
export const STATE_ID_START = 'onboarding';
export const STATE_NAME_NODE_OTHERS = '#others';
export const STATE_NAME_NODE_EXIT = '#exit';
export const DOWNLOAD_CONTACTS_TRACK = 'analytics-journey-contacts-download';
export const CONTACT_OPENED_TRACK = 'analytics-journey-contacts-contactOpened';
export const EXPORT_ERROR_MESSAGE = 'contactsJourney.contactsSideBar.export.error';
export const SANKEY_CHANGE_EVENT = 'SANKEY_CHANGE_EVENT';
export const DEFAULT_DATE_FORMAT = 'DD/MM/YYYY';
export const PERIOD_RANGE_MIN = -30;
export const PERIOD_RANGE_MAX = 1;
export const PERIOD_YESTERDAY = -1;
export const PERIOD_TODAY = 0;
export const MAXIMUM_DAYS_TO_SELECT_FROM_TODAY = 6;
export const ONE_STEP_WIDTH = 23;
export const FULL_WIDTH = 100;
export const BEHAVIOR_DISPLAYED = 'displayed';
export const BEHAVIOR_EXCEPTION = 'exception';
export const AUTO_TRACKING_ERROR = 'auto-tracking-inactive';
export const NO_DATA_FOUND_ERROR = 'journey-insuficient-data';
export const GERNERIC_ERROR = 'journey-generic-error';
export const MOVED_SCROLL_TRACK = 'analytics-journey-scroll-moved';
export const DATE_CHANGED_TRACK = 'analytics-journey-date-changed';
export const FIRST_NODE_CHANGED_TRACK = 'analytics-journey-firstNode-changed';
export const DIAGRAM_BEHAVIOR_TRACK = 'analytics-journey-diagram-behavior';
export const INFO_MODAL_TRACK = 'analytics-jorney-details-opened';
export const CONTACTS_LISTED_TRACK = 'analytics-journey-contacts-listed';
export const APPLICATION_EMPTY_DATA_DESCRIPTION = 'contactsJourney.errorNoData.descriptionDefault';
export const MASTER_EMPTY_DATA_DESCRIPTION = 'contactsJourney.errorNoData.descriptionMaster';
export const APPLICATION_ORIGIN = 'ContactsJourney';
export const FILE_NAME_DATE_FORMAT = 'YYYYMMDDHHmm';
export const ERROR_GENERATING_AUDIENCE_TITLE = 'activeMessages.send.audience.button.errorToasts.errorGeneratingAudienceTitle';
export const ERROR_GENERATING_AUDIENCE_BODY = 'activeMessages.send.audience.button.errorToasts.errorGeneratingAudienceBody';
export const REFRESH_BUTTON_TEXT = 'utils.forms.refresh';
