import { IStateService } from 'angular-ui-router';
import { Application } from 'modules/shared/ApplicationTypings';
import moment from 'moment';

//Components
import { ResizeObserver } from 'resize-observer';

//Constants
import { CHANNELS_IMAGES } from 'types/ChannelsImages';
import { userDetailStateName } from '../../users';
import {
    GROWTH_ACTIVE_MESSAGE_WA_SENT_ACTIVE_MESSAGE_BUTTON_CLICKED
} from '../../growth/GrowthSegmentEvents';
import {
    CONTACT_OPENED_TRACK,
    DOWNLOAD_CONTACTS_TRACK,
    EXPORT_ERROR_MESSAGE,
    APPLICATION_ORIGIN,
    FILE_NAME_DATE_FORMAT,
    ERROR_GENERATING_AUDIENCE_TITLE,
    ERROR_GENERATING_AUDIENCE_BODY,
    REFRESH_BUTTON_TEXT,
    STATE_ID_START,
} from './Constants';

//Models
import { ContactsJourneyEdge } from 'modules/analytics/models/ContactsJourneyEdge';
import { ContactsJourneyContact } from 'modules/analytics/models/ContactsJourneyContact';
import { Period } from 'interfaces/DaterangerPicker';

//Services
import { SidebarContentService } from 'modules/shared/SidebarContentService';
import { ContactsJourneyService, DATA_SOURCE } from '../services/ContactsJourneyService';
import { UsersService } from 'modules/application/detail/users/UsersService';
import { GrowthSegmentService } from '../../growth/GrowthSegmentService';
import { BlipToastService } from 'modules/shared/BlipToastService';

import { SegmentService, SUCCESS_STATUS, FAILURE_STATUS } from 'modules/application/misc/SegmentService';
import { ITimeoutService, IWindowService } from 'angular';
import { CONTACTS_SIDE_BAR_ID, CONTACTS_USERS_PER_TAKE } from './Constants';
import { createJourneyTrack, getNodeNameAndPercentage } from './utils/contactsJourneyUtils';
import { NavbarFeatures } from 'modules/navbar/NavbarFeatures';
import TranslateService from 'modules/translate/TranslateService';
import { ActiveMessageService } from '../../growth/activeMessages/ActiveMessageService';
import { ActiveMessagesFeatures } from '../../growth/activeMessages/ActiveMessagesFeatures';

//Constants
import { ActiveMessagesConstants } from '../../growth/activeMessages/ActiveMessagesConstants';
import { toCamelCase } from 'data/string';

export class ContactsJourneySideBarController {

    public isActiveMessageButtonEnabled: boolean;
    private selectedEdgeName: string;
    private selectedEdgeContactsCount: number;
    private selectedEdgeContactsPercentage: string;
    private edgeContacts: ContactsJourneyContact[] = [];
    private isLoading: boolean = false;
    private lastPageReached: boolean = false;
    private isGeneratingCSV: boolean = false;

    constructor(
        private SidebarContentService: SidebarContentService,
        private selectedEdge: ContactsJourneyEdge,
        private searchPeriod: Period,
        private firstNode: string = STATE_ID_START,
        private dataSource: DATA_SOURCE,
        private selectedNodeUsersCount: number,
        private ContactsJourneyService: ContactsJourneyService,
        private $state: IStateService,
        private $window: IWindowService,
        private UsersService: UsersService,
        private SegmentService: SegmentService,
        private application: Application,
        private $timeout: ITimeoutService,
        private isShowingCSVExportButton: boolean,
        private ngToast: any,
        private TranslateService: TranslateService,
        private ActiveMessageService: ActiveMessageService,
        private GrowthSegmentService: GrowthSegmentService,
        private BlipToastService: BlipToastService,
    ) {
        this.init();
    }

    async init() {
        this.setSideBarPositioning();
        this.getSummaryDataFromSelectedEdge();
        this.listenForEventToCloseSidebar();
        await this.checkFeatures();
    }

    listenForEventToCloseSidebar() {
        document.getElementById('contacts-journey-view').addEventListener('click', (e: any) => {
            if (!e.target.id || e.target.id != 'open-contacts-button') {
                if (this.isContactsSideBarOpened()) {
                    this.closeSidebar();
                }
            }
        });
    }

    isContactsSideBarOpened() {
        const openedSideBars = this.SidebarContentService.getSidebars();
        return openedSideBars.find(sideBar => {
            return sideBar.sidebarId == CONTACTS_SIDE_BAR_ID;
        });
    }

    setChannelsImagesToEdgeContacts() {
        this.edgeContacts.forEach((contact: ContactsJourneyContact) => {
            if (contact.source) {
                const sourceData = CHANNELS_IMAGES.find((channel) => {
                    return channel.channel.toLocaleLowerCase() == contact.source.toLocaleLowerCase();
                });
                contact.sourceImage = sourceData && sourceData.src;
            }
        });
    }

    getSummaryDataFromSelectedEdge() {
        const { selectedNodeName, selectedNodePercentage } = getNodeNameAndPercentage(this.selectedEdge.name);

        const firstEdge = this.selectedEdge.step == 0;

        this.selectedEdgeName = selectedNodeName;
        this.selectedEdgeContactsCount = this.selectedNodeUsersCount;
        this.selectedEdgeContactsPercentage = firstEdge ? '(100%)' : `(${selectedNodePercentage})`;
    }

    async closeSidebar() {
        this.$timeout(
            () => this.SidebarContentService.cleanAndClose(false),
            0,
        );
    }

    goToContactDetails(identity) {
        let behavior = SUCCESS_STATUS;
        try {
            if (!identity) {
                return;
            }
            const id = this.UsersService.getDecodedUserIdentity(identity);

            const contactUrl = this.$state.href(userDetailStateName, { id });
            this.$window.open(contactUrl, '_blank');
        } catch (e) {
            behavior = FAILURE_STATUS;
        } finally {
            const contactOpenedTrackBody = {
                behavior,
                nodeType: this.selectedEdge.type,
                nodeName: this.selectedEdge.name
            };

            createJourneyTrack(this.SegmentService, this.application, CONTACT_OPENED_TRACK, contactOpenedTrackBody);
        }
    }

    setSideBarDistanceFromTop() {
        const sidebarElement = document.getElementById('contacts-journey-side-bar');

        if (sidebarElement) {
            const navBarElement = document.getElementById('main-navbar');
            const instabilityBannerElement = document.getElementsByClassName('instability-banner')[0];
            let instabilityBannerHeight = instabilityBannerElement ? instabilityBannerElement.scrollHeight : 0;

            if (window.scrollY <= instabilityBannerHeight) {
                instabilityBannerHeight = instabilityBannerHeight - window.scrollY;
            } else {
                instabilityBannerHeight = 0;
            }

            const distanceFromTop = instabilityBannerHeight + navBarElement.offsetHeight;

            sidebarElement.style.top = `${distanceFromTop.toString()}px`;

            const sidebarHeight = this.$window.innerHeight - navBarElement.offsetHeight;
            sidebarElement.style.height = `${sidebarHeight.toString()}px`;
        }
    }

    setSideBarPositioning() {
        this.setSideBarDistanceFromTop();

        if (NavbarFeatures.isInstabilityBannerEnabled) {
            this.listenForPageScroll();
        }

        const resizeObserver = new ResizeObserver(() => {
            this.setSideBarDistanceFromTop();
        });
        const navBarElement = document.getElementById('main-navbar');
        const instabilityBannerElement = document.getElementsByClassName('instability-banner')[0];
        resizeObserver.observe(navBarElement);

        if (instabilityBannerElement) {
            resizeObserver.observe(instabilityBannerElement);
        }
    }

    listenForPageScroll() {
        window.addEventListener('scroll', () => {
            this.setSideBarDistanceFromTop();
        });
    }

    async loadMore() {
        if (this.isLoading) {
            return;
        }

        try {
            if (!this.lastPageReached) {
                this.isLoading = true;
                await this.getEdgeContacts();
            }
        } catch (error) {
            this.lastPageReached = true;
        } finally {
            this.isLoading = false;
        }
    }

    async getEdgeContacts() {
        const skip = this.edgeContacts.length;

        const pageContacts = await this.ContactsJourneyService.getEdgeContacts(
            this.searchPeriod,
            this.selectedEdge,
            this.dataSource,
            this.firstNode,
            skip,
            CONTACTS_USERS_PER_TAKE);

        const mergedContacts = this.edgeContacts.concat(pageContacts);
        this.edgeContacts = mergedContacts;
        this.setChannelsImagesToEdgeContacts();
    }

    async downloadCSV() {
        let behavior = SUCCESS_STATUS;
        let fileSize = 0;
        try {
            this.isGeneratingCSV = true;
            const dataCsv = await this.ContactsJourneyService.getJourneyCSVData(
                this.searchPeriod,
                this.selectedEdge,
                this.dataSource,
                this.firstNode
            );

            fileSize = dataCsv?.size ? dataCsv.size : 0;
            if (dataCsv?.uri) {
                window.open(dataCsv.uri, '_blank');
            } else {
                throw new TypeError('Invalid uri.');
            }

        } catch (err) {
            this.ngToast.danger(
                await this.TranslateService.instantTranslate(EXPORT_ERROR_MESSAGE)
            );
            behavior = FAILURE_STATUS;
        } finally {
            this.isGeneratingCSV = false;
            this.trackCSVDownloadEvent(behavior, fileSize);
        }
    }

    async checkFeatures() {
        this.isActiveMessageButtonEnabled = await ActiveMessagesFeatures.isActiveMessagesOnContactsJourneyEnabled() as boolean;
    }

    async generateAudience() {
        try {
            const url = await this.ActiveMessageService.createAudience(this.edgeContacts);

            const stepName = toCamelCase(this.selectedEdgeName.split('[')[0]);
            const ownerIdentity = this.$state.params.shortName;
            const dateTime = await moment().format(FILE_NAME_DATE_FORMAT);
            const fileName = `${APPLICATION_ORIGIN}_${ownerIdentity}_${stepName}_${dateTime}.csv`;

            createJourneyTrack(this.SegmentService,
                this.application,
                GROWTH_ACTIVE_MESSAGE_WA_SENT_ACTIVE_MESSAGE_BUTTON_CLICKED,
                { applicationOrigin: APPLICATION_ORIGIN }
            );

            this.$state.go(ActiveMessagesConstants.SEND_ACTIVE_MESSAGE_STATE, {
                fileName,
                mediaUrl : url,
                applicationOrigin : APPLICATION_ORIGIN
            });
        } catch (e) {
            this.BlipToastService.show('danger', {
                title: await this.TranslateService.instantTranslate(ERROR_GENERATING_AUDIENCE_TITLE),
                msg: await this.TranslateService.instantTranslate(ERROR_GENERATING_AUDIENCE_BODY),
                buttonText: await this.TranslateService.instantTranslate(REFRESH_BUTTON_TEXT),
                callback: () => this.$state.reload(),
                duration: 19000
            });
        }
    }

    trackCSVDownloadEvent(behavior: string, fileSize: number) {
        const nodeType = this.selectedEdge.type;
        const nodeName = this.selectedEdge.name;
        const usersCount = this.selectedNodeUsersCount;

        const contactsDownloadedTrackBody = {
            behavior,
            nodeType,
            nodeName,
            usersCount,
            fileSize
        };

        createJourneyTrack(this.SegmentService, this.application, DOWNLOAD_CONTACTS_TRACK, contactsDownloadedTrackBody);
    }
}
