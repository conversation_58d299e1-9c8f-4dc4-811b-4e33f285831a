import { FeatureToggleClientService } from 'feature-toggle-client';

export const contactsJourneyContactsButton = 'contacts-journey-contacts-button';
export const contactsJourneyCsvExportButton = 'contacts-journey-csv-export-button';
export const contactsJourneyCassandraDataSource = 'contacts-journey-cassandra-datasource';
export const contactsJourneyFirstNodeFilter = 'contacts-journey-first-node-filter';

export class ContactsJourneyFeatures {
    static async isShowingContactsButton() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            contactsJourneyContactsButton
        );
    }

    static async isShowingCSVExportButton() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            contactsJourneyCsvExportButton
        );
    }

    static async isUsingCassandraDataSource() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            contactsJourneyCassandraDataSource
        );
    }

    static async isShowingFirstNodeFilter() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            contactsJourneyFirstNodeFilter
        );
    }
}
