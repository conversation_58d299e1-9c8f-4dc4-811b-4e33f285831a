<bds-paper elevation="primary" id="contacts-journey-side-bar" class="sidebar-content-component position-right">
    <div class="sidebar-content-header ph4 pv4">
        <bds-typo class="pl2 color-surface-2" tag="span" variant="fs-16" bold="bold" translate>
            contactsJourney.contactsSideBar.title
        </bds-typo>
        <div class="fr pr2">
            <bds-typo ng-click="$ctrl.closeSidebar()" id="contacts-side-bar-close-button">
                <bds-icon name="close" theme="outline" size="x-large"></bds-icon>
            </bds-typo>
        </div>
    </div>
    <div class="sidebar-content-body pa4">
        <card id="contacts-side-bar-summary">
            <div id="summary-texts">
                <bds-typo tag="p" variant="fs-14" bold="bold" no-wrap="true">
                    {{ $ctrl.selectedEdgeName }}
                </bds-typo>
                
            </div>
            <div id="summary-export-button" ng-class="{ 'pt4': $ctrl.isActiveMessageButtonEnabled }">
                <div class="area-button-summary-export">
                    <bds-typo tag="span" variant="fs-12" no-wrap="true">
                        {{ $ctrl.selectedEdgeContactsCount }} {{ "contactsJourney.contacts" | translate | lowercase }}
                    </bds-typo>
                    <bds-typo tag="span" variant="fs-12" no-wrap="true">
                        {{ $ctrl.selectedEdgeContactsPercentage }}
                    </bds-typo>
                    <bds-button id="active-campaign-button" variant="secondary" ng-click="$ctrl.generateAudience()"
                      size="standard" class="mh3 fr" ng-if="$ctrl.isShowingCSVExportButton && $ctrl.isActiveMessageButtonEnabled"
                      ng-disabled="$ctrl.isGeneratingCSV" translate>
                        {{ "activeMessages.sendMessageButton" | translate }}
                    </bds-button>
                </div>
                
                <div
                    tooltips
                    tooltip-template="{{'contactsJourney.contactsSideBar.export.tooltip' | translate}}"
                    tooltip-side="bottom"
                    tooltip-class="medium"
                    class="f3 pl2 tooltip-icon-hover">
                        <bds-button id="contacts-journey-export" variant="secondary" bold="bold" icon="download" ng-click="$ctrl.downloadCSV()"
                    size="standard" class="color-primary mh3 fr" ng-if="$ctrl.isShowingCSVExportButton"
                    bds-loading-variant="tertiary" bds-loading="{{$ctrl.isGeneratingCSV}}" ng-disabled="$ctrl.isGeneratingCSV" translate>
                        {{ "contactsJourney.contactsSideBar.export.button" | translate }}
                    </bds-button>
                </div>
            </div>
        </card>
        <div id="contacts-side-bar-content" class="pt4">
            <bds-typo class="mt4" tag="p" variant="fs-16" no-wrap="true" bold="semi-bold">
                {{ "contactsJourney.contacts" | translate }}
            </bds-typo>
            <div id="contacts-list-view" infinite-scroll="$ctrl.loadMore()" infinite-scroll-container="'#contacts-journey-side-bar'">
                <div ng-repeat="contact in $ctrl.edgeContacts track by $index">
                    <card class="cursor-pointer" ng-click="$ctrl.goToContactDetails(contact.identity)">
                        <div class="fl w-90">
                            <bds-typo class="bp-c-rooftop" tag="p" variant="fs-12">
                                {{ contact.name || contact.identity }}
                            </bds-typo>
                        </div>
                        <div class="fl w-10 ma1" ng-if="contact.sourceImage">
                            <img class="icon" width="20" src="{{contact.sourceImage}}" alt="{{contact.source}}">
                        </div>
                    </card>
                </div>
            </div>
            <div class="ma2" ng-if="$ctrl.isLoading">
                <bds-loading-spinner class="hydrated"></bds-loading-spinner>
            </div>
        </div>
    </div>
</bds-paper>
