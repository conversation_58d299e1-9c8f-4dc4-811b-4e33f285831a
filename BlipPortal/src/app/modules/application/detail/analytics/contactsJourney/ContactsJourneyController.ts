import './contactsJourney.scss';
import {
    cookies,
    IController, IDocumentService, IScope, ITimeoutService
} from 'angular';
import { DashboardMenuFeatures } from '../DashboardMenuFeatures';
import { ContactsJourneyFeatures } from './ContactsJourneyFeatures';
import { IStateService } from 'angular-ui-router';
import { dashboardGeneralStateName } from '..';
import { ContactsJourneyService, DATA_SOURCE } from '../services/ContactsJourneyService';
import { ContactsJourneyEdge } from 'modules/analytics/models/ContactsJourneyEdge';
import { groupBy } from 'data/array';
import { ContactsJourneyOptions } from 'modules/analytics/models/ContactsJourneyOptions';
import { FAILURE_STATUS, SegmentService, SUCCESS_STATUS } from 'modules/application/misc/SegmentService';
import { DaterangerPickerPeriod, Period } from 'interfaces/DaterangerPicker';
import { setupDaterangePickerPeriod, setupPeriodToCookies } from 'modules/analytics/Utils';
import moment from 'moment';
import TranslateService from 'modules/translate/TranslateService';
import { FlowConfigurationViewModel as FlowConfigurationViewModelFromSubmodule } from '../../portal-submodule-builder/templates/builder/models/viewModels/FlowConfigurationViewModel';
import { FlowConfigurationViewModel } from '../../templates/builder/models/viewModels/FlowConfigurationViewModel';
import { BuilderService as BuilderServiceFromSubmodule } from 'modules/application/detail/portal-submodule-builder/templates/builder/services/BuilderService';
import { BuilderService } from 'modules/application/detail/templates/builder/services/BuilderService';
import { ApplicationService2 } from 'modules/application/ApplicationService2';
import { ContactsJourneyInfoModalControler } from './contactsJourneyInfoModal/ContactsJourneyInfoModalControler';
import ContactsJourneyInfoModalView from './contactsJourneyInfoModal/ContactsJourneyInfoModalView.html';
import permissions from 'application/detail/permissions.json';
import { SidebarContentService } from 'modules/shared/SidebarContentService';
import ContactsJourneySideBarView from './ContactsJourneySideBarView.html';
import { ContactsJourneySideBarController } from './ContactsJourneySideBarController';
import { createJourneyTrack } from './utils/contactsJourneyUtils';

import {
    APPLICATION_EMPTY_DATA_DESCRIPTION,
    AUTO_TRACKING_ERROR,
    BEHAVIOR_DISPLAYED,
    BEHAVIOR_EXCEPTION,
    CONTACTS_LISTED_TRACK,
    CONTACTS_SIDE_BAR_ID,
    DATE_CHANGED_TRACK,
    DEFAULT_DATE_FORMAT,
    DIAGRAM_BEHAVIOR_TRACK,
    FIRST_NODE_CHANGED_TRACK,
    FULL_WIDTH,
    GERNERIC_ERROR,
    INFO_MODAL_TRACK,
    MASTER_EMPTY_DATA_DESCRIPTION,
    MAXIMUM_DAYS_TO_SELECT_FROM_TODAY,
    MOVED_SCROLL_TRACK,
    NO_DATA_FOUND_ERROR,
    ONE_STEP_WIDTH,
    PERIOD_RANGE_MAX,
    PERIOD_RANGE_MIN,
    PERIOD_TODAY,
    PERIOD_YESTERDAY,
    SANKEY_CHANGE_EVENT,
    SANKEY_OPTIONS,
    STATE_ID_START,
    STATE_NAME_NODE_EXIT,
    STATE_NAME_NODE_OTHERS,
} from './Constants';

export class ContactsJourneyController implements IController {

    isShowingContactsJourney: boolean = false;
    isShowingContactsButton: boolean = false;
    isContactsButtonAvailable: boolean = false;
    allJourneyEdges: Array<ContactsJourneyEdge> = [];
    displayedJourneyEdges: Array<ContactsJourneyEdge> = [];
    firstNodeFilterOptions: string;
    containerId: string = 'sankey-chart';
    changeEvent: string = SANKEY_CHANGE_EVENT;
    sankeyOptions: ContactsJourneyOptions = SANKEY_OPTIONS;
    daterangePickerPeriod: DaterangerPickerPeriod;
    cookiesPeriodKey: string;
    isLoading: boolean = true;
    automaticTrackingActive: boolean = false;
    noDataFound: boolean = false;
    configuration: FlowConfigurationViewModel|FlowConfigurationViewModelFromSubmodule;
    genericError: boolean = false;
    isMasterApplication: boolean = false;
    displayEmptyMasterMessage: boolean = false;
    noDataDescription: string = APPLICATION_EMPTY_DATA_DESCRIPTION;
    stepsCount: number = 0;
    labeledColorCardsInfo: Array<any> = [];
    currentUserPermissions: any;
    selectedEdge: ContactsJourneyEdge;
    selectedNodeUsersCount: number;
    isShowingCSVExportButton: boolean = false;
    isUsingCassandraDataSource: boolean = false;
    isShowingFirstNodeFilter: boolean = false;
    doNotTrackNextDateChange: boolean = true;
    firstNode: string;
    dataSource: DATA_SOURCE;

    constructor(
        private ModalService: any,
        private $state: IStateService,
        private ContactsJourneyService: ContactsJourneyService,
        private $rootScope,
        private $timeout: ITimeoutService,
        private SegmentService: SegmentService,
        private $cookies: cookies.ICookiesService,
        private TranslateService: TranslateService,
        private BuilderService: BuilderService|BuilderServiceFromSubmodule,
        private ApplicationService2: ApplicationService2,
        private SidebarContentService: SidebarContentService,
        private $document: IDocumentService,
        private $scope: IScope
    ) {
        'ngInject';

        this.cookiesPeriodKey = `${
            this.$rootScope.application.shortName
            }_contactsJourneyPeriodSetting`;
    }

    async $onInit() {
        this.currentUserPermissions = await this.getCurrentUserPermissions();
        await this.checkFeatures();
        this.isMasterApplication = this.ApplicationService2.isThisMasterApplication(this.$rootScope.application);
        if (!this.isMasterApplication) {
            await this.checkAutomaticTrackingEnabled();
        }
        this.initAndApplyFilter();
        this.setLabeledColorCards();
        this.listenToSelectedEdgeChange();
    }

    async getCurrentUserPermissions() {
        return Object.keys(permissions).reduce(
            (object, permissionName) => ({
                ...object,
                [permissionName]: this.$rootScope.application.applicationUserPermissionModel.some(
                    (p) =>
                        p.permissionClaim === permissions[permissionName].claim,
                ),
            }),
            {},
        );
    }

    isUserAllowedToViewContacts(): boolean {
        return this.currentUserPermissions.users;
    }

    async showContactsJourneyInfoModalModal() {
        createJourneyTrack(this.SegmentService, this.$rootScope.application, INFO_MODAL_TRACK);

        const modal = await this.ModalService.showModal({
            template: ContactsJourneyInfoModalView,
            controller: ContactsJourneyInfoModalControler,
            controllerAs: '$ctrl',
        });

        await modal.close;
    }

    listenForDiagramScrollUsage() {
        const diagramElement = document.getElementById('contacts-journey-content');
        let eventTimer;
        diagramElement.addEventListener('scroll', (event) => {
            clearTimeout(eventTimer);
            const maxScrollLeft = diagramElement.scrollWidth - diagramElement.clientWidth;
            const xAxisPosition = diagramElement.scrollLeft;
            if (xAxisPosition > 0) {
                eventTimer = setTimeout(() => {
                    this.trackPercentageDisplayed(xAxisPosition, maxScrollLeft);
                }, 1500);
            }
        });
    }

    trackPercentageDisplayed(xAxisPosition: number, maxScrollLeft: number) {
        const percentageDisplayed = (xAxisPosition / maxScrollLeft) * 100;
        createJourneyTrack(this.SegmentService, this.$rootScope.application, MOVED_SCROLL_TRACK, {
            stepsCount: this.stepsCount,
            percentageDisplayed: Math.round(percentageDisplayed)
        });
    }

    async checkAutomaticTrackingEnabled() {
        try {
            this.automaticTrackingActive = await this.BuilderService.isAutomaticTrackingPublished();
        } catch (e) {
            console.error(`Error while getting published flow data: ${e}`);
        } finally {
            if (!this.automaticTrackingActive) {
                this.setIsLoading(false);

                const behaviorTrackObject = {
                    behavior: BEHAVIOR_EXCEPTION,
                    exception: AUTO_TRACKING_ERROR
                };
                createJourneyTrack(this.SegmentService, this.$rootScope.application, DIAGRAM_BEHAVIOR_TRACK, behaviorTrackObject);
            }
        }
    }

    async checkFeatures() {
        this.isShowingContactsJourney = await DashboardMenuFeatures.isShowingContactsJourney();
        if (!this.isShowingContactsJourney) {
            this.$state.go(dashboardGeneralStateName);
        }
        this.isShowingContactsButton = await ContactsJourneyFeatures.isShowingContactsButton();
        this.isShowingCSVExportButton = await ContactsJourneyFeatures.isShowingCSVExportButton();
        this.isUsingCassandraDataSource = await ContactsJourneyFeatures.isUsingCassandraDataSource();
        this.isShowingFirstNodeFilter = await ContactsJourneyFeatures.isShowingFirstNodeFilter();
    }

    private async loadData() {
        const { startDate, endDate } = this.daterangePickerPeriod.selectedPeriod;
        const searchPeriod: Period = {
            startDate,
            endDate
        };

        try {
            this.allJourneyEdges = await this.getJourneyEdges(searchPeriod, this.firstNode);
            this.firstNodeFilterOptions = this.firstNodeFilterOptions ?? this.getFirstNodeFilterOptionsJson(this.allJourneyEdges);
            this.displayEmptyMasterMessage = this.isMasterApplication && this.allJourneyEdges.length === 0;
            this.noDataDescription = this.displayEmptyMasterMessage ? MASTER_EMPTY_DATA_DESCRIPTION : APPLICATION_EMPTY_DATA_DESCRIPTION;

            if (this.allJourneyEdges.length === 0) {
                this.noDataFound = true;

                const behaviorTrackObject = {
                    behavior: BEHAVIOR_EXCEPTION,
                    exception: NO_DATA_FOUND_ERROR
                };
                createJourneyTrack(this.SegmentService, this.$rootScope.application, DIAGRAM_BEHAVIOR_TRACK, behaviorTrackObject);
            } else {
                this.noDataFound = false;
                this.displayEmptyMasterMessage = false;
                this.$timeout(() => {
                    this.listenForDiagramScrollUsage();
                    this.updateJourneyData();
                });
                createJourneyTrack(this.SegmentService, this.$rootScope.application, DIAGRAM_BEHAVIOR_TRACK, {behavior: BEHAVIOR_DISPLAYED});
            }
        } catch (e) {
            this.genericError = true;

            const behaviorTrackObject = {
                behavior: BEHAVIOR_EXCEPTION,
                exception: GERNERIC_ERROR
            };
            createJourneyTrack(this.SegmentService, this.$rootScope.application, DIAGRAM_BEHAVIOR_TRACK, behaviorTrackObject);
            console.error('Error loading sankey diagram: ', e);
        } finally {
            this.setIsLoading(false);
        }
    }

    private async getJourneyEdges(searchPeriod: Period, firstNode: string = STATE_ID_START) {
        let allJourneyEdges: ContactsJourneyEdge[] = [];

        if (this.automaticTrackingActive || this.isMasterApplication) {
            this.dataSource = DATA_SOURCE.SQL;
            allJourneyEdges = await this.ContactsJourneyService.getJourneyEdges(searchPeriod, this.dataSource, firstNode);
        }

        if (allJourneyEdges.length === 0 && this.isUsingCassandraDataSource) {
            this.dataSource = DATA_SOURCE.CASSANDRA;
            allJourneyEdges = await this.ContactsJourneyService.getJourneyEdges(searchPeriod, this.dataSource, firstNode);
        }

        return allJourneyEdges;
    }

    private getFirstNodeFilterOptionsJson(edges: ContactsJourneyEdge[]) {
        const nodes = this.convertEdgesToNodeOptions(edges);
        const uniqueNodes = this.filterUniqueNodes(nodes);
        const sortedNodes = uniqueNodes.sort((a, b) => a.label > b.label ? 1 : -1);
        return JSON.stringify(sortedNodes);
    }

    private getNodeNameWithoutInstance(nameWithInstance: string) {
        return nameWithInstance.substring(0, nameWithInstance.lastIndexOf('[') - 1);
    }

    private convertEdgesToNodeOptions(edges: ContactsJourneyEdge[]) {
        return edges
        .map(x => ({'label': this.getNodeNameWithoutInstance(x.from), 'value': x.fromStateId}))
        .filter(x => ![STATE_NAME_NODE_OTHERS, STATE_NAME_NODE_EXIT].includes(x.label) && x.value !== STATE_ID_START);
    }

    private filterUniqueNodes(nodes: any[]) {
        return nodes.filter((item, index) => nodes.findIndex(e => e.label === item.label) === index);
    }

    updateJourneyData() {
        this.displayedJourneyEdges = this.normalizeEdgesToDisplay(this.allJourneyEdges);
        const groupedSteps = groupBy(this.allJourneyEdges, 'step');
        this.stepsCount = Object.keys(groupedSteps).length;
        this.updateJourneyView();

    }

    normalizeEdgesToDisplay(displayedEdges: Array<ContactsJourneyEdge>) {
        const exitName = this.TranslateService.instantTranslate('contactsJourney.exit');
        const othersName = this.TranslateService.instantTranslate('contactsJourney.othersNodeName');

        displayedEdges.forEach((edge: ContactsJourneyEdge) => {
            edge.to = edge.to
            .replace(STATE_NAME_NODE_OTHERS, othersName)
            .replace(STATE_NAME_NODE_EXIT, exitName);

            edge.from = edge.from
            .replace(STATE_NAME_NODE_OTHERS, othersName)
            .replace(STATE_NAME_NODE_EXIT, exitName);
        });

        return displayedEdges;
    }

    adjustJourneyViewWidth() {
        const viewWidth = ONE_STEP_WIDTH * this.stepsCount;
        const normalizedWidth = viewWidth < FULL_WIDTH ? FULL_WIDTH : viewWidth;
        const diagramElement = document.getElementById('diagram-body');
        if (diagramElement) {
            diagramElement.style.width = `${normalizedWidth}%`;
        }
    }

    updateJourneyView() {
        this.$timeout(() => {
            this.adjustJourneyViewWidth();
            this.$rootScope.$broadcast(this.changeEvent, this.containerId);
        });
    }

    initAndApplyFilter() {
        const periodConstants = {
            defaultStartFromToday: PERIOD_YESTERDAY,
            defaultEndFromToday: PERIOD_TODAY,
            validStartFromToday: PERIOD_RANGE_MIN,
            validEndFromToday: PERIOD_RANGE_MAX,
            maximumDaysToSelect: MAXIMUM_DAYS_TO_SELECT_FROM_TODAY
        };

        const hasTime = false;

        /* When 'this.dateRangePickerPeriod' receives its value, the 'blip-daterange-picker' calls 'this.applyDateRangeFilter()' and the search is made.
        This is defined in attribute 'on-date-selection' inside 'blip-daterange-picker'*/
        this.daterangePickerPeriod = setupDaterangePickerPeriod(periodConstants, this.$cookies, this.cookiesPeriodKey, hasTime);
    }

    applyFirstNodeFilter = (event: any) => {
        this.SegmentService.createBotTrack(
            FIRST_NODE_CHANGED_TRACK,
            this.$rootScope.application
        );

        this.firstNode = event.detail?.value;
        this.applyFilter();
    }

    applyDateRangeFilter() {
        if (this.doNotTrackNextDateChange) {
            this.doNotTrackNextDateChange = false;
        } else {
            this.SegmentService.createBotTrack(
                DATE_CHANGED_TRACK,
                this.$rootScope.application
            );
        }

        this.firstNodeFilterOptions = undefined;
        this.firstNode = undefined;

        this.applyFilter();
    }

    applyFilter() {
        this.genericError = false;
        this.noDataFound = false;
        this.displayEmptyMasterMessage = false;

        this.setIsLoading(true);
        this.isLoading = true;
        this.displayedJourneyEdges = [];
        this.updateJourneyView();

        const start = moment(this.daterangePickerPeriod.selectedPeriod.startDate)
            .format(DEFAULT_DATE_FORMAT);
        const end = moment(this.daterangePickerPeriod.selectedPeriod.endDate)
            .format(DEFAULT_DATE_FORMAT);

        const periodToCookies = {
            start,
            end,
        };
        setupPeriodToCookies({
            $cookies: this.$cookies,
            period: periodToCookies,
            key: this.cookiesPeriodKey,
        });

        this.loadData();
    }

    canShowDiagram(): boolean {
        return this.isShowingContactsJourney
            && !this.isLoading
            && !this.canShowNoDataView()
            && !this.canShowGenericError();
    }

    setIsLoading(isLoading: boolean) {
        this.isLoading = isLoading;
    }

    setLabeledColorCards() {
        const cards = [
            {type: 'default', color: 'colorPrimary'},
            {type: 'exit', color: 'colorExit'},
            {type: 'other', color: 'colorSurface4'}
        ];

        this.labeledColorCardsInfo = cards.map(card => ({
                    title: `contactsJourney.instructions.nodes.${card.type}.title`,
                    color: card.color,
                    text: `contactsJourney.instructions.nodes.${card.type}.description`
                })
            );
    }

    async openContactsSideBar() {
        let behavior = SUCCESS_STATUS;
        try {
            this.isContactsButtonAvailable && await this.SidebarContentService.showSidebar({
                controller: ContactsJourneySideBarController,
                template: ContactsJourneySideBarView,
                controllerAs: '$ctrl',
                appendToElement: this.$document[0].getElementById(
                    'main-content-area',
                ),
                inputs: {
                    selectedEdge: this.selectedEdge,
                    searchPeriod: this.daterangePickerPeriod.selectedPeriod,
                    dataSource: this.dataSource,
                    firstNode: this.firstNode,
                    application: this.$rootScope.application,
                    selectedNodeUsersCount: this.selectedNodeUsersCount,
                    isShowingCSVExportButton: this.isShowingCSVExportButton
                },
                sidebarId: CONTACTS_SIDE_BAR_ID,
            });
        } catch (e) {
            behavior = FAILURE_STATUS;
        } finally {
            createJourneyTrack(this.SegmentService, this.$rootScope.application, CONTACTS_LISTED_TRACK, {
                behavior,
                nodeType: this.selectedEdge.type,
                nodeName: this.selectedEdge.name
            });
        }
    }

    listenToSelectedEdgeChange() {
        this.$scope.$watch('$ctrl.selectedEdge', () => {
            this.SidebarContentService.cleanAndClose(false);
        });
    }

    canShowLoading(): boolean {
        return this.isLoading;
    }

    canShowNoDataView(): boolean {
        return !this.canShowLoading() && (this.noDataFound || this.displayEmptyMasterMessage);
    }

    canShowEmptyMasterApplicationMessage(): boolean {
        return this.displayEmptyMasterMessage;
    }

    canShowGenericError(): boolean {
        return this.genericError;
    }

    canShowHeader(): boolean {
        return this.canShowDiagram() || this.canShowNoDataView();
    }

    canCurrentUserViewContacts(): boolean {
        return this.isShowingContactsButton
            && this.isUserAllowedToViewContacts();
    }
}
