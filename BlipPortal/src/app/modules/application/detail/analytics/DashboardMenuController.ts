import {
    IController,
} from 'angular';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { DashboardMenuFeatures } from './DashboardMenuFeatures';

export class DashboardMenuController implements IController {

    isShowingContactsJourney: boolean = false;
    isHidingContactsJourneyBetaTag: boolean = false;

    constructor(
        private SegmentService: SegmentService,
        private application
    ) {
        'ngInject';
    }

    async $onInit() {
        await this.checkFeatures();
    }

    async checkFeatures() {
        this.isShowingContactsJourney = await DashboardMenuFeatures.isShowingContactsJourney();
        this.isHidingContactsJourneyBetaTag = await DashboardMenuFeatures.isHidingContactsJourneyBetaTag();
    }

    trackOpenTab(trackEvent: string) {
        this.SegmentService.createApplicationTrack({
            trackEvent,
            application: this.application
        });
    }
}
