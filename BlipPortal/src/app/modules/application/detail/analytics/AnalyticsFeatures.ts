import { FeatureToggleClientService } from 'feature-toggle-client';

export const isHidingAnalyticsTabs = 'is-hiding-analytics-tabs';
export const isUsingCommandsWithDelegation = 'messaging-hub-command-with-delegation';
export const isDisplayingAnalyticsOverviewTab = 'is-displaying-analytics-overview-tab';
export const isDisplayingAnalyticsActiveMessagesTab = 'is-displaying-analytics-active-messages-tab';
export const isShowingDataDictionary = 'is-showing-data-dictionary';
export const isShowingDataExtractor = 'is-showing-data-extractor';
export const isShowingGoodData = 'is-showing-gooddata';
export const enableLakeDatasource = 'enable-lake-datasource';

export class AnalyticsFeatures {

    static isHidingAnalyticsTabs(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isHidingAnalyticsTabs
        );
    }

    static isDisplayingAnalyticsOverviewTab(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isDisplayingAnalyticsOverviewTab
        );
    }

    static isDisplayingAnalyticsActiveMessagesTab(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isDisplayingAnalyticsActiveMessagesTab
        );
    }

    static isShowingDataDictionary(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isShowingDataDictionary
        );
    }

    static isShowingDataExtractor(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isShowingDataExtractor
        );
    }

    static isShowingGoodData(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            isShowingGoodData
        );
    }

    static enableLakeDatasource(): Promise<boolean> {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            enableLakeDatasource
        );
    }
}
