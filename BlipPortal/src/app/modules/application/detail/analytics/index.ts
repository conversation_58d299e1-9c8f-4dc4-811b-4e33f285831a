import * as angular from 'angular';
import permissions from 'application/detail/permissions.json';
import { IStateProvider, IStateService } from 'angular-ui-router';

//Modules
import 'angular-ui-router';
import 'angularjs-datepicker';
import 'angularjs-datepicker/dist/angular-datepicker.css';
import analytics from 'modules/analytics';
import shared from 'modules/shared';
import statistics from 'modules/statistics';
import { analyticsComponents } from './components';

// Views
import DashboardMenuView from './DashboardMenuView.html';
import ReportView from './report/ReportView.html';
import ReportsView from './reports/ReportsView.html';
import GeneralDashboardView from './generalDashboard/GeneralDashboardView.html';
import ContactsJourneyView from './contactsJourney/ContactsJourneyView.html';
import AnalyticsTabsView from './tabs/AnalyticsTabsView.html';
import DataExtractorView from './dataExtractor/DataExtractorView.html';
import GoodDataView from './goodData/GoodDataView.html';

// Controllers
import { DashboardMenuController } from './DashboardMenuController';
import { GeneralDashboardController } from './generalDashboard/GeneralDashboardController';
import { ReportController } from 'modules/application/detail/analytics/report/ReportController';
import { ReportsController } from 'modules/application/detail/analytics/reports/ReportsController';
import { ContactsJourneyController } from './contactsJourney/ContactsJourneyController';
import { DataExtractorController } from './dataExtractor/DataExtractorController';
import { GoodDataController } from './goodData/GoodDataController';

// Services
import { GeneralDashboardService } from './generalDashboard/GeneralDashboardService';
import { AnalyticsReportsService } from './services/AnalyticsReportsService';
import { AnalyticsChartsService } from './services/AnalyticsChartsService';
import { ContactsJourneyService } from './services/ContactsJourneyService';
import { AnalyticsTabsController } from './tabs/AnalyticsTabsController';
import { AnalyticsFeatures } from './AnalyticsFeatures';
import { getServiceAsync } from 'data/function';
import { EVENTS } from './utils/AnalyticsConstants';
import { MicroFrontendService } from 'modules/shared/MicroFrontendService';

// Constants
import { analyticsScriptSource, goodDataScriptSource } from 'app.constants';

//Old routes
export const dashboardStateName = 'auth.application.detail.dashboard';
export const dashboardGeneralStateName = 'auth.application.detail.dashboard.general';
export const reportsStateName = 'auth.application.detail.dashboard.reports';
export const reportStateName = 'auth.application.detail.dashboard.reports.report';
export const contactsJourneyStateName = 'auth.application.detail.dashboard.contactsJourney';

//New routes
export const analyticsStateName = 'auth.application.detail.analytics';
export const analyticsDashboardStateName = 'auth.application.detail.analytics.dashboard';
export const analyticsActiveMessagesStateName = 'auth.application.detail.analytics.activeMessages';
export const analyticsDataDictionaryStateName = 'auth.application.detail.analytics.dataDictionary';
export const analyticsOverviewStateName = 'auth.application.detail.analytics.overview'; // old screen, previously called (general) dashboard
export const analyticsReportsStateName = 'auth.application.detail.analytics.reports';
export const analyticsReportStateName = 'auth.application.detail.analytics.reports.report';
export const analyticsContactsJourneyStateName = 'auth.application.detail.analytics.contactsJourney';
export const analysisDataExtractorStateName = 'auth.application.detail.analytics.dataExtractor';
export const analysisGoodDataStateName = 'auth.application.detail.analytics.goodData';

const ENTERING_ACTION = 'entering';
const EXITING_ACTION = 'exiting';

const GENERAL_DASHBOARD = 'general_dashboard';
const ANALYTICS_DASHBOARD = 'analytics_dashboard';

const handleReportDetailActions = async (direction: string) => {
    const isExiting = direction === EXITING_ACTION;
    isExiting && emitReportUpdatedEvent();

    const tabsContentElement = document.getElementById('tabsContent');
    tabsContentElement && handleTabsContentExibition(tabsContentElement, isExiting);
};

const onEnterAnalyticsRoute = async ($state: IStateService, source: string) => {
    const isDisplayingAnalyticsTabs = !await AnalyticsFeatures.isHidingAnalyticsTabs();
    if (isDisplayingAnalyticsTabs && source === GENERAL_DASHBOARD) {
        $state.go(analyticsStateName);
    } else if (!isDisplayingAnalyticsTabs && source == ANALYTICS_DASHBOARD) {
        $state.go(dashboardStateName);
    }
};

const emitReportUpdatedEvent = async () => {
    const $rootScope = await getServiceAsync('$rootScope');
    $rootScope.$emit(EVENTS.REPORTS_UPDATED);
};

const handleTabsContentExibition = (tabsContentElement: HTMLElement, isExiting: boolean) => {
    tabsContentElement.style.display = isExiting ? 'block' : 'none';
};

export default angular
    .module('application.analytics', [
        '720kb.datepicker',
        'ui.router',
        analytics,
        shared,
        statistics,
        analyticsComponents,
    ])
    .service('GeneralDashboardService', GeneralDashboardService)
    .service('AnalyticsReportsService', AnalyticsReportsService)
    .service('AnalyticsChartsService', AnalyticsChartsService)
    .service('ContactsJourneyService', ContactsJourneyService)
    .controller('ReportController', ReportController)
    .controller('ReportsController', ReportsController)
    .config(($stateProvider: IStateProvider) => {
        'ngInject';

        $stateProvider
            .state(dashboardStateName, {
                url: '/dashboard',
                redirectTo: dashboardGeneralStateName,
                views: {
                    '<EMAIL>': {
                        template: DashboardMenuView,
                        controller: DashboardMenuController,
                        controllerAs: '$ctrl',
                    },
                },
                onEnter: ($state: IStateService) => {
                    onEnterAnalyticsRoute($state, GENERAL_DASHBOARD);
                    return MicroFrontendService.addSource(analyticsScriptSource);
                },
            })
            .state(dashboardGeneralStateName, {
                url: '/general',
                views: {
                    '<EMAIL>': {
                        template: GeneralDashboardView,
                        controller: GeneralDashboardController,
                        controllerAs: '$ctrl',
                    },
                },
                params: { area: 'analysis' },
            })
            .state(reportsStateName, {
                url: '/reports',
                views: {
                    '<EMAIL>': {
                        template: ReportsView,
                        controller: ReportsController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.analysis.claim,
                },
                params: { area: 'analysis' },
            })
            .state(reportStateName, {
                url: '/:reportId',
                views: {
                    '<EMAIL>': {
                        template: ReportView,
                        controller: ReportController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.analysis.claim,
                },
                params: { area: 'analysis' },
            })
            .state(contactsJourneyStateName, {
                url: '/contactsJourney',
                views: {
                    '<EMAIL>': {
                        template: ContactsJourneyView,
                        controller: ContactsJourneyController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.analysis.claim,
                },
                params: { area: 'analysis' },
            })
            .state(analysisDataExtractorStateName, {
                url: '/dataExtractor',
                views: {
                    '<EMAIL>': {
                        template: DataExtractorView,
                        controller: DataExtractorController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.analysis.claim,
                },
                params: { area: 'analysis' },
            })
            .state(analyticsStateName, {
                url: '/analytics',
                redirectTo: analyticsDashboardStateName,
                views: {
                    '<EMAIL>': {
                        template: AnalyticsTabsView,
                        controller: AnalyticsTabsController,
                        controllerAs: '$ctrl',
                    },
                },
                params: { area: 'analysis' },
                onEnter: ($state: IStateService) => {
                    onEnterAnalyticsRoute($state, ANALYTICS_DASHBOARD);
                    return MicroFrontendService.addSource(analyticsScriptSource);
                },
            })
            .state(analyticsDashboardStateName, {
                url: '/dashboard',
                params: { area: 'analysis' },
            })
            .state(analyticsActiveMessagesStateName, {
                url: '/activeMessages',
                params: { area: 'analysis' },
            })
            .state(analyticsDataDictionaryStateName, {
                url: '/dataDictionary',
                params: { area: 'analysis' },
            })
            .state(analyticsOverviewStateName, {
                url: '/overview',
                params: { area: 'analysis' },
            })
            .state(analyticsReportsStateName, {
                url: '/reports',
                params: { area: 'analysis' },
            })
            .state(analyticsReportStateName, {
                url: '/:reportId',
                views: {
                    '<EMAIL>': {
                        template: ReportView,
                        controller: ReportController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.analysis.claim,
                },
                params: { area: 'analysis' },
                onEnter: () => {
                    handleReportDetailActions(ENTERING_ACTION);
                },
                onExit: () => {
                    handleReportDetailActions(EXITING_ACTION);
                },
            })
            .state(analyticsContactsJourneyStateName, {
                url: '/contactsJourney',
                params: { area: 'analysis' },
            })
            .state(analysisGoodDataStateName, {
                url: '/goodData',
                params: { area: 'analysis' },
                views: {
                    '<EMAIL>': {
                        template: GoodDataView,
                        controller: GoodDataController,
                        controllerAs: '$ctrl',
                    },
                },
                onEnter: () => {
                    return MicroFrontendService.addSource(goodDataScriptSource);
                },
            });
    }).name;
