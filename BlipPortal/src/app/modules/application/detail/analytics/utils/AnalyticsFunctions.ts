import { CONSTANTS } from './AnalyticsConstants';

export const MinimunDatePeriod = (): Date => {
    const today = new Date();
    const minRange = new Date(new Date().setDate(today.getDate() - CONSTANTS.MIN_RANGE));
    return minRange;
};

export const MinimunDatePeriodExtension = (): Date => {
    const today = new Date();
    today.setDate(today.getDate() - CONSTANTS.MIN_RANGE_EXTENDED);
    return today;
};