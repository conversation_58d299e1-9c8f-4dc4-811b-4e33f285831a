import { FeatureToggleClientService } from 'feature-toggle-client';

export const showingContactsJourney = 'showing-contacts-journey';
export const hidingContactsJourneyBetaTag = 'contacts-journey-hiding-beta-tag';

export class DashboardMenuFeatures {
    static async isShowingContactsJourney() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            showingContactsJourney
        );
    }

    static async isHidingContactsJourneyBetaTag() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            hidingContactsJourneyBetaTag
        );
    }
}
