import { ViewReport } from 'modules/analytics/models';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { AccountService2 } from 'modules/account/AccountService2';
import { CacheService } from 'modules/cache/CacheService';

const ReportOwnerCacheTimeout = 300000;

export class AnalyticsReportsService {
    constructor(
        private MessagingHubService: MessagingHubService,
        private AccountService2: AccountService2,
        private CacheService: CacheService,
        private MESSAGINGHUBDOMAIN: string
        ) {}

    async getMany(owner: string, skip = 0, take = 100): Promise<{ reports: ViewReport[]}> {
        const { items } = await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'get',
            uri: `/reports?ownerUser=${encodeURIComponent(owner)}&$skip=${skip}&$take=${take}`,
        });

        const promises = [];
        for (const item of items) {
            const promise = this.CacheService.getOrAdd(
                `${item.ownerUserIdentity}:account`,
                () => this.AccountService2.get(encodeURIComponent(item.ownerUserIdentity.split('@')[0])),
                ReportOwnerCacheTimeout)
            .then(resource => {
                if (resource) {
                    item.owner = { email: resource.email, fullName: resource.fullName, phoneNumber: resource.phoneNumber };
                } else {
                    item.owner = { email: decodeURIComponent(item.ownerUserIdentity.split('@')[0]) };
                }
            })
            .catch(e => {
                item.owner = { email: decodeURIComponent(item.ownerUserIdentity.split('@')[0]) };
            });
            promises.push(promise);
        }

        await Promise.all(promises);

        return { reports: items};
    }

    async get(reportId: string): Promise<ViewReport> {
        const report = await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'get',
            uri: `/reports/${reportId}`,
        });

        const account = await this.AccountService2.get(encodeURIComponent(report.ownerUserIdentity.split('@')[0]));
        report.owner = { email: account.email, fullName: account.fullName, phoneNumber: account.phoneNumber };

        return report;
    }

    async set(report: ViewReport): Promise<string> {
        const resource = await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'set',
            type: 'application/vnd.iris.report+json',
            uri: '/reports',
            resource: report,
        });

        return resource.id;
    }

    async delete(reportId: string) {
        return await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'delete',
            uri: `/reports/${reportId}`,
        });
    }
}
