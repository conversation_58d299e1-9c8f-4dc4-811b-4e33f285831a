import { Period } from 'interfaces/DaterangerPicker';
import { ContactsJourneyContact } from 'modules/analytics/models/ContactsJourneyContact';
import { ContactsJourneyEdge, EdgeTypes } from 'modules/analytics/models/ContactsJourneyEdge';
import { ContactsJourneyCsv } from 'modules/analytics/models/ContactsJourneyCsv';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import moment from 'moment';
import { CONTACTS_USERS_PER_TAKE } from '../contactsJourney/Constants';
import { getNodeName } from '../contactsJourney/utils/contactsJourneyUtils';

const SEARCH_DATE_FORMAT = 'YYYY-MM-DDTHH:mm:ssZ';
const JOURNEY_TIMEOUT = 300000;

export enum DATA_SOURCE {
    CASSANDRA = 'cassandra',
    SQL = 'sql'
}

export class ContactsJourneyService {

    constructor(
        private MessagingHubService: MessagingHubService,
    ) {}

    public async getJourneyEdges(searchPeriod: Period, dataSource: DATA_SOURCE, firstNode: string): Promise<ContactsJourneyEdge[]> {
        const startDate = moment(searchPeriod.startDate).format(SEARCH_DATE_FORMAT);
        const endDate = moment(searchPeriod.endDate).format(SEARCH_DATE_FORMAT);

        const commandResponse = await this.MessagingHubService.processCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/contacts-journey?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}&dataSource=${dataSource}&timeout=${JOURNEY_TIMEOUT}&firstNode=${firstNode}`,
            }, JOURNEY_TIMEOUT
        );

        return this.MessagingHubService.ensureCommandSuccess(commandResponse).resource.items;
    }

    public async getEdgeContacts(searchPeriod: Period, selectedJourneyEdge: ContactsJourneyEdge, dataSource: DATA_SOURCE, firstNode: string, skip: number = 0, take: number = CONTACTS_USERS_PER_TAKE): Promise<ContactsJourneyContact[]> {
        const startDate = moment(searchPeriod.startDate).format(SEARCH_DATE_FORMAT);
        const endDate = moment(searchPeriod.endDate).format(SEARCH_DATE_FORMAT);
        const { selectedNodeName } = getNodeName(selectedJourneyEdge);

        const commandResponse = await this.MessagingHubService.processCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/contacts-journey/${encodeURIComponent(selectedNodeName)}/users?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}&skip=${skip}&take=${take}&timeout=${JOURNEY_TIMEOUT}&firstNode=${firstNode}&dataSource=${dataSource}`
            }, JOURNEY_TIMEOUT
        );

        return this.MessagingHubService.ensureCommandSuccess(commandResponse).resource.items;
    }

    public async getJourneyCSVData(searchPeriod: Period, selectedJourneyEdge: ContactsJourneyEdge, dataSource: DATA_SOURCE, firstNode: string): Promise<ContactsJourneyCsv> {

        const startDate = moment(searchPeriod.startDate).format(SEARCH_DATE_FORMAT);
        const endDate = moment(searchPeriod.endDate).format(SEARCH_DATE_FORMAT);
        const { selectedNodeName } = getNodeName(selectedJourneyEdge);

        const commandResponse = await this.MessagingHubService.processCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/contacts-journey/${encodeURIComponent(selectedNodeName)}/download-users-csv?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}&firstNode=${firstNode}&dataSource=${dataSource}`
            }
        );

        return this.MessagingHubService.ensureCommandSuccess(commandResponse).resource;
    }

}
