import { <PERSON><PERSON><PERSON> } from 'modules/analytics/models';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { Chart } from '../report/chartModal/ChartModalController';

export class AnalyticsChartsService {
    constructor(
        private MessagingHubService: MessagingHubService,
        private MESSAGINGHUBDOMAIN: string
    ) {}

    async getMany(reportId: string, skip = 0, take = 100): Promise<{ charts: ViewChart[] }> {
        const { items } = await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'get',
            uri: `/reports/${reportId}/charts?$skip=${skip}&$take=${take}`,
        });
        return { charts: items };
    }

    async set(reportId: string, chart: Chart): Promise<string> {
        const resource = await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'set',
            type: 'application/vnd.iris.chart+json',
            uri: `/reports/${reportId}/charts`,
            resource: chart,
        });

        return resource.id;
    }

    async setMany(reportId: string, charts: Chart[]): Promise<string[]> {
        const resources = await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'set',
            type: 'application/vnd.lime.collection+json',
            uri: `/reports/${reportId}/charts`,
            resource: {
                itemType: 'application/vnd.iris.chart+json',
                items: charts,
            },
        });

        return resources.items.map( r => r.id);
    }

    async delete(reportId: string, chartId: string) {
        return await this.MessagingHubService.sendCommand({
            to: `postmaster@analytics.${this.MESSAGINGHUBDOMAIN}`,
            method: 'delete',
            uri: `/reports/${reportId}/charts/${chartId}`,
        });
    }
}
