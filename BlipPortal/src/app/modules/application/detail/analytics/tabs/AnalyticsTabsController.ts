import './AnalyticsTabs.scss';
import { IController, IRootScopeService } from 'angular';
import { IStateService } from 'angular-ui-router';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { AnalyticsFeatures } from '../AnalyticsFeatures';
import {
    analysisDataExtractorStateName,
    analysisGoodDataStateName,
    analyticsContactsJourneyStateName,
    analyticsDashboardStateName,
    analyticsDataDictionaryStateName,
    analyticsOverviewStateName,
    analyticsActiveMessagesStateName,
    analyticsReportsStateName,
} from '..';

const SegmentTracks = {
    dashboard: 'analytics-dashboard-v2-opened',
    activeMessages: 'active-messages-opened',
    overview: 'analytics-dashboard-opened',
    reports: 'analytics-custom-reports-opened',
    contactsJourney: 'analytics-journey-opened',
    dataDictionary: 'analytics-datadictionary-opened',
    dataExtractor: 'analytics-dataExtractor-opened'
};
type TabsData = {
    dashboard: { active: boolean, route: string },
    activeMessages: { active: boolean, route: string },
    overview: { active: boolean, route: string },
    reports: { active: boolean, route: string },
    contactsJourney: { active: boolean, route: string },
    dataDictionary: { active: boolean, route: string },
    dataExtractor: { active: boolean, route: string },
    goodData: { active: boolean, route: string }
};

export class AnalyticsTabsController implements IController {

    $rootScope: IRootScopeService;
    isDisplayingOverviewTab: boolean = false;
    isDisplayingActiveMessagesTab: boolean = false;
    isShowingDataDictionary: boolean = false;
    isShowingDataExtractor: boolean = false;
    isShowingGoodData: boolean = false;
    queryParams: string | undefined;
    tabData: TabsData = {
        dashboard: { active: false, route: analyticsDashboardStateName},
        activeMessages: { active: false, route: analyticsActiveMessagesStateName},
        overview: { active: false, route: analyticsOverviewStateName},
        reports:  { active: false, route: analyticsReportsStateName},
        contactsJourney:  { active: false, route: analyticsContactsJourneyStateName},
        dataDictionary:  { active: false, route: analyticsDataDictionaryStateName},
        dataExtractor: {active: false, route: analysisDataExtractorStateName},
        goodData: { active: false, route: analysisGoodDataStateName}
    };

    constructor(
        private $state: IStateService,
        private SegmentService: SegmentService,
        private application
    ) {
        'ngInject';
    }

    $onInit = async () => {
        await this.checkFeatures();
        this.initilizeTabs();
        this.handlePosition();
        window.addEventListener('scroll', () => this.handlePosition());
    }

    onClickTab(tabName: string) {
        this.enableTabController(tabName);
        this.trackOpenTab(tabName);
        this.$state.go(this.tabData[tabName].route);
    }

    private initilizeTabs = () => {
        let currentPage = this.$state.current.url.toString().replace('/', '');

        if (currentPage.includes('report')) {
            currentPage = this.handleSingleReportRedirect(currentPage);
        } else if (currentPage.includes('dataDictionary')) {
            this.handleDataDictionaryQueryParams();
        }

        this.tabData[currentPage].active = true;
    }

    private handleDataDictionaryQueryParams() {
        const uri = window.location.href;
        if (uri.includes('?path')) {
            const params = uri.split('?')[1];
            this.queryParams = params.split('=')[1];
        }
    }

    private handleSingleReportRedirect(currentPage: string) {
        currentPage = 'reports';
        this.$state.go(this.tabData[currentPage].route);
        return currentPage;
    }

    private enableTabController(tabName: string) {
        this.tabData[tabName].active = true;
        this.handlePosition();
    }

    private trackOpenTab(tabName: string) {
        this.SegmentService.createApplicationTrack({
            trackEvent: SegmentTracks[tabName],
            application: this.application
        });
    }

    private checkFeatures = async () => {
        this.isDisplayingOverviewTab = await AnalyticsFeatures.isDisplayingAnalyticsOverviewTab();
        this.isDisplayingActiveMessagesTab = await AnalyticsFeatures.isDisplayingAnalyticsActiveMessagesTab();
        this.isShowingDataDictionary = await AnalyticsFeatures.isShowingDataDictionary();
        this.isShowingDataExtractor = await AnalyticsFeatures.isShowingDataExtractor();
        this.isShowingGoodData = await AnalyticsFeatures.isShowingGoodData();
    }

    handlePosition = (shouldTriggerReposition = true) => {
        const mainBarBottom = document.querySelector('main-navbar')?.getBoundingClientRect()?.bottom;
        if (mainBarBottom) {
            const bdsTabs = document.querySelector('bds-tabs') as HTMLElement;
            if (bdsTabs && bdsTabs.style) {
                bdsTabs.style.top = `${mainBarBottom}px`;
            }
            if (shouldTriggerReposition) {
                setTimeout(() => this.handlePosition(false), 500);
            }
        }
    }
}
