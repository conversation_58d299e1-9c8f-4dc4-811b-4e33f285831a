<div id="analytics-tabs-view">
    <bds-tabs class="tabs-header">
        <bds-tab
          group="dashboard"
          label="{{'analyticsTabs.dashboard' | translate}}"
          ng-click="$ctrl.onClickTab('dashboard')"
          id="dashboardHeader"
          active="{{$ctrl.tabData['dashboard'].active}}"
        ></bds-tab>
        <bds-tab
          group="activeMessages"
          ng-if="$ctrl.isDisplayingActiveMessagesTab"
          label="{{'analyticsTabs.activeMessages' | translate}}"
          ng-click="$ctrl.onClickTab('activeMessages')"
          ui-sref="auth.application.detail.analytics.activeMessages"
          id="activeMessagesHeader"
          active="{{$ctrl.tabData['activeMessages'].active}}"
          class="tabHeader"
      ></bds-tab>
        <bds-tab
          group="overview"
          ng-if="$ctrl.isDisplayingOverviewTab"
          label="{{'analyticsTabs.overview' | translate}}"
          ng-click="$ctrl.onClickTab('overview')"
          id="overviewHeader"
          active="{{$ctrl.tabData['overview'].active}}"
        ></bds-tab>
        <bds-tab
          group="reports"
          label="{{'analyticsTabs.reports' | translate}}"
          ng-click="$ctrl.onClickTab('reports')"
          id="reportsHeader"
          active="{{$ctrl.tabData['reports'].active}}"
        ></bds-tab>
        <bds-tab
          group="contactsJourney"
          label="{{'analyticsTabs.contactsJourney' | translate}}"
          ng-click="$ctrl.onClickTab('contactsJourney')"
          id="contactsJourneyHeader"
          active="{{$ctrl.tabData['contactsJourney'].active}}"
        ></bds-tab>
        <bds-tab
          group="dataExtractor"
          ng-if="$ctrl.isShowingDataExtractor"
          label="{{'analyticsTabs.dataExtractor' | translate}}"
          ng-click="$ctrl.onClickTab('dataExtractor')"
          ui-sref="auth.application.detail.analytics.dataExtractor"
          id="dataExtractorHeader"
          active="{{$ctrl.tabData['dataExtractor'].active}}"
          class="tabHeader"
      ></bds-tab>
        <bds-tab
          group="dataDictionary"
          ng-if="$ctrl.isShowingDataDictionary"
          label="{{'analyticsTabs.dataDictionary' | translate}}"
          ng-click="$ctrl.onClickTab('dataDictionary')"
          ui-sref="auth.application.detail.analytics.dataDictionary"
          id="dataDictionaryHeader"
          active="{{$ctrl.tabData['dataDictionary'].active}}"
          class="tabHeader"
        ></bds-tab>
        <bds-tab
          group="goodData"
          ng-if="$ctrl.isShowingGoodData"
          label="{{'analyticsTabs.goodData' | translate}}"
          ng-click="$ctrl.onClickTab('goodData')"
          id="goodDataHeader"
          active="{{$ctrl.tabData['goodData'].active}}"
          class="tabHeader"
        ></bds-tab>
    </bds-tabs>
    <div class="tabs-content" id="tabsContent">
        <bds-tab-panel 
          group="dashboard"
          class="tabContent"
          id="dashboardContent">
            <dashboard ng-if="$ctrl.tabData['dashboard'].active" page="dashboard"></dashboard>
        </bds-tab-panel>
        <bds-tab-panel 
          group="activeMessages"
          ng-if="$ctrl.isDisplayingActiveMessagesTab"
          class="tabContent"
          id="activeMessagesContent">
            <dashboard ng-if="$ctrl.tabData['activeMessages'].active" page="activeMessages" params="{{$ctrl.queryParams}}"></dashboard>
        </bds-tab-panel>
        <bds-tab-panel 
          group="overview"
          ng-if="$ctrl.isDisplayingOverviewTab"
          class="tabContent"
          id="overviewContent">
            <general-dashboard ng-if="$ctrl.tabData['overview'].active"></general-dashboard>
        </bds-tab-panel>
        <bds-tab-panel 
          group="reports"
          class="tabContent"
          id="reportsContent">
            <custom-reports ng-if="$ctrl.tabData['reports'].active"></custom-reports>
        </bds-tab-panel>
        <bds-tab-panel
          group="contactsJourney"
          class="tabContent"
          id="contactsJourneyContent">
          <contacts-journey ng-if="$ctrl.tabData['contactsJourney'].active"></contacts-journey>
        </bds-tab-panel>
        <bds-tab-panel 
          group="dataExtractor"
          class="tabContent"
          id="dataExtractorContent">
            <data-extractor ng-if="$ctrl.tabData['dataExtractor'].active" page="dataExtractor" params="{{$ctrl.queryParams}}"></data-extractor>
        </bds-tab-panel>
        <bds-tab-panel 
          group="dataDictionary"
          ng-if="$ctrl.isShowingDataDictionary"
          class="tabContent"
          id="dataDictionaryContent">
            <dashboard ng-if="$ctrl.tabData['dataDictionary'].active" page="dataDictionary" params="{{$ctrl.queryParams}}"></dashboard>
        </bds-tab-panel>
        <bds-tab-panel 
          group="goodData"
          ng-if="$ctrl.isShowingGoodData"
          class="tabContent"
          id="goodDataContent">
            <good-data ng-if="$ctrl.tabData['goodData'].active" page="goodData" params="{{$ctrl.queryParams}}"></good-data>
        </bds-tab-panel>
    </div>
</div>
