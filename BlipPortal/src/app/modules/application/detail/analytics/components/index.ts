import * as angular from 'angular';
import { IComponentOptions } from 'angular';
import SankeyDiagramComponent from './sankeyDiagram/sankeyDiagram.component';
import LabeledColorCard from './labeledColorCard/labeledColorCard.component';
import CounterChildCard from './counterChildCard/counterChildCard.component';
import GeneralDashboard from './tabs/generalDashboard.component';
import CustomReports from './tabs/customReports.component';
import ContactsJourney from './tabs/contactsJourney.component';
import Dashboard from './tabs/dashboard.component';

export const analyticsComponents = angular
    .module('analyticsComponents')
    .component('generalDashboard', <IComponentOptions>GeneralDashboard)
    .component('customReports', <IComponentOptions>CustomReports)
    .component('contactsJourney', <IComponentOptions>ContactsJourney)
    .component('sankeyDiagram', <IComponentOptions>SankeyDiagramComponent)
    .component('labeledColorCard', <IComponentOptions>LabeledColorCard)
    .component('counterChildCard', <IComponentOptions>CounterChildCard)
    .component('dashboard', <IComponentOptions>Dashboard)
    .name;
