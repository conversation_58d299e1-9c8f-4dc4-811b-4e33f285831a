import { IComponentController } from 'angular';
import template from './CounterChildCardView.html';
import * as styles from './counterChildCard.scss';

class CounterChildCardController implements IComponentController {
    styles: any;

    constructor(private $element, private $timeout) {
        this.styles = styles;
    }
}

const CounterChildCardComponent = {
    template,
    controller: CounterChildCardController,
    controllerAs: '$ctrl',
    bindings: {
        name: '<',
        value: '<',
        tooltip: '<',
        loaded: '<'
    },
};

export default CounterChildCardComponent;
