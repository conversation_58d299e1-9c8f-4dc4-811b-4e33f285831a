@import "~assets/scss/main";
@import '~blip-ds/dist/collection/styles/colors';

counter-child-card {
    flex: 1;
}

.counter-child-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 8px;
    padding: 10px;
    margin-top: 20px;
    height: 11vh;
    color: $color-content-default;

    .sk-circle {
        width: 5vh;
        height: 5vh;
    }
    .icon {
        margin-left: 5px;
    }
}

@media all and (max-width:1120px) {
    .counter-child-card .counter-child-value .icon {
        font-size: 16px;
    }
}