@import '~blip-ds/dist/collection/styles/colors';
@import "~assets/scss/main";
@import "../../Colors.scss";

.labeled-color-card{
    border: 1px solid $color-surface-3;
    border-left-width: 10px;
    height: 100%;
    color: $color-content-default !important;

    &.colorPrimary{
        border-left-color: $color-primary-main;
    }

    &.colorExit{
        border-left-color: $extended_orange;
    }

    &.colorSurface4{
        border-left-color: $color-surface-4;
    }

    div div div.card-content{
        padding: 0;
    }
}    

