import { IComponentController, IRootScopeService } from 'angular';
import { ContactsJourneyEdge, EdgeOrder, EdgeTypes } from 'modules/analytics/models/ContactsJourneyEdge';
import { ContactsJourneyOptions } from 'modules/analytics/models/ContactsJourneyOptions';
import { SankeyService } from 'modules/analytics/services/SankeyService';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { COLORS_CHART, COLOR_PRIMARY, COLOR_SURFACE_4 } from '../../Colors';

const { GoogleCharts } = require('assets/js/google-charts'); //tslint:disable-line
const requiredPackages = ['sankey'];
const NODE_CLICKED_TRACK = 'analytics-journey-node-clicked';
const HEIGHT_WITHOUT_DATA = 380;
const EXIT_EDGE_COLOR = COLORS_CHART.colors.extended_orange;
const OTHERS_EDGE_COLOR = COLOR_SURFACE_4;
const REGULAR_EDGE_COLOR = COLOR_PRIMARY;

class SankeyDiagram implements IComponentController {
    containerId: string;
    loading: boolean;
    displayedEdges: Array<ContactsJourneyEdge>;
    changeEvent: string;
    options: ContactsJourneyOptions;
    contactsButtonAvailable: boolean;
    selectedEdge: ContactsJourneyEdge;
    selectedNodeUsersCount: number;
    currentWidth: number;
    constructor(
        private $rootScope: IRootScopeService,
        private SegmentService: SegmentService,
        private SankeyService: SankeyService,
    ) {
        this.containerId = this.containerId || 'sankey-chart';
    }

    $onInit() {
        this.initOrUpdateSankeyChart();
        this.subscribeEvents();

        window.addEventListener('resize', this.onResizeWindow);
    }

    $onDestroy() {
        window.removeEventListener('resize', this.onResizeWindow);
    }

    /**
     * Calls GoogleCharts load method with required packages
     */
    private initOrUpdateSankeyChart() {
        GoogleCharts.load(this.drawElement.bind(this), requiredPackages);
    }

    /**
    * Subscribe all $watch or $on events for chart data change
    */
   private subscribeEvents() {
        this.$rootScope.$on(this.changeEvent, ($e, containerId) => {
            this.onChartChange({ containerId });
        });
    }

    /**
     * Chart change event method
     * @param {Object} - Object with properties that has changes
     */
    private onChartChange({ containerId }: { containerId: string }) {
        if (this.containerId == containerId) {
            if (GoogleCharts.api) {
                this.drawElement();
            }
        }
    }

    /**
     * On resize window function
     * IMPORTANT: needs to be an arrow function because of 'this' scope
     */
     private onResizeWindow = () => {
        if (this.currentWidth != window.innerWidth) {
            this.drawElement();
            this.contactsButtonAvailable = false;
        }
    }

    private drawElement() {
        const sankeyEdges = this.orderSankeyEdges(this.SankeyService.getEdgesDisplayValue(this.displayedEdges));
        const diagramParentWidth = document.getElementById('diagram-body') ? document.getElementById('diagram-body').clientWidth : 0;
        const sankeyOptions: ContactsJourneyOptions = this.options;
        sankeyOptions.width = diagramParentWidth;
        sankeyOptions.sankey.node.colors = this.loadNodeColors(sankeyEdges);

        const ElementInstance = GoogleCharts.api.visualization.Sankey;
        const elementOptions = {
            ...sankeyOptions,
            height: sankeyEdges.length === 0 ? HEIGHT_WITHOUT_DATA : sankeyOptions.height
        };

        const element = new ElementInstance(
            document.getElementById(this.containerId),
        );

        try {
            element.draw(this.getDataTable(sankeyEdges), elementOptions);
            GoogleCharts.api.visualization.events.addListener(element, 'ready', this.onDiagramReady(element, sankeyEdges));
        } catch (e) {
            throw new Error('Error while drawing sankey diagram: ' + e);
        }

        this.currentWidth = window.innerWidth;
    }

    private orderSankeyEdges(sankeyEdges: ContactsJourneyEdge[]): ContactsJourneyEdge[] {

        const sankeyEdgesWithOrder =  sankeyEdges.map(edge => {
            return {
                ...edge,
                order: this.getEdgeOrder(edge.type)
            };
        });

        const groupedEdgesPerStep = sankeyEdgesWithOrder.reduce((groups, item) => {
            const step = groups[item.step] || [];
            step.push(item);
            groups[item.step] = step;
            step.sort((firstBlock: ContactsJourneyEdge, secondBlock: ContactsJourneyEdge) => firstBlock.order - secondBlock.order);
            return groups;
        }, {});

        const edgesPerStep = Object.getOwnPropertyNames(groupedEdgesPerStep).map(prop => groupedEdgesPerStep[prop]);
        return edgesPerStep.concat.apply([], edgesPerStep);
    }

    private getEdgeOrder(type: string) {
        const edgeOrder = {
            [EdgeTypes.End] : EdgeOrder.Bottom,
            [EdgeTypes.Other]: EdgeOrder.Middle,
            [EdgeTypes.Regular]: EdgeOrder.Top,
        };

        return edgeOrder[type];
    }

    private loadNodeColors(sankeyEdges: ContactsJourneyEdge[]): string[] {
        const edges = [];

        sankeyEdges.forEach(({ to, from, type }) => {
            edges.push({ name: from });
            edges.push({ name: to, type });
        });

        const uniqueEdges = this.removeDuplicatedEdges(edges);

        return uniqueEdges.map(({ type }) => {
            if (type == EdgeTypes.Other) {
                return OTHERS_EDGE_COLOR;
            } else if (type == EdgeTypes.End) {
                return EXIT_EDGE_COLOR;
            } else {
                return REGULAR_EDGE_COLOR;
            }
        });
    }

    private removeDuplicatedEdges(edges: any[]): any[] {
        return edges.filter((edge, index) => edges.findIndex(e => e.name === edge.name) === index);
    }

    private getDataTable(edges: Array<ContactsJourneyEdge>) {
        const diagramDataArray = edges.map((currentEdge) => {
            return [
                currentEdge.from,
                currentEdge.to,
                currentEdge.count,
                currentEdge.type,
                this.SankeyService.createCustomTooltipContent(currentEdge, edges)
            ];
        });

        const dataTable = new GoogleCharts.api.visualization.DataTable();
        dataTable.addColumn('string', 'From');
        dataTable.addColumn('string', 'To');
        dataTable.addColumn({ type: 'number', role: 'Count' });
        dataTable.addColumn({ type: 'string', role: 'style' });
        dataTable.addColumn({'type': 'string', 'role': 'tooltip', 'p': {'html': true}});
        dataTable.addRows(diagramDataArray);

        return dataTable;
    }

    private listenToEdgesClick(element: any, sankeyEdges: Array<ContactsJourneyEdge>) {
        GoogleCharts.api.visualization.events.addListener(element, 'select', () => {
            if (element.getSelection().length > 0) {
                this.handleContactsButtonAvailability(true);
                this.handleSelectedEdge(element, sankeyEdges);
            } else {
                this.drawElement();
                this.handleContactsButtonAvailability(false);
                this.selectedEdge = undefined;
            }
        });
    }

    private handleSelectedEdge(element: any, sankeyEdges: Array<ContactsJourneyEdge>) {
        const selectedNode = element.getSelection()[0];
        if (selectedNode) {
            const edgeName = selectedNode.name;

            this.selectedEdge = this.getJourneyEdgeByName(edgeName, sankeyEdges);
            this.trackEdgeClick(this.selectedEdge, edgeName);

        }
    }

    private getJourneyEdgeByName(edgeName: string, sankeyEdges: Array<ContactsJourneyEdge>): ContactsJourneyEdge {
        let selectedEdge: ContactsJourneyEdge;

        const edgesFromSelectedName: ContactsJourneyEdge[] = sankeyEdges.filter((displayedEdge: ContactsJourneyEdge) => {
            return displayedEdge.from === edgeName;
        });
        const edgesToSelectedName = sankeyEdges.filter((displayedEdge: ContactsJourneyEdge) => {
            return displayedEdge.to === edgeName;
        });

        if (edgesFromSelectedName.length > 0) {
            selectedEdge = edgesFromSelectedName[0];
            this.selectedNodeUsersCount = edgesFromSelectedName.reduce((sum, current) => sum + current.count, 0);
        } else {
            selectedEdge = edgesToSelectedName.reduce((max, edge) => max.step > edge.step ? max : edge);
            this.selectedNodeUsersCount = edgesToSelectedName.reduce((sum, current) => sum + current.count, 0);
            selectedEdge.step++;
        }

        selectedEdge.name = edgeName;
        selectedEdge.type =  edgesToSelectedName && edgesToSelectedName.length > 0
            ? edgesToSelectedName[0].type
            : edgesFromSelectedName[0].type;

        return selectedEdge;
    }

    private trackEdgeClick(selectedEdge: ContactsJourneyEdge, edgeName: string) {
        this.SegmentService.createApplicationTrack({
            trackEvent: NODE_CLICKED_TRACK,
            payload: {
                visualizedStep: selectedEdge.step,
                edgeName: edgeName,
                edgeType: selectedEdge.type
            }
        });
    }

    private onDiagramReady = (element: any, sankeyEdges: Array<ContactsJourneyEdge>) => {
        this.listenToEdgesClick(element, sankeyEdges);
    }

    private handleContactsButtonAvailability(isButtonAvailable: boolean) {
        this.contactsButtonAvailable = isButtonAvailable;
    }

}

const SankeyDiagramComponent = {
    controller: SankeyDiagram,
    controllerAs: '$ctrl',
    bindings: {
        displayedEdges: '<',
        changeEvent: '=',
        options: '<',
        contactsButtonAvailable: '=',
        selectedEdge: '=',
        selectedNodeUsersCount: '='
    },
};

export default SankeyDiagramComponent;
