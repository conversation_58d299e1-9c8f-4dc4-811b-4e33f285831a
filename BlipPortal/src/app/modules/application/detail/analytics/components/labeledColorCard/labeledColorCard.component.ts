import { IComponentController } from 'angular';
import template from './LabeledColorCardView.html';
import * as styles from './labeledColorCard.scss';

class LabeledColorCardController implements IComponentController {
    styles: any;

    constructor(private $element, private $timeout) {
        this.styles = styles;
    }
}

const LabeledColorCardComponent = {
    template,
    controller: LabeledColorCardController,
    controllerAs: '$ctrl',
    bindings: {
        title: '<',
        text: '<',
        color: '<'
    },
};

export default LabeledColorCardComponent;
