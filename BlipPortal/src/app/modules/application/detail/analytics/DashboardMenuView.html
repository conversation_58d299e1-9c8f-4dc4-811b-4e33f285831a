<sidenav>
    <sidenav-menu>
        <sidenav-menu-item subtitle="{{'modules.application.detail.dashboard.general.subtitle' | translate}}" title="{{'modules.application.detail.dashboard.general.title' | translate}}" sref="auth.application.detail.dashboard.general" ng-click="$ctrl.trackOpenTab('analytics-dashboard-opened')"></sidenav-menu-item>
        <sidenav-menu-item subtitle="{{'reports.subtitle' | translate}}" title="{{'reports.title' | translate}}" sref="auth.application.detail.dashboard.reports" ng-click="$ctrl.trackOpenTab('analytics-custom-reports-opened')"></sidenav-menu-item>
        <sidenav-menu-item is-beta="!$ctrl.isHidingContactsJourneyBetaTag" ng-if="$ctrl.isShowingContactsJourney" subtitle="{{'contactsJourney.subtitle' | translate}}" title="{{'contactsJourney.title' | translate}}" sref="auth.application.detail.dashboard.contactsJourney" ng-click="$ctrl.trackOpenTab('analytics-journey-opened')"></sidenav-menu-item>
    </sidenav-menu>
</sidenav>
