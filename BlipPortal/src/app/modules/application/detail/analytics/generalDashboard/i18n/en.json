{"metrics": {"usersTitle": "Users", "messagesTitle": "Messages", "activeClients": "Active", "engagedClients": "Engaged", "totalMessagesCount": "Total", "messagesReceived": "Received", "messagesSent": "<PERSON><PERSON>", "activeMessages": "Active", "uniqueUsersTooltip": "Users that sent at least one message to the bot", "activeUsersTooltip": "Unique users that sent or received at least one message from the bot. <br> Data available since 12-06-2018.", "engagedUsersTooltip": "Unique users that sent at least one message to the bot", "messagesTraffickedTooltip": "Total of messages sent or received", "messagesReceivedTooltip": "Total of messages the bot received from the active users", "messagesSentTooltip": "Total of messages the bot sent to the users", "activeMessagesTooltip": "Messages sent by the bot outside of the 24h window", "activeMessagesPerDomain": {"title": "Proactive messages per channel", "channel": "Channel", "empty": "There are no proactive messages in the chosen period", "totalMessages": "Total proactive messages"}, "overviewHelp": {"title": "What is Overview?", "info1": "The dashboard displays all your chatbot's main metrics, allowing you to make quicker and better business decisions. The report calculates contacts and exchanged messages, including interactions with human service or proactive messages broadcast.", "info2": "Accounting is done every day, and data can still change for up to two days. <br />For example, metrics displayed on December 10 may still change, as exchanged messages on December 9 and 10 are still being computed.", "close": "Ok, got it", "learnMore": "Learn more about metrics", "linkUrl": "https://help.blip.ai/hc/en-us/articles/*************-Vis%C3%A3o-G<PERSON>-das-M%C3%A9tricas-de-An%C3%A1lise-TakeBlip"}, "usersAndMessages": "Users and messages", "usersAndMessagesDescription": "For more details about those metrics check the ", "helpCenter": "helpcenter.", "knowMoreAboutMetrics": "Learn more about metrics.", "usersDescription": "All unique user who received or sent a message to the chatbot.", "messagesDescription": "They are counted when the chatbot send or receive messages from contacts.", "tooltip": {"activeUsers": "Contacts who sent or received at least one chatbot message in the selected period.", "engagedUsers": "Contacts who sent at least one message to the chatbot in the selected period.", "totalMessages": "Total of messages sent and received by the chatbot in the selected period.", "receivedMessages": "Messages received by the chatbot in the selected period.", "sentMessages": "Messages sent by the chatbot in the selected period.", "activeMessages": "Messages sent by chatbot 24 hours after receiving the last message from the contact. They are subject to usage and pricing policies, specific to each channel."}}, "analysisCsv": {"dateHeader": "Date (dd/mm/yyyy)", "activeUsersPerDay": "Active users per day (DAUs)", "engagedUsersPerDay": "Engaged users per day (DEUs)", "receivedMessagesPerDay": "Received messages per day", "sentMessagesPerDay": "Sent messages per day"}, "datepicker": {"cancelText": "Cancel", "applyText": "Apply", "startDatePlaceholder": "Start date", "endDatePlaceholder": "End date"}, "action": {"downloadCsv": "Export", "reloadData": "Update"}}