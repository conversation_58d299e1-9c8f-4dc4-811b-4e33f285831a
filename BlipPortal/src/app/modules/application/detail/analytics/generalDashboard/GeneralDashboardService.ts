import MessagingHubService from 'modules/messaginghub/MessagingHubService';

export class GeneralDashboardService {
    constructor(private MessagingHubService: MessagingHubService) {
        'ngInject';
    }

    async getGenders(): Promise<any> {
        return this.MessagingHubService.sendCommand({
            method: 'get',
            uri: '/contacts?$groupby=gender',
        });
    }

    async getAverageResponseTime(): Promise<any> {
        return this.MessagingHubService.sendCommand({
            method: 'get',
            uri: '/message-statistic/responseTime',
        });
    }

    async getWordCount(startDate, endDate): Promise<any> {
        return this.MessagingHubService.sendCommand({
            method: 'get',
            uri: `/message-statistic/word-count?startDate=${encodeURIComponent(
                startDate,
            )}&endDate=${encodeURIComponent(endDate)}&$skip=0&$take=100`,
        });
    }
}
