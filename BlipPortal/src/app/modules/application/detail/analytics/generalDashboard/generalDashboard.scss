@import "~assets/scss/main";
@import '~blip-ds/dist/collection/styles/colors';

#general-dashboard-download-csv {
    line-height: 40px;
    margin-right: 5px;
}

#general-dashboard-reload {
    line-height: 40px;
    margin-left: 5px;
}

.dashboard-datepicker {
    margin-left: 10px;
    input[type="text"] {
        height: 2.5*$m;
    }
}

.hover-div {
    .tooltip-icon-hover {
        opacity: 0;
        transition: opacity 0.2s linear;
        -webkit-transition: opacity 0.2s linear;
    }

    &:hover {
        .tooltip-icon-hover {
            opacity: 1;
            transition: opacity 0.2s linear;
            -webkit-transition: opacity 0.2s linear;
        }
    }
}

.font-size-24px {
    p {
        font-size: 24px !important;
    }
    .tooltips  {
        .icon-info {
            font-size: 24px;
        }
        
    }
}

#general-dashboard {

    margin-bottom: 10px;

    .card-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    card .card-header {
        padding-bottom: 0 !important;
    }

    .analytics-spakline-graph {
        margin-right: 0;
        @include media-queries('desktop-wide'){
            margin-right: 3rem;
        }
        @include media-queries('desktop-wide-x'){
            margin-right: 6rem;
        }
    }
   
    // with new mau concept
    #counters {
        .position-title {
            padding-left: 20px;
            padding-top: 5px;
        }

        #list-users {
            margin-left: 5%;

            .pipe {
                content: " ";
                width: 1px;
                background-color: $color-surface-1;
                margin-bottom: 0;
                margin-right: -30%;
                margin-top: -20px;
            }
        }

        .list-counters {
            tooltip tip.wide{
                min-width: 30*$m;
            }
        }
    }

    // without new mau concept
    #list-messages {
        .pipe {
            content: " ";
            width: 1px;
            background-color: $color-content-disable;
            margin-bottom: 0;
        }

        .hover-div {
            .tooltip-icon-hover {
                opacity: 0;
                transition: opacity 0.2s linear;
                -webkit-transition: opacity 0.2s linear;
            }

            &:hover {
                .tooltip-icon-hover {
                    opacity: 1;
                    transition: opacity 0.2s linear;
                    -webkit-transition: opacity 0.2s linear;
                }
            }
        }

        tooltip tip.wide{
            min-width: 40*$m;
        }
    }

    #dashboard-filters {
        display: flex;
        flex-wrap: wrap;

        .dash-head-tip {
            text-decoration-line: underline;
            color: $color-primary;
        }
    }

    .flex1 {
        flex: 1.5;
    }

    .flex3 {
        flex: 3;
    }

    .dash-primary-dark-text {
        color: $color-primary;
        flex: 1;
    }
}

.active-message-chart-list thead tr th {
    color: $color-content-default;
}

.active-message-chart-list tbody {
    tr {
        background-color: $color-content-default  !important;
    }

    tr:nth-child(odd) {
        background-color: $color-content-default  !important;
    }

    td:first-of-type {
        color: $color-content-default;
    }
}

.chart-no-data {
    text-align: center;
    h4 {
        margin-top: 7%;
    }
}

.general-dashboard-help {
    padding: 3.2*$m !important;

    img:last-child {
        margin-left: auto;
    }

    .general-dashboard-help-title {
        font-size: $bp-fs-4;
        min-width: 18*$m;
        margin-right: 1.6*$m;
        color: $bp-color-city;
    }

    .general-dashboard-help-data {
        margin-right: 1.6*$m;

        p, a {
            font-size: $bp-fs-6 !important;
        }

        a {
            margin-right: 2.4*$m;
            i {
                margin-left: 0.5*$m;
            }
        }

        .general-dashboard-help-text {
            margin-bottom: 1.6*$m;

            p {
                color: $bp-color-rooftop;

                a {
                    margin: 0;
                }
            }
        }
    }
}

.picker-view {
    flex: 1;
    align-items: center;
    justify-content: flex-end;
    display: flex;
}

.dash-title-view {
    flex: 1;
}

.google-visualization-table-table {
    .th {
        color: $color-content-disable;
        text-align: left;
        padding-bottom: 5px;
    }
    .tl {
        background:$color-neutral-light-snow;
        color: $color-content-default;
            text-align: left;
        &:first-child {
            font-weight: 700;
        }
    }
}
.general-card-description {
    color: $color-content-default !important;
    font-weight: 400;
    line-height: 24px;
}

.general-card-title {
    color: $color-content-default !important;
}