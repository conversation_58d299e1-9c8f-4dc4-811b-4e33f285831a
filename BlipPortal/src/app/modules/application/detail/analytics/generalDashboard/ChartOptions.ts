import * as moment from 'moment';
import { getUsFormatDate } from 'data/date';

export class ChartOptions {
    static getLineChartOptions({
        tooltip = undefined,
        max = undefined,
        min = 0,
        beginAtZero = true,
        period = undefined,
    } = {}) {
        const nearest10s = 10 ** Math.max(1, Math.floor(Math.log10(max || 0)));
        const tooltipOptions = tooltip
            ? {
                  tooltips: {
                      callbacks: {
                          label: ({ index }, { datasets }) => {
                              return tooltip(
                                  datasets.map(
                                      (dataset) => dataset.data[index],
                                  ),
                              );
                          },
                      },
                  },
              }
            : {};

        return {
            ...tooltipOptions,
            scales: {
                xAxes: [
                    {
                        type: 'time',
                        display: true,
                        position: 'bottom',
                        time: {
                            min: getUsFormatDate(period.start),
                            max: getUsFormatDate(period.end),
                            tooltipFormat: 'DD/MM/YYYY',
                            unit: this.getUnitByDay(period),
                            displayFormats: {
                                millisecond: 'MMM DD',
                                second: 'MMM DD',
                                minute: 'MMM DD',
                                hour: 'MMM DD',
                                day: 'MMM DD',
                                week: 'MMM DD',
                                month: 'MMM DD',
                                quarter: 'MMM DD YY',
                                year: 'YYYY',
                            },
                        },
                    },
                ],
                yAxes: [
                    {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            max: max
                                ? Math.ceil(max / nearest10s) * nearest10s
                                : undefined,
                            min,
                            beginAtZero,
                        },
                    },
                ],
            },
        };
    }

    static getUnitByDay(period) {
        const s = moment(getUsFormatDate(period.start));
        const e = moment(getUsFormatDate(period.end));

        const diff = e.diff(s, 'days');

        if (diff <= 31) {
            return 'day';
        } else if (diff <= 180) {
            return 'week';
        } else if (diff <= 365) {
            return 'month';
        } else if (diff <= 730) {
            return 'quarter';
        } else {
            return 'year';
        }
    }
}
