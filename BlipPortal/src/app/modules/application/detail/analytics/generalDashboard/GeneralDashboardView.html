<page-header>
    <custom-title>
        <div class="flex flex-items-center">
            <bds-typo  variant="fs-32" bold="extra-bold" translate>
                modules.application.detail.dashboard.general.title
            </bds-typo>
            &nbsp;
            <bds-typo variant="fs-32" ng-click="$ctrl.showOverviewModal()" ng-if="$ctrl.isAnalyticsMessagesGeneralInfoEnabled">
                <bds-icon  type="icon" name="info" theme="solid" size="medium" qa-test="resources-show-help-info-icon">
                </bds-icon>
            </bds-typo>
        </div>
    </custom-title>
    <custom-content ng-if="$ctrl.usersPerDay && $ctrl.usersPerDay.length > 0">
        <bds-button variant="secondary" icon="refresh" size="standard" class="hydrated mh3"  ng-click="$ctrl.updateDashboard()" translate>
            action.reloadData
        </bds-button>
        <bds-button id="general-dashboard-reload" icon="download" size="standard" class="hydrated mh3" ng-click="$ctrl.downloadCSV()" translate>
            action.downloadCsv
        </bds-button>
    </custom-content>
</page-header>

<div class="container">
</div>

<div id="general-dashboard" class="container">
    <div id="dashboard-filters" class="bp-c-rooftop flex justify-between z-5 relative">
        <div class="small-datepicker-input picker-view mr2">
            <blip-daterange-picker 
                period="$ctrl.daterangePickerPeriod"
                cancel-text="{{'datepicker.cancelText' | translate}}"
                apply-text="{{'datepicker.applyText' | translate}}"
                start-date-placeholder="{{'datepicker.startDatePlaceholder' | translate}}"
                end-date-placeholder="{{'datepicker.endDatePlaceholder' | translate}}"
                on-date-selection="$ctrl.applyDateRangeFilter()">
            </blip-daterange-picker>
        </div>
    </div>
    <div class="flex flex-wrap z-2">
        <div class="card z-2 ma0 pa4 flex1 mt4 mr2 ml2 card-content">
            <div class="flex flex-column">
                <bds-typo class="general-card-title"  variant="fs-24" bold="bold" translate>
                    metrics.usersTitle
                </bds-typo>
                <bds-typo class="general-card-description"  variant="fs-16" bold="regular" translate>
                    metrics.usersDescription
                </bds-typo>
            </div>
            <div class="flex">
                <counter-child-card
                    name="'metrics.activeClients' | translate"
                    value="$ctrl.formatNumbers($ctrl.activeClients)"
                    loaded="$ctrl.activeClients !== undefined"
                    tooltip="'metrics.tooltip.activeUsers' | translate">
                </counter-child-card>

                <counter-child-card
                    name="'metrics.engagedClients' | translate"
                    value="$ctrl.formatNumbers($ctrl.engagedClients)"
                    loaded="$ctrl.engagedClients !== undefined"
                    tooltip="'metrics.tooltip.engagedUsers' | translate">
                </counter-child-card>
            </div>
        </div>
        <div class="card ma0 pa4 flex3 mt4 mr2 ml2 card-content">
            <div class="flex flex-column">
                <bds-typo class="general-card-title"  variant="fs-24" bold="bold" translate>
                    metrics.messagesTitle
                </bds-typo>
                <bds-typo class="general-card-description"  variant="fs-16" bold="regular" translate>
                    metrics.messagesDescription
                </bds-typo>
            </div>
            <div class="flex">
                <counter-child-card
                    name="'metrics.totalMessagesCount' | translate"
                    value="$ctrl.formatNumbers($ctrl.messagesTrafficked)"
                    loaded="$ctrl.isTrafickedMessagesLoaded"
                    side="right"
                    tooltip="'metrics.tooltip.totalMessages' | translate">
                </counter-child-card>

                <counter-child-card
                    name="'metrics.messagesReceived' | translate"
                    value="$ctrl.formatNumbers($ctrl.messagesReceived)"
                    loaded="$ctrl.isReceivedMessagesLoaded"
                    side="bottom"
                    tooltip="'metrics.tooltip.receivedMessages' | translate">
                </counter-child-card>

                <counter-child-card
                    name="'metrics.messagesSent' | translate"
                    value="$ctrl.formatNumbers($ctrl.messagesSent)"
                    loaded="$ctrl.isSentMessagesLoaded"
                    side="bottom"
                    tooltip="'metrics.tooltip.sentMessages' | translate">
                </counter-child-card>

                <counter-child-card
                    name="'metrics.activeMessages' | translate"
                    value="$ctrl.formatNumbers($ctrl.activeMessages)"
                    loaded="$ctrl.isActiveMessagesLoaded"
                    side="bottom"
                    tooltip="'metrics.tooltip.activeMessages' | translate">
                </counter-child-card>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="mr2 ml2" ng-if="$ctrl.isActiveMessagePerDomainTableEnabled">
        <card ng-if="$ctrl.isActiveMessagePerDomainChartEmpty">
            <bds-typo  class="general-card-title"  variant="fs-24" bold="bold" translate>
                metrics.activeMessagesPerDomain.title
            </bds-typo>
            <bds-typo 
                class="no-content-found" variant="fs-20"
                translate>
                metrics.activeMessagesPerDomain.empty
            </bds-typo>
        </card>

        <div class="row" ng-if="!$ctrl.isActiveMessagePerDomainChartEmpty">
            <analytics-chart
                class="active-message-chart-list"
                chart="$ctrl.activeMessagesPerDomainChart"
                is-loading="$ctrl.activeMessagesPerDomainChart.isLoading">
            </analytics-chart>
        </div>
    </div>

    <!-- Users per day -->
    <div class="row mr2 ml2">
        <analytics-chart class="font-size-24px" chart="$ctrl.usersPerDayChart"
            info="{{'modules.application.detail.dashboard.general.usersPerDayTooltip' | translate}}"
            is-loading="$ctrl.usersPerDayChart.isLoading"
            ></analytics-chart>
    </div>
    
    <div class="row mr2 ml2">
        <analytics-chart class="font-size-24px" chart="$ctrl.dailyMessagesChart"
            info="{{'metrics.messagesReceivedTooltip' | translate}}"
            is-loading="$ctrl.dailyMessagesChart.isLoading"
            ></analytics-chart>
    </div>
</div>
