import * as moment from 'moment';
import './generalDashboard.scss';
import { getUsFormatDate, formatDateFromString, toUTCISOString } from 'data/date';
import {
    IWindowService,
    IController,
} from 'angular';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { Channels } from 'types/Channels';
import { subtract, groupBy } from 'data/array';
import {
    AnalyticsPayload,
    ServiceType,
    MessagesCategoryType,
    UsersCategoryType,
    ViewChart,
} from 'modules/analytics/models';
import { ChartType } from 'modules/analytics/models/Chart';
import { AnalyticsServiceFactory } from 'modules/analytics/services/AnalyticsServiceFactory';
import { GenerateMatrixFromApiData } from 'modules/analytics/Transformers';
import {
    maybePeriodFromCookies,
    setupPeriodToCookies,
    sortByStorageDate,
    getTranslatedActions,
} from 'modules/analytics/Utils';
import CSVParser from 'assets/js/export-csv';
import { MetricsFeatures } from './MetricsFeatures';
import { DaterangerPickerPeriod } from 'interfaces/DaterangerPicker';
import { GeneralInfoModalController } from './generalInfoModal/GeneralInfoModalController';
import GeneralInfoModalView from './generalInfoModal/GeneralInfoModalView.html';
import { MinimunDatePeriod } from '../utils/AnalyticsFunctions';

export type Messages = {
    sentOrReceived: number;
    sent: number;
    received: number;
    statusDispatched: number;
    statusReceived: number;
    statusConsumed: number;
    statusError: number;
};

export type MessagesPerChannel = {
    labels: string[];
    data: any[];
};

export class GeneralDashboardController implements IController {
    uniqueClients: number;
    activeClients: number;
    engagedClients: number;
    dailyMessagesChart: any;
    usersPerDayChart: ViewChart;
    activeMessagesPerDomainChart: ViewChart;
    cookiesPeriodKey: string;
    usersPerDayChartType: string;
    filterItems: any[] = [];
    filters: { [name: string]: {}[] } = {};
    csvHeader: any[];
    csvArray: any[];
    period: any;
    maxDate: Date;
    daterangePickerPeriod: DaterangerPickerPeriod;

    usersPerDayData: any;

    isLoadingUsers: boolean;
    usersChartType: string;
    usersChartOptions: any;
    newUsers: number;
    newUsersChartData: any;
    recurrentUsers: number;
    recurrentUsersChartData: any;

    isLoadingMessages: boolean;
    messagesTrafficked: number;
    messagesReceived: number;
    messagesSent: number;
    activeMessages: number;

    usersPerDay: any;
    messagesPerDay: any;

    isAnalyticsMessagesGeneralInfoEnabled: boolean = false;
    isActiveMessagePerDomainTableEnabled: boolean = false;

    isActiveMessagePerDomainChartEmpty: boolean = false;

    defaultDateFormat: string = 'DD/MM/YYYY';

    isTrafickedMessagesLoaded: boolean = false;
    isSentMessagesLoaded: boolean = false;
    isReceivedMessagesLoaded: boolean = false;
    isActiveMessagesLoaded: boolean = false;
    isFeatureTogglesLoaded: boolean = false;
    doNotTrackNextDateChange: boolean = true;

    constructor(
        private ModalService,
        private $rootScope: any,
        private $window: IWindowService,
        private SegmentService: SegmentService,
        private $translate,
        private $cookies,
        private AnalyticsServiceFactory: AnalyticsServiceFactory,
        private FileSaver,
    ) {
        'ngInject';
        this.usersPerDayData = [];
        this.cookiesPeriodKey = `${
           this.$rootScope.application.shortName
            }_dashboardPeriodSetting`;

        this.usersChartType = ChartType.Line;
        this.usersChartOptions = {
            width: 180,
            height: 70,
            enableInteractivity: 'false',
            lineWidth: 1.5,
            curveType: 'function',
            hAxis: {
                //eixo y
                baseline: 'none',
                gridlines: {
                    count: 0,
                },
            },
            vAxis: {
                // eixo x
                baseline: 'none',
                gridlines: {
                    count: 0,
                },
            },
            pointSize: '0',
            legend: {
                position: 'none',
            },
        };
        this.isLoadingUsers = false;
        this.usersPerDay = [];
        this.messagesPerDay = [];

        this.setDefaultValues();
        this.initLineCharts();
        this.getDate();
        this.updateSelectedPeriod();
        this.initFilters();
    }

    private setDefaultValues() {
        this.messagesTrafficked = 0;
        this.messagesReceived = 0;
        this.messagesSent = 0;
        this.activeMessages = 0;
        this.uniqueClients = 0;
        this.activeClients = 0;
        this.engagedClients = 0;
    }

    async checkFeatures(): Promise<void> {
        try {
            this.isAnalyticsMessagesGeneralInfoEnabled = await MetricsFeatures.analyticsMessagesGeneralInfoEnabled();
            this.isActiveMessagePerDomainTableEnabled = await MetricsFeatures.activeMessagePerDomainTableEnabled();
        } catch (error) {
            console.error(`Error while loading feature toggles: ${error}`);
        } finally {
            this.isFeatureTogglesLoaded = true;
        }
    }

    formatNumbers(value) {
        return value?.toLocaleString()?.replaceAll(',', '.');
    }

    async $onInit() {
        await this.checkFeatures();
        await this.loadData();
    }

    async showOverviewModal() {
        this.SegmentService.createBotTrack(
            'analytics-dashboard-modal-opened',
            this.$rootScope.application
        );

        const modal = await this.ModalService.showModal({
            template: GeneralInfoModalView,
            controller: GeneralInfoModalController,
            controllerAs: '$ctrl',
        });

        await modal.close;
    }

    private initLineCharts() {
        const dailyMessagesChartName = this.$translate.instant(
            'modules.application.detail.dashboard.general.messagesPerDay',
        );
        const activeMessagesPerDomainName = this.$translate.instant(
            'metrics.activeMessagesPerDomain.title',
        );
        const dailyUsersName = this.$translate.instant(
            'modules.application.detail.dashboard.general.clientsPerDay'
        );

        this.usersPerDayChart = new ViewChart({
            chartType: ChartType.Line,
            isLoading: true,
            name: dailyUsersName,
        });
        this.dailyMessagesChart = new ViewChart({
            chartType: ChartType.Line,
            category: MessagesCategoryType.TotalMessages,
            isLoading: true,
            name: dailyMessagesChartName,
        });

        this.activeMessagesPerDomainChart = new ViewChart({
            chartType: ChartType.List,
            category: MessagesCategoryType.ActiveMessages,
            isLoading: true,
            name: activeMessagesPerDomainName,
        });
    }

    get usEndDate() {
        return getUsFormatDate(this.period.end);
    }

    get usStartDate() {
        return getUsFormatDate(this.period.start);
    }

    get applicationStatus() {
        return this.$rootScope.application.status;
    }

    private async initFilters() {
        const channels = await this.$translate('modules.ui.filterBy.channels');
        this.filters = {
            [channels]: Channels,
        };
    }

    addFilterItem(item, key) {
        this.filterItems = this.filterItems.concat({ ...item, key }); //Add new item to filter list

        this.filters[key] = Channels.filter(subtract(this.filterItems));

        this.$rootScope.$broadcast('ToggleDropdownItem');
        this.$rootScope.$broadcast('angucomplete-alt:clearInput');
    }

    removeFilterItem(newFilters, removedItem) {
        this.filterItems = newFilters;
        this.filters[removedItem.key] = Channels.filter(
            subtract(this.filterItems),
        );
    }

    print() {
        this.$window.print();
    }

    async updateDashboard() {
        this.SegmentService.createBotTrack(
            'analytics-dashboard-refreshed',
            this.$rootScope.application
        );

        try {
            await this.loadData();
        } catch (e) {
            console.error(`Error while updating dashboard: ${e}`);
        }
    }

    private getDate() {
        this.period = maybePeriodFromCookies({
            $cookies: this.$cookies,
            key: this.cookiesPeriodKey,
            initialInterval: 7,
        });
    }

    private updateSelectedPeriod() {
        const startDate = moment(this.period.start, this.defaultDateFormat).toDate();
        const endDate = moment(this.period.end, this.defaultDateFormat).toDate();

        this.daterangePickerPeriod = {
            selectedPeriod: {
                startDate,
                endDate,
            },
            validPeriod: {
                startDate: MinimunDatePeriod(),
                endDate: moment().add(1, 'days').toDate()
            }
        };
    }

    async applyDateRangeFilter() {
        try {
            if (this.doNotTrackNextDateChange) {
                this.doNotTrackNextDateChange = false;
            } else {
                await this.loadData('user');
            }
        } catch (e) {
            console.error(`Error while applying daterange filter: ${e}`);
        }
    }

    private async loadData(periodSource: string = 'default') {
        if (periodSource == 'user') {
            this.SegmentService.createBotTrack(
                'analytics-dashboard-chart-date-changed',
                this.$rootScope.application
            );
        }

        const start = moment(this.daterangePickerPeriod.selectedPeriod.startDate)
            .format(this.defaultDateFormat);
        const end = moment(this.daterangePickerPeriod.selectedPeriod.endDate)
            .format(this.defaultDateFormat);
        this.period = {
            start,
            end,
        };

        /**
         * Get current period and put to cookies object
         */
        setupPeriodToCookies({
            $cookies: this.$cookies,
            period: this.period,
            key: this.cookiesPeriodKey,
        });

        const payload: AnalyticsPayload = {
            shortName: this.$rootScope.application.shortName,
            startDate: toUTCISOString(this.daterangePickerPeriod.selectedPeriod.startDate),
            endDate: toUTCISOString(this.daterangePickerPeriod.selectedPeriod.endDate)
        };

        if (this.isFeatureTogglesLoaded) {
            this.loadClients(payload);
            this.loadActiveMessages(payload);
            this.loadClientsPerDay(payload);
            this.loadReceivedAndSentMessages(payload);
            this.loadActiveMessagesPerDomainTable(payload);
        }
    }

    /**
     * Generate CSV
     */
    private async generateCSV() {
        try {
            const dateHeader = await this.$translate(
                'analysisCsv.dateHeader'
            );
            const activeUsersPerDayHeader = await this.$translate(
                'analysisCsv.activeUsersPerDay',
            );
            const engagedUsersPerDayHeader = await this.$translate(
                'analysisCsv.engagedUsersPerDay',
            );
            const sentMessagesPerDayHeader = await this.$translate(
                'analysisCsv.sentMessagesPerDay',
            );
            const receivedMessagesPerDayHeader = await this.$translate(
                'analysisCsv.receivedMessagesPerDay',
            );

            this.csvArray = [];

            let day = moment.utc(formatDateFromString(this.period.start));

            this.usersPerDay.forEach((element, index) => {
                if (index != 0) { // first element is just title, skip
                    this.csvArray.push({
                        [`${dateHeader}`]: day.format(this.defaultDateFormat),
                        [`${activeUsersPerDayHeader}`]: element[1],
                        [`${engagedUsersPerDayHeader}`]: element[2],
                        [`${receivedMessagesPerDayHeader}`]: this.messagesPerDay[index][1],
                        [`${sentMessagesPerDayHeader}`]: this.messagesPerDay[index][2],
                    });
                    day = day.add(1, 'days');
                }
            });
        } catch (err) {
            console.error(err);
        }
    }

    /**
     * Download CSV
     */
    async downloadCSV() {

        await this.generateCSV();

        this.SegmentService.createBotTrack(
            'analytics-dashboard-downloaded',
            this.$rootScope.application,
            { Date: new Date().toISOString() }
        );

        try {
            const filename = 'blip-dashboard.csv';
            const { file } = CSVParser.CSVAsBlob({
                data: this.csvArray,
                hasHeader: true,
            });
            this.FileSaver.saveAs(file, filename);
        } catch (err) {
            console.error(err);
        }
    }

    /**
     * Daily messages traffic
     */
    async loadReceivedAndSentMessages(payload: AnalyticsPayload) {
        this.isReceivedMessagesLoaded = false;
        this.isSentMessagesLoaded = false;
        this.isTrafickedMessagesLoaded = false;
        this.dailyMessagesChart.isLoading = true;

        try {
            const AnalyticsService = this.AnalyticsServiceFactory.createService(
                ServiceType.Messages,
            );
            const events = await AnalyticsService.getActionsForCategory({
                chartType: this.dailyMessagesChart.chartType,
                category: this.dailyMessagesChart.category,
                endDate: payload.endDate,
                startDate: payload.startDate,
                application: this.$rootScope.application,
            });

            const groupedActions = groupBy(events, 'action');
            const chartEvents = Object.keys(groupedActions)
                .reduce(
                    (acs, a) =>
                        acs.concat([events.filter((e) => e.action == a)]),
                    [],
                )
                .map((e) => e.sort(sortByStorageDate));

            const data = [
                [
                    'Day',
                    ...getTranslatedActions(
                        this.$translate,
                        Object.keys(groupedActions),
                    ),
                ],
                ...GenerateMatrixFromApiData(chartEvents, this.period),
            ];

            this.messagesPerDay = data;

            this.dailyMessagesChart = new ViewChart({
                ...this.dailyMessagesChart,
                data,
                isLoading: false,
            });

            this.messagesReceived = groupedActions.receivedMessages ?
                groupedActions.receivedMessages.map(item => item.count).reduce((prev, next) => prev + next) :
                    0;

            this.messagesSent = groupedActions.sentMessages ?
                groupedActions.sentMessages.map(item => item.count).reduce((prev, next) => prev + next) :
                    0;

            this.messagesTrafficked = this.messagesReceived + this.messagesSent;

            this.isReceivedMessagesLoaded = true;
            this.isSentMessagesLoaded = true;
            this.isTrafickedMessagesLoaded = true;

        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Active users per day
     * @param {AnalyticsPayload} payload
     */
    private async loadClientsPerDay(payload: AnalyticsPayload) {
        this.usersPerDayChart.isLoading = true;

        try {
            const userAnalyticsService = this.AnalyticsServiceFactory.createService(
                ServiceType.Users,
            );

            this.usersPerDayChart = new ViewChart({
                ...this.usersPerDayChart,
                category: UsersCategoryType.TotalUsers,
            });
            const events = await userAnalyticsService.getActionsForCategory({
                ...payload,
                chartType: this.usersPerDayChart.chartType,
                category: this.usersPerDayChart.category,
                application: this.$rootScope.application,
            });

            const groupedActions = groupBy(events, 'action');
            const chartEvents = Object.keys(groupedActions)
                .reduce(
                    (acs, a) =>
                        acs.concat([events.filter((e) => e.action == a)]),
                    [],
                )
                .map((e) => e.sort(sortByStorageDate));

            const chartData = [
                [
                    'Day',
                    ...getTranslatedActions(
                        this.$translate,
                        Object.keys(groupedActions),
                    ),
                ],
                ...GenerateMatrixFromApiData(chartEvents, this.period),
            ];

            this.usersPerDayData = events;
            this.usersPerDay = chartData;

            // Chart Object
            this.usersPerDayChart = new ViewChart({
                ...this.usersPerDayChart,
                data: chartData,
                isLoading: false
            });
        } catch (e) {
            console.error('Error while getting users per day data: ', e);
        } finally {
            this.usersPerDayChart.isLoading = false;
        }
    }

    /**
     * Bind active users
     * @param payload
     */
    private async loadClients(payload) {
        try {
            const Service = this.AnalyticsServiceFactory.createService(ServiceType.Users);

            this.activeClients = undefined;
            this.engagedClients = undefined;

            this.loadActiveClientsCount(payload, Service);
            this.loadEngagedClientsCount(payload, Service);
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Total of active clients
     * @param payload
    * * @param Service
     */
    private async loadActiveClientsCount(payload, Service) {
        this.activeClients = await Service.getTotalActiveUsers(payload);
    }

    /**
     * Total of engaged clients
     * @param payload
    * * @param Service
     */
    private async loadEngagedClientsCount(payload, Service) {
        this.engagedClients = await Service.getTotalEngagedUsers(payload);
    }

    /**
     * Active messages
     * @param {AnalyticsPayload} payload
     */
    private async loadActiveMessages(payload: AnalyticsPayload) {
        this.isLoadingMessages = true;

        try {
            const Service = this.AnalyticsServiceFactory.createService(
                ServiceType.Messages,
            );
            const data = await Service.getActionsForCategory({
                ...payload,
                category: MessagesCategoryType.ActiveMessages,
                application: this.$rootScope.application,
            });
            this.activeMessages = data[0].count;
        } finally {
            this.isActiveMessagesLoaded = true;
            this.isLoadingMessages = false;
        }
    }

    /**
     * Active messages per domain
     * @param {AnalyticsPayload} payload
     */
    private async loadActiveMessagesPerDomainTable(payload: AnalyticsPayload) {
        this.activeMessagesPerDomainChart.isLoading = true;

        try {
            const messageAnalyticsService = this.AnalyticsServiceFactory.createService(
                ServiceType.Messages,
            );

            this.activeMessagesPerDomainChart = new ViewChart({
                ...this.activeMessagesPerDomainChart,
                category: MessagesCategoryType.ActiveMessagesPerDomain,
            });

            const activeMessagesPerDomain = await messageAnalyticsService.getActionsForCategory({
                ...payload,
                chartType: this.activeMessagesPerDomainChart.chartType,
                category: this.activeMessagesPerDomainChart.category,
                application: this.$rootScope.application,
            });

            const data = [
                [
                    this.$translate.instant('metrics.activeMessagesPerDomain.channel'),
                    this.$translate.instant('metrics.activeMessagesPerDomain.totalMessages')
                ],
                ...activeMessagesPerDomain[0].itemsPerDomain.map(t => {
                    const channel = Channels.find(channel => channel.value == t.domain);
                    return [channel ? channel.text : t.domain, t.count];
                })
            ];
            this.isActiveMessagePerDomainChartEmpty = data.length < 2;

            // Chart Object
            this.activeMessagesPerDomainChart = new ViewChart({
                ...this.activeMessagesPerDomainChart,
                data,
                isLoading: false
            });

        } catch (e) {
            console.error('Error while getting active messages per domain: ', e);
        } finally {
            this.activeMessagesPerDomainChart.isLoading = false;
        }
    }
}
