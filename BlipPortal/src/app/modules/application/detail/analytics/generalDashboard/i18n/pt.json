{"metrics": {"usersTitle": "Usuários", "messagesTitle": "Mensagens", "activeClients": "Ativos", "engagedClients": "Enga<PERSON><PERSON>", "totalMessagesCount": "Total", "messagesReceived": "Recebidas", "messagesSent": "Enviadas", "activeMessages": "Ativas", "uniqueUsersTooltip": "Usuários que enviaram alguma mensagem para o chatbot", "activeUsersTooltip": "Usuários únicos que enviaram ou receberam mensagens do chatbot. <br> Dados disponíveis desde 06/12/2018.", "engagedUsersTooltip": "Usuários únicos que enviaram alguma mensagem para o chatbot", "messagesTraffickedTooltip": "Total de mensagens enviadas e recebidas", "messagesReceivedTooltip": "Mensagens que o bot recebeu dos usuários (ativos)", "messagesSentTooltip": "Mensagens que o bot enviou aos usuários", "activeMessagesTooltip": "Mensagens enviadas pelo bot fora da janela de 24h", "activeMessagesPerDomain": {"title": "Mensagens ativas por canal", "channel": "Canal", "empty": "Não há mensagens ativas no período selecionado", "totalMessages": "Total de mensagens ativas"}, "overviewHelp": {"title": "O que é a Visão Geral?", "info1": "A visão geral permite a visualização das principais métricas do chatbot com objetivo de ajudar a tomar decisões rápidas de negócio. O relatório contabiliza todos os contatos e mensagens trafegadas, incluindo interações durante o atendimento humano ou envio de mensagens ativas.", "info2": "A contabilidade é feita diariamente e os dados podem sofrer alterações em um período de até dois dias. <br />Um painel exibido no dia 10 de dezembro, por exemplo, pode ainda sofrer alterações, pois as mensagens trafegadas nos dias 9 e 10 de dezembro ainda estão sendo computadas.", "close": "Ok, entendi", "learnMore": "<PERSON><PERSON> mais sobre as m<PERSON><PERSON><PERSON>", "linkUrl": "https://help.blip.ai/hc/pt-br/articles/1500008977602-Vis%C3%A3o-G<PERSON>-das-M%C3%A9tricas-de-An%C3%A1lise-TakeBlip"}, "usersAndMessages": "Usuários e Mensagens", "usersAndMessagesDescription": "Para mais detalhes sobre estas métricas acesse o ", "helpCenter": "helpcenter. ", "knowMoreAboutMetrics": "<PERSON><PERSON> mais sobre as m<PERSON><PERSON><PERSON>.", "usersDescription": "Todo usuário único que recebeu ou enviou mensagem para o chatbot.", "messagesDescription": "São contabilizadas quando o chatbot envia ou recebe mensagens dos contatos.", "tooltip": {"activeUsers": "Contatos que enviaram ou receberam pelo menos uma mensagem do chatbot no período selecionado.", "engagedUsers": "Contatos que enviaram pelo menos uma mensagem para o chatbot no período selecionado.", "totalMessages": "Quantidade total de mensagens enviadas e recebidas pelo chatbot no período selecionado.", "receivedMessages": "Mensagens recebidas pelo chatbot no período selecionado.", "sentMessages": "Mensagens enviadas pelo chatbot no período selecionado.", "activeMessages": "Mensagens enviadas pelo chatbot após 24 horas do recebimento da última mensagem do contato. Estão sujeitas a políticas de utilização e tarifação, específicas de cada canal."}}, "analysisCsv": {"dateHeader": "Data", "activeUsersPerDay": "Usuários ativos por dia (DAUs)", "engagedUsersPerDay": "Usuários engajados por dia (DEUs)", "receivedMessagesPerDay": "Mensagens recebidas por dia", "sentMessagesPerDay": "Mensagens enviadas por dia"}, "datepicker": {"cancelText": "<PERSON><PERSON><PERSON>", "applyText": "Aplicar", "startDatePlaceholder": "Data inicial", "endDatePlaceholder": "Data final"}, "action": {"downloadCsv": "Exportar", "reloadData": "<PERSON><PERSON><PERSON><PERSON>"}}