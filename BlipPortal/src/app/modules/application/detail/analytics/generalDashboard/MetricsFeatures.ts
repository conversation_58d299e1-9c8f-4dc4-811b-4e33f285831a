import { FeatureToggleClientService } from 'feature-toggle-client';

export const analyticsMessagesGeneralInfo = 'analytics-messages-general-info';
export const activeMessagePerDomainTable = 'active-messages-per-domain-table';

export class MetricsFeatures {

    static async analyticsMessagesGeneralInfoEnabled() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            analyticsMessagesGeneralInfo
        );
    }

    static async activeMessagePerDomainTableEnabled() {
        return FeatureToggleClientService.getInstance().isFeatureEnabled(
            activeMessagePerDomainTable
        );
    }
}
