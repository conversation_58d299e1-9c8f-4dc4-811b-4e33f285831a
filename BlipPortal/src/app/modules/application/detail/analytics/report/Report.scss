@import '~assets/scss/main';
@import '~blip-ds/dist/collection/styles/colors';

.pointer {
    & * {
        cursor: pointer;
    }
}

#report-name-input {
    @include placeholder($color-content-disable);
}

.reports-header-title {
    align-self: left;

    input {
        font-size: 1.5rem;
        font-weight: 400;
        margin-right: 1rem;
        border: none;
        background: transparent;
        outline: none;
        border: 1px solid transparent;
        color:  $color-content-default;

        &[disabled='disabled'] {
            &:focus,
            &:hover {
                color:  $color-content-default;
                border: 1px solid transparent;
            }
        }

        &:focus,
        &:hover {
            color:  $color-content-default;
            border: 1px solid $color-content-ghost;
        }
    }
}

.chart-modal-wrapper {
    @include flex-box;
    align-items: flex-end;

    .blip-select__options {
        max-height: 148px !important;
    }
}


.blip-select__content-input label.bp-label {
    color: $color-content-default;
}

.reports-chart-list {
    li {
        .analytics-chart-card {
            .card-header {
                padding-left: 5.3rem;
            }
        }

        .anchor {
            visibility: hidden;
            top: 16px;
            left: 3rem;
            margin-top: 2px;
            color: $color-content-default !important;
        }

        &:hover {
            .anchor {
                visibility: visible;
            }
        }
    }
}

#analytics {
    .bp-c-city {
        color: $color-content-default !important;
    }

    #date {
        input[type='text'] {
            height: 4 * $u;
            text-align: center;
        }
    }

    #charts {
        .google-visualization-table-table * {
            background-color: $color-surface-1;
        }
        ul {
            position: relative;
            list-style: none;

            li {
                position: relative;
                list-style: none;
            }

            .dndPlaceholder + li {
                card {
                    background-color: $color-accent;
                }
            }
        }

        .google-visualization-table-tr-even .tl {
            overflow-wrap: break-word;
            
        }

        .google-visualization-table-tr-odd .tl {
            overflow-wrap: break-word;
        }
        .th {
            color: $color-content-disable;
            text-align: left;
            padding-bottom: 5px;

        }
        .tl {
            color: $color-content-default;
                text-align: left;
            &:first-child {
                font-weight: 700;
            }
        }
    }
}

.reports-header {
    @include flex-box;
    justify-content: space-between;

    h2 {
        margin-bottom: 13px;
        line-height: 1em;
    }

    .bp-btn bp-btn--bot bp-btn--small {
        margin-top: 10px;
    }

    &__actions {
        @include flex-box;
        align-items: center;

        button {
            font-weight: 600;
            letter-spacing: 0;
        }
    }

    &__data {
        width: 70%;
    }

    &__add-chart {
        .dropdown-item-content {
            min-width: 280px;
            padding: 20px 32px;
            background-color: $color-surface-1 !important;
            color: $color-secondary !important;
            text-align: left;

        }

        div {
            span[ng-click] {
                width: 100%;
                display: inline-block;
                padding: 0;
                line-height: 38px;
            }

            .dropdown-title {
                letter-spacing: -1px;
                text-transform: none;
                font-weight: 400;
                position: relative;
                text-align: left;
                margin-bottom: 20px;
                margin-top: 12px;
                line-height: 1.8 * $m;
            }
        }

        .chart-types {
            display: inline-block;
            color: $color-content-default !important;
            line-height: 0;
            padding-left: 4px;
            margin: 0;

            li {
                list-style: none;
                padding-bottom: 5px;
                width: 50%;
                font-size: $bp-fs-6;
                font-weight: 300;
                letter-spacing: 0;
                float: left;
                text-align: left;
                text-transform: none;

                &:last-child {
                    border-bottom: 0;
                    margin-bottom: 0;
                    padding-bottom: 0;
                }

                &:nth-last-child(2) {
                    border-bottom: 0;
                    margin-bottom: 0;
                    padding-bottom: 0;
                }

                div {
                    @include flex-box;
                    align-items: center;

                    i {
                        color: $color-content-default;
                        font-size: $bp-fs-4;
                        padding-right: 10px;
                    }
                }
            }

            .chart-type-item--active {
                background: $color-primary !important;
                border: 1px solid $color-primary !important;
                color: $color-surface-1;
            }
        }
    }

    &__filter {
        text-align: right;

        datepicker {
            input[type='text'] {
                width: 85px;
                margin-left: 0;
            }
        }
    }
}

.subheader {
    @include flex-box;
    justify-content: space-between;

    .filter {
        text-align: right;

        datepicker {
            input[type='text'] {
                width: 85px;
                margin-left: 0;
            }
        }
    }
}

.font-size-16px {
    p {
        font-size: 16px !important;
    }
}
