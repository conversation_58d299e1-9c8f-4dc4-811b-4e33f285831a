import * as dimensionsJSON from '../../../../../analytics/dimensions.json';

import './ChartModal.scss';

import { IFormController } from 'angular';
import { AnalyticsServiceFactory } from 'modules/analytics/services/AnalyticsServiceFactory';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { ServiceType } from '../../../../../analytics/models';
import { debouncePromise } from 'data/function';

export type Chart = {
    id: string;
    name?: string;
    dimension: string;
    category: string;
    chartType: string;
    order?: number
};

type Dimension = {
    key: ServiceType;
    value: string;
};

const FIND_CATEGORIES_BY_FILTER_DEBOUNCE_MS = 500;

export class ChartModalController {
    chartForm: IFormController;
    selectedCategoryObject: any;
    newChart: any;
    chartCategory: string;
    chartTitle: string;
    dimensions: any;
    categories: any;
    transformedCategories: any = [];
    transformedDimensions: any = [];
    dimension: any;
    debouncedFindCategoriesByFilter: Function;

    constructor(
        private chartType: string,
        private application: any,
        private $translate: any,
        private ngToast: any,
        private AnalyticsServiceFactory: AnalyticsServiceFactory,
        private SegmentService: SegmentService,
        public close,
        public chart: Chart,
        public customEvents
    ) {
        this.init();
    }

    async init() {
        this.newChart = { ...this.chart };
        this._loadDimensions();
        this.debouncedFindCategoriesByFilter = debouncePromise(
            this.findCategoriesByFilter,
            FIND_CATEGORIES_BY_FILTER_DEBOUNCE_MS,
        );
    }

    $onInit() {
        document.getElementById('chartName').addEventListener('bdsChange', this.onChangeName);
    }

    onChangeName = (event) => {
        const { detail: { value } } = event;
        this.newChart.name = value;
    }

    isButtonDisabled() {
        return (
            this.chartForm.$invalid ||
            !this.newChart ||
            !this.newChart.dimension ||
            !this.newChart.category ||
            (this.categories &&
                this.newChart.category &&
                !this.categories.find((c) => c.key == this.newChart.category))
        );
    }

    async saveAndClose() {
        const segmentEvent = this.newChart.id == undefined ?
        'analytics-custom-reports-chart-added' : 'analytics-custom-reports-chart-edited';
        this.SegmentService.createBotTrack(
            segmentEvent,
            this.application,
            { ChartFormat: this.chartType }
        );

        if (this.categories.length <= 0) {
            const message = await this.$translate(
                'modules.application.detail.dashboard.analytics.noContext',
            );
            this.ngToast.danger(message);

            return;
        }

        const chart: Chart = {
            ...this.newChart,
            dimension: this.newChart.dimension,
            category: this.newChart.category,
            chartType: this.chartType,
        };

        this.close(chart);
    }

    async trackAndClose() {
        this.SegmentService.createBotTrack(
            'analytics-custom-reports-chart-cancel-edited',
            this.application
        );

        this.close();
    }

    async loadCategories(dimension: ServiceType, dimensionChange = false) {
        if (!dimension) {
            return;
        }

        if (dimensionChange) {
            this.newChart.category = '';
        }

        if (dimension == 'events') {
            this.categories = this.customEvents
                ? this.customEvents.map((i) => ({ key: i.key, value: i.value }))
                : [];
        } else {
            const analyticsService = this.AnalyticsServiceFactory.createService(
                dimension,
            );
            const items = dimension == 'users'
                ? await analyticsService.getCategories(this.chartType)
                : await analyticsService.getCategories();
            this.categories = items;
        }

        this.transformedCategories = this.categories
            .map(c => ({
                value: c.key,
                label: c.value}));
    }

    findCategoriesByFilter = async ({ query }, dimension: ServiceType) => {
        const analyticsService = this.AnalyticsServiceFactory.createService(dimension);
        const items = await analyticsService.getCategories(encodeURIComponent(query));
        this.categories = items;
        return items.map(c => ({
            value: c.key,
            label: c.value}));
    }

    private _loadDimensions() {
        const dimensionsKeys = dimensionsJSON
            ? Object.keys(dimensionsJSON.default)
            : [];

        const dimensions: Dimension[] = [];
        dimensionsKeys.forEach((d: ServiceType) => {
            const value = this.$translate.instant(
                `reports.dimensions.${d}`,
            );
            dimensions.push({ key: d, value: value });
        });

        this.dimensions = dimensions;

        this.transformedDimensions = this.dimensions
            .filter(d => d.key != 'usersMau')
            .map(d => ({ label: d.value, value: d.key }));

        if (this.isEditing()) {
            this.loadCategories(this.chart.dimension as ServiceType);
        }
    }

    public isEditing() {
        return this.chart.name || this.chart.dimension || this.chart.category
            ? true
            : false;
    }

    getChartGlobalTypes() {
        switch (this.chartType) {
            case 'list':
                return 'reports.chartModal.chartTypes.list';
            case 'line':
                return 'reports.chartModal.chartTypes.line';
            case 'pie':
                return 'reports.chartModal.chartTypes.pie';
            case 'bar':
                return 'reports.chartModal.chartTypes.bar';
            case 'column':
                return 'reports.chartModal.chartTypes.column';
            case 'counter':
                return 'reports.chartModal.chartTypes.counter';
        }
    }
}
