<page-header 
    back-button="{{$ctrl.isDisplayingAnalyticsTabs ? 'auth.application.detail.analytics.reports' : 'auth.application.detail.dashboard.reports'}}" 
    container="full"
    class="{{$ctrl.isDisplayingAnalyticsTabs && 'mt6'}}">
    <custom-title>
        <bds-typo class="flex flex-row items-center reports-header-title" tooltips tooltip-template="{{'utils.misc.rename' | translate}}"
            tooltip-side="bottom" tooltip-class="short">
            <input auto-expand="initial" maxlength="30" placeholder="{{'utils.misc.untitled' | translate}}" id="report-name-input" ng-disabled="!$ctrl.isOwner()"
                ng-class="{'i bp-c-rooftop': !$ctrl.reportTitle}" ng-blur="$ctrl.updateReportTitle()" ng-model="$ctrl.reportTitle"
            />
        </bds-typo>
    </custom-title>
    <custom-content>
        <div class="reports-header__actions" ng-if="$ctrl.isOwner($ctrl.report.owner)">
            <div class="reports-header__add-chart flex items-center mr2">
                <div class="small-letter-avatar f6" tooltips tooltip-template="{{'reports.createdBy' | translate}} {{$ctrl.report.owner.fullName || $ctrl.report.owner.email}}"
                    tooltip-side="bottom" tooltip-class="medium">
                    <letter-avatar text="$ctrl.reportOwnerIdentifier"></letter-avatar>
                </div>
                <dropdown-item class="ml4" 
                    item-title="<bds-button variant='primary' 
                        size='standard' disabled='false' 
                        arrow='false' icon='add' type='button' 
                        class='hydrated'>{{'reports.createChart' | translate}}
                        </bds-button>"
                    align="right" hide-icon="true">
                    <h5 class="dropdown-title" ng-class="{'chart-type-item--with-error': $ctrl.hasNoType}" translate>reports.selectChartFormat</h5>
                    <ul class="chart-types">
                        <li ng-class="{'chart-type-item--active': $ctrl.chartType == 'list'}" ng-click="$ctrl.createChart('list')">
                            <div>
                                <bds-icon type="icon" theme="solid" class="icon-list"></bds-icon>
                                <div class="pb2">{{'utils.misc.list' | translate}}</div>
                            </div>
                        </li>
                        <li ng-class="{'chart-type-item--active': $ctrl.chartType == 'line'}" ng-click="$ctrl.createChart('line')">
                            <div>
                                <bds-icon type="icon" theme="solid" class="icon-line"></bds-icon>
                                <div class="pb2">{{'utils.misc.line' | translate}}</div>
                            </div>
                        </li>
                        <li ng-class="{'chart-type-item--active': $ctrl.chartType == 'pie'}" ng-click="$ctrl.createChart('pie')">
                            <div>
                                <bds-icon type="icon" theme="solid" class="icon-pie"></bds-icon>
                                <div class="pb2">{{'utils.misc.pie' | translate}}</div>
                            </div>
                        </li>
                        <li ng-class="{'chart-type-item--active': $ctrl.chartType == 'bar'}" ng-click="$ctrl.createChart('bar')">
                            <div>
                                <bds-icon type="icon" theme="solid" class="icon-barlist"></bds-icon>
                                <div class="pb2">{{'utils.misc.bars' | translate}}</div>
                            </div>
                        </li>
                        <li ng-class="{'chart-type-item--active': $ctrl.chartType == 'column'}" ng-click="$ctrl.createChart('column')">
                            <div>
                                <bds-icon type="icon" theme="solid" class="icon-bar-copy"></bds-icon>
                                <div class="pb2">{{'utils.misc.column' | translate}}</div>
                            </div>
                        </li>
                        <li ng-class="{'chart-type-item--active': $ctrl.chartType == 'counter'}" ng-click="$ctrl.createChart('counter')">
                            <div>
                                <bds-icon type="icon" theme="solid" class="icon-layer-copy"></bds-icon>
                                <div class="pb2">{{'utils.misc.counter' | translate}}</div>
                            </div>
                        </li>
                    </ul>
                </dropdown-item>
            </div>
            <div class="icon icon--hidden">
                <bds-icon name="trash" theme="outline"  ng-auth-write ng-click="$ctrl.confirmDelete()"></bds-icon>
            </div>
        </div>
    </custom-content>
    <div ng-if="$ctrl.report.isPrivate">
        <div class="ml5 dib" tooltips tooltip-template="{{$ctrl.changeVisibilityMsg}}" tooltip-side="bottom" tooltip-class="wide"
            ng-click="$ctrl.togglePermission()">
            <img src="/assets/img/lock-permission.svg" />
            <bds-typo class="ml2 f4" tag="span" variant="fs-14" bold="regular" translate>
                ({{'reports.private'}})
            </bds-typo>
        </div>
    </div>
    <div ng-if="!$ctrl.report.isPrivate">
        <div class="ml5 dib" tooltips tooltip-template="{{$ctrl.changeVisibilityMsg}}" tooltip-side="bottom" tooltip-class="wide"
            ng-click="$ctrl.togglePermission()">
            <img src="/assets/img/unlock.svg" />
            <bds-typo class="ml2 f4" tag="span" variant="fs-14" bold="regular" translate>
                ({{'reports.public'}})
            </bds-typo>
        </div>
    </div>
</page-header>

<div id="analytics" class="container">
    <div class="subheader">
        <div></div>
        <div class="filter dib">                
            <bds-datepicker
                id="datepicker"
                type-of-date="period" 
                start-date-limit="{{$ctrl.startDateLimitFormatted}}" 
                end-date-limit={{$ctrl.endDateLimitFormatted}}
                value-date-selected="{{$ctrl.startDatePeriodFormatted}}"
                value-end-date-selected="{{$ctrl.endDatePeriodFormatted}}"
                language="{{$ctrl.language}}">
            </bds-datepicker> 
        </div>
    </div>
    <!-- has charts -->
    <div id="charts" class="reports-chart-list row">
        <ul ng-sortable="$ctrl.ngSortableOptions">
            <li ng-repeat="chartId in $ctrl.chartsList">
                <div class="anchor">
                    <bds-icon name="order-elements" type="icon" theme="outline" size="small"></bds-icon>
                </div>
                <analytics-chart class="font-size-16px" is-loading="$ctrl.charts[chartId].isLoading" chart="$ctrl.charts[chartId]" report-owner="$ctrl.report.owner"
                    on-action="$ctrl.onChartAction($action)" edit-mode="modal"></analytics-chart>
            </li>
        </ul>
    </div>

    <span ng-show="$ctrl.chartsList.length <= 0" class="no-content-found w-80" translate>
        modules.application.detail.dashboard.analytics.noGraphMessage
    </span>
</div>
