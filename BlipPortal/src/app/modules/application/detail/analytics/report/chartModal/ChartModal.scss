@import '~blip-ds/dist/collection/styles/colors';

#create-chart-modal {
    color: $color-content-default;
    .card-title{
        font-weight: 700;
        line-height: 32px;
    }
    .card-content {
        margin: 0;
        background-color: $color-surface-1;
        min-height: 270px;
    }
    .blip-select .blip-select__options ul{
        max-height: 123px;
    }

    .form-content .card-content {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-content: flex-start;
        align-items: flex-start;
        margin: 0;
        .input-text {
            width: 100%;
        }
    }
}
.header-content-modal {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.modal-footer {
    padding: 0 !important;
    display: flex;
    padding-top: 9px;
    align-items: flex-start;
}