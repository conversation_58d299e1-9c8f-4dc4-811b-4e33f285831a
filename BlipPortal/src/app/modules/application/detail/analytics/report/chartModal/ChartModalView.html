<div id="create-chart-modal" class="modal">
    <div class="modal-overlay" ng-click="$ctrl.close()"></div>
    <div class="modal-dialog modal-sm layout-center-y">
        <div class="modal-toolbar">
            <button ng-click="$ctrl.close()" type="button" class="no-style">
                <bds-icon type="icon" name="close" theme="outline"></bds-icon>
            </button>
        </div>
        <form name="$ctrl.chartForm" ng-submit="$ctrl.saveAndClose()" class="ma0 form-content">
            <div class="modal-body row card-content">
                <div>
                    <div class="header-content-modal">
                        <bds-typo variant="fs-24" bold="bold" ng-if="!$ctrl.isEditing()">{{'reports.chartModal.createChart' | translate}} {{$ctrl.getChartGlobalTypes()
                            | translate}}</bds-typo>
                        <bds-typo variant="fs-24" bold="bold" ng-if="$ctrl.isEditing()">{{'reports.chartModal.editChart' | translate}} {{$ctrl.getChartGlobalTypes()
                            | translate}}</bds-typo>
                        <bds-typo variant="fs-16" translate>reports.chartModal.subtitle</bds-typo>
                    </div>
                    <div class="insert-wrapper mt4 pb2">
                        <bds-input id="chartName" class="input-text" label="{{'reports.chartModal.name' | translate}}" value="{{$ctrl.newChart.name}}"></bds-input>
                    </div>
                    <div class="chart-modal-wrapper">
                        <div class="row five columns">
                            <blip-select name="chartDimension" ng-change="$ctrl.loadCategories($ctrl.newChart.dimension, true)" ng-model="$ctrl.newChart.dimension"
                                label="{{ 'reports.chartModal.dimension' | translate }}" mode="select"
                                options="$ctrl.transformedDimensions" />
                        </div>
                        
                        <div class="row seven columns">
                            <div ng-if="$ctrl.newChart.dimension == 'events'">
                                <blip-select name="chartCategory" disabled="!$ctrl.newChart.dimension" ng-model="$ctrl.newChart.category" label="{{ 'reports.chartModal.category' | translate }}"
                                    mode="autocomplete"
                                    custom-search="$ctrl.debouncedFindCategoriesByFilter($event, $ctrl.newChart.dimension)"
                                    options="$ctrl.transformedCategories" />
                                <error-messages form="$ctrl.chartForm" field="chartCategory" error="$ctrl.error"></error-messages>
                            </div>
                            <div ng-if="$ctrl.newChart.dimension != 'events'">
                                <blip-select name="chartCategory" disabled="!$ctrl.newChart.dimension" ng-model="$ctrl.newChart.category" label="{{ 'reports.chartModal.category' | translate }}"
                                    mode="select"
                                    options="$ctrl.transformedCategories" />
                                <error-messages form="$ctrl.chartForm" field="chartCategory" error="$ctrl.error"></error-messages>
                            </div>                            
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <bds-button type="button" variant="secondary" ng-click="$ctrl.trackAndClose()" translate>utils.forms.cancel</bds-button>
                <bds-button variant="primary" loading="$ctrl.isLoading" ng-if="!$ctrl.isEditing()" type="submit" ng-disabled="$ctrl.isButtonDisabled()" 
                    button-value="{{'utils.forms.add' | translate}}"></bds-button>
                <bds-button variant="primary" loading="$ctrl.isLoading" ng-if="$ctrl.isEditing()" type="submit" ng-disabled="$ctrl.isButtonDisabled()" 
                    button-value="{{'utils.forms.save' | translate}}"></bds-button>
            </div>
        </form>
    </div>
</div>
