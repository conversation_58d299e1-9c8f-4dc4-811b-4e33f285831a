// Misc
import 'data/array';
import { subtract, groupBy } from 'data/array';
import {
    getBrFormatDate,
    getUsFormatDate,
    toUTCISOString
} from 'data/date';
import {
    getTranslatedActions,
    maybePeriodFromCookies,
    setupPeriodToCookies,
    isEmptyEvents,
    getEmptyDataByType,
    sortByStorageDate
} from 'modules/analytics/Utils';

// Core
import { IScope, IRootScopeService, IController } from 'angular';
import { IStateService } from 'angular-ui-router';

// Types
import { Channels } from 'types/Channels';
import {
    Change,
    Delete,
    Edit,
} from 'modules/analytics/components/analyticsChart/analyticsChart.component';
import { ChartModalController } from './chartModal/ChartModalController';
import { ViewChart, ViewReport, ServiceType, AnalyticsData } from 'modules/analytics/models';
import { UserAccount } from 'modules/account/AccountService2';

// Services
import { LoadingService } from 'modules/ui/LoadingService';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { AnalyticsServiceFactory } from 'modules/analytics/services/AnalyticsServiceFactory';
import { EventTrackAnalyticsService } from 'modules/analytics/services/EventTrackAnalyticsService';
import { ConfirmationModal } from 'modules/ui';
import { AnalyticsReportsService } from '../services/AnalyticsReportsService';
import { AnalyticsChartsService } from '../services/AnalyticsChartsService';
import {
    GenerateMatrixFromApiData,
    transposeMatrix,
} from 'modules/analytics/Transformers';

// Views
import ChartModalView from './chartModal/ChartModalView.html';

// Style
import './Report.scss';
import { capitalizeF } from 'data/string';
import { colorsArray } from 'modules/analytics/components/chart/defaultOptions';

// Exports
export const UpdateReports = 'UpdateReports';
export const DeleteReport = 'DeleteReport';

import { BlipService } from 'modules/messaginghub/BlipService';
import moment from 'moment';
import { DaterangerPickerPeriod } from 'interfaces/DaterangerPicker';
import { ReportFeatures } from './ReportFeatures';
import { ChartType } from 'modules/analytics/models/Chart';
import { AnalyticsFeatures } from '../AnalyticsFeatures';
import { analyticsReportStateName, analyticsReportsStateName, reportsStateName, reportStateName } from '..';
import { CONSTANTS } from '../utils/AnalyticsConstants';
import { MinimunDatePeriodExtension, MinimunDatePeriod } from '../utils/AnalyticsFunctions';
import { IAnalyticsService } from 'modules/analytics/services/IAnalyticsService';
import { UserBlipAccount } from 'modules/account/AccountService2';

const PENDING_STATUS_CODE = 7390;
const languages = {'pt': 'pt_BR', 'es': 'es_ES', 'en': 'en_US'};

export class ReportController implements IController {
    untitled: any;
    reportTitle: string;
    ngSortableOptions: {
        animation: number;
        scroll: boolean;
        handle: string;
        onSort: (e: any) => void;
    };
    cookiesPeriodKey: string;
    private customEvents: Promise<any>;
    public filterItems: any[] = [];
    public filters: { [name: string]: {}[] } = {};
    public period: { start: any; end: any };
    public chartsList: Array<string> = [];
    public charts: { [name: string]: ViewChart } = {};
    public dimensions: any;
    public me: UserBlipAccount;
    public report: ViewReport;
    public reports: ViewReport[];
    daterangePickerPeriod: DaterangerPickerPeriod;
    isUsingCustomReportCountEndPoint: boolean = false;
    isDisplayingAnalyticsTabs: boolean = false;
    enableLakeDatasource: boolean = false;
    lakeDataSource: string = 'lake';
    defaultDataSource: string = 'db';
    startDateLimit: string;
    language: string = 'pt-BR';

    constructor(
        private $state: IStateService,
        private ModalService,
        private application,
        private $translate,
        private $rootScope: IRootScopeService,
        private $scope: IScope,
        private ngToast,
        private AccountService2,
        private LoadingService: LoadingService,
        private AnalyticsServiceFactory: AnalyticsServiceFactory,
        private EventTrackAnalyticsService: EventTrackAnalyticsService,
        private SegmentService: SegmentService,
        private AnalyticsReportsService: AnalyticsReportsService,
        private AnalyticsChartsService: AnalyticsChartsService,
        private BlipService: BlipService,
        private $cookies
    ) {
        this.cookiesPeriodKey = `${
            this.application.shortName
        }_customReportsPeriodSetting`;
        this.untitled = this.$translate.instant('utils.misc.untitled');
        this.ngSortableOptions = {
            animation: 100,
            scroll: true,
            handle: '.anchor',
            onSort: (e) => this.sortCharts(),
        };

        this.initFilters();
    }

    async checkFeatures(): Promise<any> {
        this.isUsingCustomReportCountEndPoint = await ReportFeatures.isUsingCustomReportCountEndPoint();
        this.isDisplayingAnalyticsTabs = !await AnalyticsFeatures.isHidingAnalyticsTabs();
        this.enableLakeDatasource = await AnalyticsFeatures.enableLakeDatasource();
    }

    get reportOwnerIdentifier() {
        if (this.report && this.report.owner) {
            return this.report.owner.fullName || this.report.owner.email;
        } else {
            return '';
        }
    }

    get usEndDate() {
        return getUsFormatDate(this.period.end);
    }

    get usStartDate() {
        return getUsFormatDate(this.period.start);
    }

    get changeVisibilityMsg() {
        if (this.isOwner()) {
            return this.report.isPrivate
                ? this.$translate.instant('reports.privateTooltip')
                : this.$translate.instant('reports.publicTooltip');
        } else {
            return this.$translate.instant('utils.errorMsg.74');
        }
    }
    
    get startDateLimitFormatted() {
        if (!this.startDateLimit) {
            if (this.period.start < MinimunDatePeriod()) {
                this.startDateLimit = moment(MinimunDatePeriod()).format(CONSTANTS.DEFAULT_DATE_FORMAT);
            } else {
                this.startDateLimit = moment(MinimunDatePeriodExtension()).format(CONSTANTS.DEFAULT_DATE_FORMAT);
            }
        }
        return moment(this.startDateLimit, CONSTANTS.DEFAULT_DATE_FORMAT).format(CONSTANTS.DEFAULT_DATE_FORMAT);
    }

    set startDateLimitFormatted(newDate: string) {
        this.startDateLimit = moment(newDate, CONSTANTS.DEFAULT_DATE_FORMAT).format('DD/MM/YYYY');
    }

    get endDateLimitFormatted() {
        return moment().format(CONSTANTS.DEFAULT_DATE_FORMAT);
    }

    get startDatePeriodFormatted() {
        return moment(this.period.start, CONSTANTS.DEFAULT_DATE_FORMAT).format(CONSTANTS.DEFAULT_DATE_FORMAT);
    }

    set startDatePeriodFormatted(newDate: string) {
        this.period.start = moment(newDate, CONSTANTS.DEFAULT_DATE_FORMAT).format(CONSTANTS.DEFAULT_DATE_FORMAT);
    }
    
    get endDatePeriodFormatted() {
        return moment(this.period.end, CONSTANTS.DEFAULT_DATE_FORMAT).format(CONSTANTS.DEFAULT_DATE_FORMAT);
    }

    private mapLanguage(language: string): string {
        return languages[language] || 'pt_BR';
    }

    private updateSelectedPeriod() {
        const startDate = moment(this.period.start, CONSTANTS.DEFAULT_DATE_FORMAT).toDate();
        const endDate = moment(this.period.end, CONSTANTS.DEFAULT_DATE_FORMAT).toDate();
    
        this.daterangePickerPeriod = {
            selectedPeriod: {
                startDate,
                endDate,
            },
            validPeriod: {
                startDate: MinimunDatePeriod(),
                endDate: moment()
                    .add(1, 'days')
                    .toDate(),
            },
        };
    }

    setStartDate(date: Date) {
        const formattedDate = moment(date).format(CONSTANTS.DEFAULT_DATE_FORMAT);
        this.startDatePeriodFormatted = formattedDate;
    }

    setStartDateLimit(date: Date) {
        const formattedDate = moment(date).format(CONSTANTS.DEFAULT_DATE_FORMAT);
        this.startDateLimitFormatted = formattedDate;
    }

    private async updateValidPeriodForEvents(charts?: ViewChart[]) {
        const chartsDimension = charts ? charts.map((c) => c.dimension) : [];

        const allEventsDimension = chartsDimension.length > 0 && chartsDimension.every(dimension => dimension === 'events');

        if (allEventsDimension) {
            this.daterangePickerPeriod.validPeriod.startDate = MinimunDatePeriodExtension();
            this.setStartDateLimit(MinimunDatePeriodExtension());
        } else {
            this.daterangePickerPeriod.validPeriod.startDate = MinimunDatePeriod();
            this.setStartDateLimit(MinimunDatePeriod());
            const validateInterval = this.validateInterval();
            if (validateInterval) {
                this.setStartDate(MinimunDatePeriod());
                this.daterangePickerPeriod.selectedPeriod.startDate = MinimunDatePeriod();
                this.daterangePickerPeriod.selectedPeriod.endDate = moment().toDate();
                this.period.end = moment().format(CONSTANTS.DEFAULT_DATE_FORMAT);
                
            }
        }
        await this.populateCharts();
    }

    private validateInterval() {
        const today = moment().startOf('day');
        const selectedStartDate = moment(this.daterangePickerPeriod.selectedPeriod.startDate).startOf('day');
        const daysDifference = Math.abs(selectedStartDate.diff(today, 'days'));
        return daysDifference > 94;
    }

    async $onInit() {
        this.period = maybePeriodFromCookies({
            $cookies: this.$cookies,
            key: this.cookiesPeriodKey,
            initialInterval: 30,
        });
        try {
            this.LoadingService.startLoading();
            this.updateSelectedPeriod();
            this.initializeDatepicker();
            this.language = this.mapLanguage(this.$translate.use());

            await this.checkFeatures();
            await this.getCurrentUserAccount();

            if (this.$state.params.reportId) {
                await this.getReport(this.$state.params.reportId);
            }

            await this.bindCustomEventCategories();

            const reportDoesExist = this.$state.params && this.$state.params.reportId;
            const isReportDetailRouteButReportDoesNotExist = [reportStateName, analyticsReportStateName]
                .includes(this.$state.current.name);

            if (reportDoesExist) {
                await this.openReportDetail();
            } else if (isReportDetailRouteButReportDoesNotExist) {
                await this.createNewReport();
            }
        } catch (error) {
            console.error('Error loading custom reports data.', error);
        } finally {
           this.LoadingService.stopLoading();
        }
    }

    private async createNewReport() {
        const email = this.me.email || this.BlipService.localNode();
        this.report = new ViewReport({
            name: undefined,
            isPrivate: true,
            owner: this.me,
            ownerUserIdentity: `${encodeURIComponent(email)}@blip.ai`,
        });

        this.reportTitle = this.report.name;

        this.report.id = undefined;
        const reportId = await this.AnalyticsReportsService.set(
            this.report
        );

        this.report.id = reportId;
        this.$state.go('.', { reportId: reportId }, { notify: false });
    }

    async getReport(reportId: string) {
        try {
            this.report = await this.AnalyticsReportsService.get(
                reportId,
            );
        } catch (e) {
            console.error(e);
        }
    }

    async openReportDetail() {
        try {
            this.LoadingService.startLoading();

            this.reportTitle = this.report ? this.report.name : undefined;
            await this.initializeCharts();
            await this.populateCharts();

            this.$scope.$watch('$ctrl.report', (newValue) => {
                if (newValue) {
                    this.SegmentService.createTrack('view-analytics-report', {
                        botName: this.application.name,
                    });
                }
            });
        } catch (e) {
            console.error(e);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async getCurrentUserAccount() {
        this.me = await this.AccountService2.me();
    }

    async initFilters() {
        const channels = await this.$translate('modules.ui.filterBy.channels');
        this.filters = {
            [channels]: Channels,
        };
    }

    addFilterItem(item, key) {
        this.filterItems = this.filterItems.concat({ ...item, key }); //Add new item to filter list

        this.filters[key] = Channels.filter(subtract(this.filterItems));

        this.$rootScope.$broadcast('ToggleDropdownItem');
        this.$rootScope.$broadcast('angucomplete-alt:clearInput');
    }

    removeFilterItem(newFilters, removedItem) {
        this.filterItems = newFilters;
        this.filters[removedItem.key] = Channels.filter(
            subtract(this.filterItems),
        );
    }

    private sortCharts() {
        this.saveAnalyticsChartsOrder();
    }

    private saveAnalyticsChartsOrder() {
        const charts = this.chartsList
            .map((id, index) => ({
                id,
                dimension: this.charts[id].dimension || 'events',
                category: this.charts[id].category,
                chartType: this.charts[id].chartType || 'column',
                name: this.charts[id].name,
                order: index,
            }))
            .filter(
                (chart) => chart.chartType && chart.dimension && chart.category,
            );

        this.AnalyticsChartsService.setMany(
            this.report.id,
            charts,
        );
    }

    private async initializeDatepicker() {
        const datepicker = document.getElementById('datepicker') as HTMLElement;

        if (datepicker) {
            datepicker.addEventListener('concludeDatepicker', async (event: CustomEvent) => {
                let triggerDatepickerEvent;
                const eventStartDate = moment(event.detail.startDate, 'DD/MM/YYYY');
                const eventEndDate = moment(event.detail.endDate, 'DD/MM/YYYY');

                const selectedStartDate = moment(this.daterangePickerPeriod.selectedPeriod.startDate, CONSTANTS.DEFAULT_DATE_FORMAT);
                const selectedEndDate = moment(this.daterangePickerPeriod.selectedPeriod.endDate, CONSTANTS.DEFAULT_DATE_FORMAT);

                if (!selectedStartDate.isSame(eventStartDate)) {
                    this.daterangePickerPeriod.selectedPeriod.startDate = eventStartDate.toDate();
                    triggerDatepickerEvent = true;
                }

                if (!selectedEndDate.isSame(eventEndDate)) {
                    this.daterangePickerPeriod.selectedPeriod.endDate = eventEndDate.toDate();
                    triggerDatepickerEvent = true;
                }

                if (triggerDatepickerEvent) {
                    await this.populateCharts('user');
                    triggerDatepickerEvent = false;
                }
            });
        }
    }

    private async bindCustomEventCategories() {
        const pagination = {take: CONSTANTS.GET_CATEGORIES_TAKE_SIZE};
        try {
            this.customEvents = await this.EventTrackAnalyticsService.getCategories(undefined, pagination);
        } catch (e) {
            switch (e.code) {
                case 67:
                    // Ignore if not found
                    break;
                default:
                    console.error(e);
            }
        }
    }

    async onChartAction(action) {
        switch (action.constructor) {
            case Change:
                await this.updateChart(action.chart);
                break;
            case Delete:
                this.confirmDeleteChart(action.chart);
                break;
            case Edit:
                this.editChart(action.chart);
                break;
        }
    }

    async confirmDeleteChart({ id }) {
        const yes = await this.$translate('utils.misc.yes');
        const no = await this.$translate('utils.misc.no');
        const title = await this.$translate(
            'reports.chartModal.deleteModalTitle',
        );
        const body = await this.$translate('reports.chartModal.body');

        const modal = await this.ModalService.showModal(
            ConfirmationModal({
                title: {
                    text: title,
                },
                body,
                buttons: {
                    confirm: {
                        text: yes,
                    },
                    cancel: {
                        text: no,
                    },
                },
            }),
        );

        const confirmation = await modal.close;

        if (confirmation) {
            this.SegmentService.createBotTrack(
                'analytics-custom-reports-chart-deleted',
                this.application,
                { ChartFormat: this.charts[id].chartType }
            );
            await this.deleteChart(id);
        } else {
            this.SegmentService.createBotTrack(
                'analytics-custom-reports-delete-chart-canceled',
                this.application,
                { ChartFormat: this.charts[id].chartType }
            );
        }
    }

    registerReportIfDontExists() {
        if (!this.reports.find((r) => r.id == this.report.id)) {
            const newReport = new ViewReport({
                ...this.report,
                name: this.reportTitle || this.untitled,
            });

            this.reports = this.reports.concat(newReport);
        }
    }

    async _saveAnalyticsChart(viewChart: ViewChart) {
        try {
            this.LoadingService.startLoading();

            const chart = {
                id: viewChart.id,
                name: viewChart.name,
                chartType: viewChart.chartType,
                dimension: viewChart.dimension,
                category: viewChart.category,
                order: viewChart.order,
            };

            const chartId = await this.AnalyticsChartsService.set(
                this.report.id,
                chart,
            );
            viewChart.id = chartId;

            this.charts = { ...this.charts, [viewChart.id]: viewChart };
            if (!this.chartsList.includes(viewChart.id)) {
                this.chartsList = [viewChart.id, ...this.chartsList];
                await this.populateChart(viewChart);
            }

            this.saveAnalyticsChartsOrder();

            await this.updateAnalyticsReportModifiedAt(this.report);
        } catch (e) {
            console.error(e);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async _deleteAnalyticsChart(chartId: string) {
        try {
            this.LoadingService.startLoading();

            await this.AnalyticsChartsService.delete(
                this.report.id,
                chartId,
            );

            await this.updateAnalyticsReportModifiedAt(this.report);

            await this.getAllAnalyticsCharts(this.report.id);
        } catch (e) {
            console.error(e);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async updateAnalyticsReportModifiedAt(report) {
        //Constructor changes automatically modifiedAt property
        this.report = new ViewReport({ ...report });
        await this.AnalyticsReportsService.set(this.report);
    }

    updateReportModifiedAt(report) {
        return report.id == this.report.id
            ? new ViewReport({ ...report }) // Constructor changes automatically modifiedAt property
            : { ...report };
    }

    async deleteChart(id) {
        this.chartsList = this.chartsList.filter((_id) => _id !== id);
        this.charts = { ...this.charts, [id]: undefined };

        delete this.charts[id];

        await this._deleteAnalyticsChart(id);
    }

    async updateChart(chart: ViewChart) {
        const oldChart = this.charts[chart.id];

        if (chart.id) {
            this.charts = { ...this.charts, [chart.id]: chart };
        }

        await this._saveAnalyticsChart(chart);


        if (
            oldChart &&
            (chart.dimension !== oldChart.dimension ||
                chart.category !== oldChart.category ||
                chart.chartType !== oldChart.chartType)
        ) {
            await this.populateChart(chart);
        }
        await this.getAllAnalyticsCharts(this.report.id);
    }

    async editChart(chart) {
        const modal = await this.ModalService.showModal({
            template: ChartModalView,
            controller: ChartModalController,
            controllerAs: '$ctrl',
            inputs: {
                chart,
                chartType: chart.chartType,
                application: this.application,
                AnalyticsServiceFactory: this.AnalyticsServiceFactory,
                customEvents: this.customEvents,
            },
        });

        const data = await modal.close;
        if (data) {
            await this.updateChart(data);
        }
    }

    async createChart(chartType) {
        let chart;

        chart = {};

        const modal = await this.ModalService.showModal({
            template: ChartModalView,
            controller: ChartModalController,
            controllerAs: '$ctrl',
            inputs: {
                chart,
                chartType: chartType,
                application: this.application,
                customEvents: this.customEvents,
            },
        });

        const data = await modal.close;

        //Add new chart on list and update bucket
        if (data) {
            this.onChartAction(new Change({ chart: data }));
        }
    }

    /**
     * Initialize charts array with default dimension and chartType
     */
    private async initializeCharts() {
        try {
            let resource: { charts?: ViewChart[] };

            resource = await this.getAllAnalyticsCharts(
                this.report.id,
            );

            const charts = resource != undefined ? resource.charts : [];
            this.charts = charts.reduce(
                (cs, c) => ({
                    ...cs,
                    [c.id]: new ViewChart({
                        dimension: 'events',
                        chartType: 'column',
                        ...c,
                    }),
                }),
                {},
            );

            this.chartsList = charts.map((c) => c.id);
        } catch (e) {
            switch (e.code) {
                case 67:
                    // Ignore if not found
                    break;
                default:
                    this._eraseChartsData();
                    console.error(e);
            }
        }
    }

    private async getAllAnalyticsCharts(reportId: string) {
        let count = 0;
        const analyticsCharts = [];

        while (true) {
            const { charts } = await this.AnalyticsChartsService.getMany(
                reportId,
                count,
                CONSTANTS.GET_CHARTS_TAKE_SIZE,
            );
            analyticsCharts.push(...charts);

            count += CONSTANTS.GET_CHARTS_TAKE_SIZE;
            if (charts.length < CONSTANTS.GET_CHARTS_TAKE_SIZE) {
                break;
            }
        }
        this.updateValidPeriodForEvents(analyticsCharts);

        return { charts: analyticsCharts };
    }

    _eraseChartsData() {
        this.chartsList.forEach((id) => {
            this.charts[id].data = undefined;
        });
    }

    async populateCharts(periodSource: string = 'default') {
        if (periodSource == 'user') {
            this.SegmentService.createBotTrack(
                'analytics-custom-reports-chart-date-changed',
                this.application
            );
        }

        this.period = {
            start: moment(this.daterangePickerPeriod.selectedPeriod.startDate).format(CONSTANTS.DEFAULT_DATE_FORMAT),
            end: moment(this.daterangePickerPeriod.selectedPeriod.endDate).format(CONSTANTS.DEFAULT_DATE_FORMAT),
        };

        const startDate = getBrFormatDate(this.period.start);
        const endDate = getBrFormatDate(this.period.end);

        if (startDate > endDate) {
            const errorMsg99 = await this.$translate('utils.errorMsg.99');
            this.ngToast.warning(errorMsg99);

            return;
        }

        /**
         * Get current period and put to cookies object
         */
        setupPeriodToCookies({
            $cookies: this.$cookies,
            period: this.period,
            key: this.cookiesPeriodKey,
        });

        const populateChartsTasks = this.chartsList.map(async id => {
            this.populateChart(this.charts[id]);
        });
        await Promise.all(populateChartsTasks);
    }

    async populateChart(userChart) {
        const chart = new ViewChart({ ...this.charts[userChart.id] });

        const startDate = toUTCISOString(this.daterangePickerPeriod.selectedPeriod.startDate, 0, 0, 0, 0);
        const endDate = toUTCISOString(this.daterangePickerPeriod.selectedPeriod.endDate, 23, 59, 59, 999);

        try {
            this.charts = {
                ...this.charts,
                [userChart.id]: new ViewChart({
                    ...chart,
                    isLoading: true,
                    data: undefined,
                }),
            };

            const dimension = capitalizeF(chart.dimension);
            const AnalyticsService = this.AnalyticsServiceFactory.createService(
                <ServiceType>ServiceType[dimension],
            );

            const isEventCounterChart = chart.chartType === ChartType.Counter && chart.dimension === 'events';

            if (isEventCounterChart && this.isUsingCustomReportCountEndPoint) {
                const chartData = await AnalyticsService.getEventTrackCategoryCount({
                    chartType: chart.chartType,
                    category: chart.category,
                    endDate,
                    startDate,
                    application: this.application,
                });

                this.charts = {
                    ...this.charts,
                    [userChart.id]: new ViewChart({
                        ...chart,
                        data: chartData,
                        isLoading: false,
                    }),
                };

            } else {
                const events = await this.getEvents(AnalyticsService, chart, endDate, startDate);

                const groupedActions = groupBy(events, 'action');

                const actionCounts = this.getActionCounts(groupedActions);

                const chartEvents = this.getChartEvents(groupedActions, events);

                const actions = await this.$translate('utils.misc.actions');
                const day = await this.$translate('utils.misc.day');

                const totalEvents = actionCounts.reduce(
                    (acc, action) => acc + action.count,
                    0,
                );
                const actionCountsList = actionCounts.map((a) => a.count);

                const data = this.buildChartData(
                    chart,
                    actions,
                    groupedActions,
                    actionCountsList,
                    actionCounts,
                    totalEvents,
                    day,
                    chartEvents);

                this.charts = {
                    ...this.charts,
                    [userChart.id]: new ViewChart({
                        ...chart,
                        data: isEmptyEvents(events)
                            ? getEmptyDataByType(data)
                            : data,
                        isLoading: false,
                    }),
                };
            }

        } catch (e) {
            if (e.code != PENDING_STATUS_CODE) {
                this.charts = {
                    ...this.charts,
                    [userChart.id]: new ViewChart({
                        ...this.charts[userChart.id],
                        data: undefined,
                        isLoading: false,
                    }),
                };
            }
            console.error(e);
        }
    }

    private async getEvents(AnalyticsService: IAnalyticsService, chart: ViewChart, endDate: string, startDate: string) {
        return await AnalyticsService.getActionsForCategory({
            chartType: chart.chartType,
            category: chart.category,
            endDate,
            startDate,
            application: this.application,
        });
    }

    private getChartEvents(groupedActions: any, events: AnalyticsData[]) {
        return Object.keys(groupedActions)
            .reduce(
                (acs, a) => acs.concat([events.filter((e) => e.action == a)]),
                []
            )
            .map((e) => e.sort(sortByStorageDate));
    }

    private getActionCounts(groupedActions: any) {
        return Object.keys(groupedActions).map((action) => ({
            value: action,
            count: groupedActions[action]
                .map((i) => i.count)
                .reduce((previous, current) => previous + current),
        }));
    }

    private buildChartData(chart: ViewChart, actions: any, groupedActions: any, actionCountsList: any[], actionCounts: { value: string; count: any; }[], totalEvents: any, day: any, chartEvents: any[]) {
        switch (chart.chartType) {
            case ChartType.Bar:
            case ChartType.Column:
                return this.buildBarColumnData(actions, groupedActions, actionCountsList, chart.dimension);
            case ChartType.Pie:
                return this.buildPieData(actions, groupedActions, actionCountsList, chart.dimension);
            case ChartType.Table:
            case ChartType.List:
                return this.buildListTableData(actions, groupedActions, actionCounts, totalEvents, actionCountsList, chart.dimension);
            case ChartType.Counter:
                return totalEvents;
            default:
                return this.buildLineData(day, groupedActions, chartEvents, chart.dimension);
        }
    }

    private buildLineData(day: any, groupedActions: any, chartEvents: any[], dimension) {

        return [
            [
                day,
                ...getTranslatedActions(
                    this.$translate,
                    Object.keys(groupedActions),
                    ChartType.Line,
                    dimension
                ),
            ],
            ...GenerateMatrixFromApiData(chartEvents, this.period),
        ];
    }

    private buildPieData(actions: any, groupedActions: any, actionCountsList: any[], dimension: string) {
        return transposeMatrix([
            [
                actions,
                ...getTranslatedActions(
                    this.$translate,
                    Object.keys(groupedActions),
                    ChartType.Pie,
                    dimension
                ),
            ],
            ['Total', ...actionCountsList],
        ]);
    }

    private buildListTableData(
        actions: any,
        groupedActions: any,
        actionCounts: { value: string; count: any; }[],
        totalEvents: any,
        actionCountsList: any[],
        dimension: string) {
        return transposeMatrix([
            [
                actions,
                ...getTranslatedActions(
                    this.$translate,
                    Object.keys(groupedActions),
                    ChartType.List,
                    dimension
                ),
            ],
            [
                '%',
                ...actionCounts.map(
                    (a) => `${((a.count / totalEvents) * 100).toFixed(
                        2
                    )}%`
                ),
            ],
            ['Total', ...actionCountsList],
        ]);
    }

    private buildBarColumnData(actions: any, groupedActions: any, actionCountsList: any[], dimension: string) {
        return transposeMatrix([
            [
                actions,
                ...getTranslatedActions(
                    this.$translate,
                    Object.keys(groupedActions),
                    ChartType.Bar,
                    dimension
                ),
            ],
            ['Total', ...actionCountsList],
            [
                { role: 'style' },
                ...getTranslatedActions(
                    this.$translate,
                    Object.keys(groupedActions),
                    ChartType.Bar,
                    dimension
                ).map(
                    (c, i) => colorsArray[i % colorsArray.length]
                ),
            ],
        ]);
    }

    async openChartModal(chartType) {
        const modal = await this.ModalService.showModal({
            template: ChartModalView,
            controller: ChartModalController,
            controllerAs: '$ctrl',
            inputs: {
                chartType: chartType,
                application: this.application,
                customEvents: this.customEvents,
            },
        });

        const data = await modal.close;
        if (data) {
            this.onChartAction(new Change({ chart: data }));
        }
    }

    /* Reports */
    private async bindReport() {
        try {
            this.report = this.reports.find(
                (r) => r.id == this.$state.params.reportId,
            );

            this.reportTitle = this.report ? this.report.name : undefined;
        } catch (e) {
            switch (e.code) {
                case 67:
                    // Ignore if not found
                    break;
                default:
                    console.error(e);
            }
        }
    }

    async deleteReport() {
        await this.AnalyticsReportsService.delete(
            this.report.id,
        );

        this.SegmentService.createTrack('delete-analytics-report', {
            botName: this.application.name,
        });
        this.isDisplayingAnalyticsTabs ? this.$state.go(analyticsReportsStateName) : this.$state.go(reportsStateName);
    }

    isOwner(user?: UserAccount) {
        if (user) {
            return this.me && user && user.email == this.me.email;
        }
        return this.report && this.report.owner.email == this.me.email;
    }

    async updateReportTitle() {
        try {
            const newReport = new ViewReport({
                ...this.report,
                name: this.reportTitle || this.untitled,
            });

            await this.AnalyticsReportsService.set(
                newReport,
            );

            this.report = newReport;
        } catch (e) {
            console.error(e);
        }
    }

    async togglePermission() {
        this.SegmentService.createBotTrack(
            'analytics-custom-reports-permission-changed',
            this.application
        );

        if (!this.isOwner()) {
            return;
        }

        try {
            const isPrivate = !this.report.isPrivate;
            this.report = { ...this.report, isPrivate };

            this.AnalyticsReportsService.set(this.report);
        } catch (e) {
            console.error(e);
        }
    }

    async confirmDelete() {
        if (!this.report.id) {
            this.$state.go('auth.application.detail.dashboard.reports');
            return;
        }

        const yes = await this.$translate('utils.misc.yes');
        const no = await this.$translate('utils.misc.no');
        const title = await this.$translate('reports.modal.title');
        const body = await this.$translate('reports.modal.body');

        const modal = await this.ModalService.showModal(
            ConfirmationModal({
                title: {
                    text: title,
                },
                body: body,
                buttons: {
                    confirm: {
                        text: yes,
                    },
                    cancel: {
                        text: no,
                    },
                },
            }),
        );

        const confirmation = await modal.close;

        if (confirmation) {
            this.deleteReport();
        }
    }

    private getDataSourceName(): string {
        if (this.enableLakeDatasource) {
            return this.lakeDataSource;
        }

        return this.defaultDataSource;
    }
}
