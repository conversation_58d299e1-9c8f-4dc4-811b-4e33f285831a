/**
 * This colors is used in charts or anothers files when imports of the colors of DS is not possible.
 */
/**
 * Colors blip scheme
 */
export const COLOR_BRAND = '#0096fa';
export const COLOR_PRIMARY = '#1968f0';
export const COLOR_SECONDARY = '#1f1f1f';
/**
 * Content colors
 */
export const COLOR_CONTENT_DEFAULT = '#454545';
export const COLOR_CONTENT_DISABLE = '#636363';
export const COLOR_CONTENT_GHOST = '#8C8C8C';
export const COLOR_CONTENT_BRIGHT = '#ffffff';
export const COLOR_CONTENT_DIN = '#000000';

/**
 * Surface colors
 */
export const COLOR_SURFACE_1 = '#F6F6F6';
export const COLOR_SURFACE_2 = '#E0E0E0';
export const COLOR_SURFACE_3 = '#C7C7C7';
export const COLOR_SURFACE_4 = '#141414';
/**
 * Feedback colors
 */
export const COLOR_INFO = '#c5d9fb';
export const COLOR_SYSTEM = '#B2DFFD';
export const COLOR_FOCUS = '#c226fb';
export const COLOR_SUCCESS = '#84ebbc';
export const COLOR_WARNING = '#fde99b';
export const COLOR_ERROR = '#f67979';
export const COLOR_DELETE = '#e60f0f';

/**
 * Color used for charts
 */
export const COLORS_CHART = {
    colors: {
        extended_blue: '#1968F0',
        extended_ocean: '#00D3E4',
        extended_green: '#35DE90',
        extended_yellow: '#FBCF23',
        extended_orange: '#F06305',
        extended_red: '#E60F0F',
        extended_pink: '#FB4BC1',
        extended_gray: '#666666',
    },
};
