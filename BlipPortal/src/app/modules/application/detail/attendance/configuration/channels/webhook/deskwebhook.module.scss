@import '~assets/scss/main';

.paragraph {
    width: 100%;
    margin: 0;
    font-size: $bp-fs-6;
    line-height: $bp-lh-plus;
    color: $color-content-default;
}
.labelContainer {
    max-width: 40*$m;
}

.section {
    padding: 1*$m 0;
}

.typoLegend{
    margin-bottom: 10px;
}

.typoTitle {
    display: block;
    color: $color-content-default;
    margin-bottom: 1.875rem;
 }

 .checkboxes{
    display: inline-grid;
    color: $color-content-default;
    
    label{
        font-weight: normal !important;
    }
}

:global(#desk-webhook-provider-overview-tab) {
    .section {
        display: flex;
        align-items: center;
    }

    .logo {
        width: 7*$m;

        flex-shrink: 0;

        margin-right: 4*$m;
    }
}

:global(#desk-webhook-provider-configuration-tab) {
    .form {
        margin: 0;

        .paragraph {
            margin-bottom: 1*$m;
        }

        .input {
            width: 100%;
            max-width: 40*$m;

            display: block;

            margin-bottom: 1*$m;
        }
        .multipleInput {
            width: 100%;
            max-width: 43*$m;

            display: block;

            margin-bottom: 1*$m;
        }

        .footer {
            margin-top: 2*$m;
            width: 100%;
            text-align: right;
            justify-content: flex-end;
            border-top: none;
        }

        .bp-input{
            label {
                color: green;
            }
        }
    }
}

:global(#desk-webhook-contact-configuration-tab) {
    .contactEntityProperty{
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        margin-left: $m;
    }
    .contactdesk-webhookFields {
        display: flex;
        flex-direction: row;

        * {
            width: 100%;
            margin-right: 0.5 * $m;
        }
    }

    .footer {
        margin-top: 2*$m;
        padding-top: $m;
        width: 100%;
        height: auto;
        text-align: right;
        justify-content: flex-end;
        border-top: none;
    }

    .actionIcon {
        width: 32px;
        height: 20px;
        margin-right: 5px;
        align-self: center;
    }
}
