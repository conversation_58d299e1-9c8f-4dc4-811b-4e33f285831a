<page-header id="salesforce-channel-header" back-button="auth.application.detail.attendance.channels" page-title="{{'salesforce.title' | translate}}">
    <custom-content>
        <switch
            id="channel-switch"
            ng-permission="true"
            on-toggle="$ctrl.onSwitch(value)"
            ng-model="$ctrl.isActive">
        </switch>
    </custom-content>
</page-header>

<div id="salesforce-channel-page" class="container">
    <article class="bp-card bp-card--left-arrow">
        <content-tabs>
            <tab id="salesforce-overview-tab"
                tab-title="{{'salesforce.overview.title' | translate}}"
                track-click="helpdesk-integrations-salesforce-overview-opened">
                <section class="{{$ctrl.styles.section}}">
                    <img class="{{$ctrl.styles.logo}}"
                        src="/assets/img/desk/providers/salesforce.svg"
                        alt="{{ 'salesforce.overview.image.alt' | translate }}"
                        title="{{ 'salesforce.overview.image.title' | translate }}">
                        <div>
                            <bds-typo tag="p" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate>
                                salesforce.overview.description.1
                            </bds-typo>
                            <p></p>
                            <bds-typo tag="p" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate>
                                salesforce.overview.description.2
                            </bds-typo>
                        </div>
                </section>
            </tab>

            <tab id="salesforce-configuration-tab"
                tab-title="{{'salesforce.configuration.title' | translate}}"
                track-click="helpdesk-integrations-salesforce-config-opened">
                <section class="{{$ctrl.styles.section}}">
                    <form id="salesforce-configuration-form"
                        class="{{$ctrl.styles.form}}"
                        name="$ctrl.configurationForm"
                        ng-submit="$ctrl.submitConfigurationForm()"
                        novalidate>
                        <bds-typo tag="h1" variant="fs-20" bold="semi-bold" class="{{$ctrl.styles.typoTitle}}" translate>
                            salesforce.configuration.form.title
                        </bds-typo>
                        <div class="{{$ctrl.styles.typoLegend}}">
                            <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate="">
                                salesforce.configuration.form.description.1
                            </bds-typo>
                            <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate>
                                salesforce.configuration.form.description.2
                            </bds-typo>
                        </div>
                        <blip-input-dpr
                            id="salesforce-configuration-api-endpoint-input"
                            class="{{$ctrl.styles.input}}"
                            type="text"
                            ng-model="$ctrl.configuration['Salesforce.ApiEndpoint']"
                            disabled="$ctrl.channel.isActive"
                            field-name="api-endpoint"
                            field-id="api-endpoint"
                            label="{{'salesforce.configuration.form.apiEndpoint.label' | translate}}"
                            placeholder="{{'salesforce.configuration.form.apiEndpoint.placeholder' | translate}}"
                            required="true">
                        </blip-input-dpr>

                        <blip-input-dpr
                            id="salesforce-configuration-organization-id-input"
                            class="{{$ctrl.styles.input}}"
                            type="text"
                            ng-model="$ctrl.configuration['Salesforce.OrganizationId']"
                            disabled="$ctrl.channel.isActive"
                            field-name="organization"
                            field-id="organization"
                            label="{{'salesforce.configuration.form.organizationId.label' | translate}}"
                            placeholder="{{'salesforce.configuration.form.organizationId.placeholder' | translate}}"
                            required="true">
                        </blip-input-dpr>

                        <blip-input-dpr
                            id="salesforce-configuration-deploymnet-id-input"
                            class="{{$ctrl.styles.input}}"
                            type="text"
                            ng-model="$ctrl.configuration['Salesforce.DeploymentId']"
                            field-name="deployment"
                            field-id="deployment"
                            disabled="$ctrl.channel.isActive"
                            label="{{'salesforce.configuration.form.deploymentId.label' | translate}}"
                            placeholder="{{'salesforce.configuration.form.deploymentId.placeholder' | translate}}"
                            required="true">
                        </blip-input-dpr>

                        <blip-input-dpr
                            id="salesforce-configuration-button-id-input"
                            class="{{$ctrl.styles.input}}"
                            type="text"
                            ng-model="$ctrl.mainButtonId"
                            field-name="button"
                            field-id="button"
                            disabled="$ctrl.channel.isActive"
                            label="{{'salesforce.configuration.form.buttonId.mainlabel' | translate}}"
                            placeholder="{{'salesforce.configuration.form.buttonId.placeholder' | translate}}"
                            required="true">
                        </blip-input-dpr>

                        <p ng-if="$ctrl.additionalButtonIds.length > 0" class="{{$ctrl.styles.paragraph}} {{$ctrl.styles.labelContainer}} bp-fs-7">
                            <span translate translate-compile>salesforce.configuration.form.buttonId.disclaimer</span>
                        </p>

                        <blip-multiple-input
                            id="salesforce-configuration-button-ids"
                            class="{{$ctrl.styles.multipleInput}}"
                            ng-model="$ctrl.additionalButtonIds"
                            input-label="{{'salesforce.configuration.form.buttonId.label' | translate}}"
                            input-placeholder="{{'salesforce.configuration.form.buttonId.placeholder' | translate}}"
                            add-button-text="{{'salesforce.configuration.form.buttonId.addButton' | translate}}">
                        </blip-multiple-input>

                        <div class="{{$ctrl.styles.checkboxes}}">
                            <bds-checkbox
                                id="create-contact-checkbox"
                                ng-checked="$ctrl.createContact"
                                label="{{'salesforce.configuration.form.createContact' | translate}}"
                                name="create-contact"
                                custom-events="[{ event:'bdsChange', cb: $ctrl.changeCreateContact }]">
                            </bds-checkbox>
                            <bds-checkbox
                                id="create-case-checkbox"
                                ng-checked="$ctrl.createCase"
                                label="{{'salesforce.configuration.form.createCase' | translate}}"
                                name="create-case"
                                custom-events="[{ event:'bdsChange', cb: $ctrl.changeCreateCase }]">
                            </bds-checkbox>
                        </div>

                        <footer class="{{$ctrl.styles.footer}}">
                            <button
                                ng-if="!$ctrl.isActive"
                                id="salesforce-configuration-submit-button"
                                class="bp-btn bp-btn--bot"
                                type="submit"
                                ng-disabled="$ctrl.configurationForm.$invalid"
                                translate>
                                salesforce.configuration.form.connect
                            </button>
                            <button
                                ng-if="$ctrl.isActive"
                                id="salesforce-configuration-submit-button"
                                class="bp-btn bp-btn--bot"
                                type="submit"
                                ng-disabled="$ctrl.configurationForm.$pristine || $ctrl.configurationForm.$invalid"
                                translate>
                                salesforce.configuration.form.save
                            </button>
                        </footer>
                    </form>
                </section>
            </tab>

            <tab id="salesforce-contact-configuration-tab"
                tab-title="{{'salesforce.contacts.title' | translate}}"
                track-click="helpdesk-integrations-salesforce-contacts-opened">
                <section class="{{$ctrl.styles.section}}">
                    <form id="salesforce-contacts-form"
                        class="{{$ctrl.styles.form}}"
                        name="$ctrl.contactsForm"
                        ng-submit="$ctrl.submitContactsForm()"
                        novalidate>
                        <bds-typo tag="h1" variant="fs-20"  bold="semi-bold"  class="{{$ctrl.styles.typoTitle}}" translate>
                            salesforce.contacts.form.title
                        </bds-typo>

                        <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate="">
                            salesforce.contacts.form.description
                        </bds-typo>

                        <div class="mt4">
                            <div ng-repeat="contactEntity in $ctrl.contactEntities track by $index" class="flex flex-row justify-between mb3">
                                <div class="w-33">
                                    <span class="fw6" ng-if="$index === 0">BLiP</span>
                                    <blip-input-dpr
                                        id="salesforce-configuration-api-endpoint-input"
                                        class="{{$ctrl.styles.input}}"
                                        type="text"
                                        ng-model="contactEntity.contactProperty"
                                        disabled="$ctrl.channel.isActive"
                                        field-name="api-endpoint"
                                        field-id="api-endpoint"
                                        label="{{'salesforce.contacts.form.contactProperty.label' | translate}}"
                                        placeholder="{{'salesforce.contacts.form.contactProperty.placeholder' | translate}}"
                                        required="true">
                                    </blip-input-dpr>
                                </div>
                                <div class="{{$ctrl.styles.contactEntityProperty}}">
                                    <span class="fw6" ng-if="$index === 0">Salesforce</span>
                                    <div class="{{$ctrl.styles.contactSalesforceFields}}">
                                        <blip-input-dpr
                                            id="salesforce-configuration-api-endpoint-input"
                                            class="{{$ctrl.styles.input}}"
                                            type="text"
                                            ng-model="contactEntity.entityName"
                                            disabled="$ctrl.channel.isActive"
                                            field-name="api-endpoint"
                                            field-id="api-endpoint"
                                            label="{{'salesforce.contacts.form.entity.label' | translate}}"
                                            placeholder="{{'salesforce.contacts.form.entity.placeholder' | translate}}"
                                            required="true">
                                        </blip-input-dpr>
                                        <blip-input-dpr
                                            id="salesforce-configuration-api-endpoint-input"
                                            class="{{$ctrl.styles.input}}"
                                            type="text"
                                            ng-model="contactEntity.fieldName"
                                            disabled="$ctrl.channel.isActive"
                                            field-name="api-endpoint"
                                            field-id="api-endpoint"
                                            label="{{'salesforce.contacts.form.property.label' | translate}}"
                                            placeholder="{{'salesforce.contacts.form.property.placeholder' | translate}}"
                                            required="true">
                                        </blip-input-dpr>
                                        <bds-icon class="{{$ctrl.styles.actionIcon}}"
                                            name="trash"
                                            theme="outline"
                                            ng-click="$ctrl.deleteContactEntity($index)">
                                        </bds-icon>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <bds-button type="button" icon="add" variant='tertiary'  ng-click="$ctrl.addContactEntity()"
                             translate>salesforce.contacts.form.addbutton</bds-button>
                        </div>

                        <footer class="{{$ctrl.styles.footer}}">
                            <button
                                ng-if="!$ctrl.isActive"
                                id="salesforce-contacts-submit-button"
                                class="bp-btn bp-btn--bot"
                                type="submit"
                                ng-disabled="$ctrl.contactsForm.$invalid"
                                translate>
                                salesforce.contacts.form.connect
                            </button>
                            <button
                                ng-if="$ctrl.isActive"
                                id="salesforce-contacts-submit-button"
                                class="bp-btn bp-btn--bot"
                                type="submit"
                                ng-disabled="$ctrl.contactsForm.$pristine || $ctrl.contactsForm.$invalid"
                                translate>
                                salesforce.contacts.form.save
                            </button>
                        </footer>
                    </form>
                </section>
            </tab>

            <tab id="salesforce-documentation-tab"
                tab-title="{{'salesforce.documentation.title' | translate}}"
                tab-href="https://help.salesforce.com/articleView?id=live_agent_intro_classic.htm&type=5"
                track-click="helpdesk-integrations-salesforce-doc-opened">
            </tab>
        </content-tabs>
    </article>
</div>
