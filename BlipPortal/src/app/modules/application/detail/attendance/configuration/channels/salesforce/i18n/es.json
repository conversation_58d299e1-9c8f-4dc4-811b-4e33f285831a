{"salesforce": {"title": "Salesforce Live Agent", "overview": {"title": "Visión General", "image": {"alt": "Logotipo de Salesforce", "title": "Salesforce"}, "description": {"1": "Comuníquese con sus clientes al instante a través de <a href='https://www.salesforce.com/br/products/service-cloud/features/live-agent/' target='_blank'> Salesforce Live Agent </a>!", "2": "<strong> Recuerde: </strong> al integrar Salesforce Live Agent como canal de atención, no podrá utilizar las funciones de monitoreo e informes de Blip."}}, "configuration": {"title": "Configuración básica", "form": {"title": "Configurar Salesforce Live Agent", "description": {"1": "Para activar la integración, debe <a href='https://www.salesforce.com/br/' target='_blank'> tener una cuenta de Salesforce </a>.", "2": "Para saber dónde obtener la información solicitada a continuación, <a href='https://help.salesforce.com/articleView?id=live_agent_intro_classic.htm&type=5' target='_blank'> consulte la documentación </a>."}, "apiEndpoint": {"label": "Nombre de host del endpoint del chat", "placeholder": "Ex: d.gla5.gus.salesforce.com"}, "organizationId": {"label": "ID de Organización", "placeholder": "Ex: 00D21000000767i"}, "deploymentId": {"label": "ID de Implementación", "placeholder": "Ex: 523B00000005KXz"}, "buttonId": {"mainlabel": "<PERSON> (principal)", "label": "ID de Botón", "placeholder": "Ex: 525C00000004h3m", "disclaimer": "Si agrega más de un botón, debe dirigirlos al área de <a ui-sref=\"auth.application.detail.attendance.rules\"> reglas de atención</a>. <PERSON> lo contrario, toda la atención irá al botón principal.", "addButton": "Agregar botón"}, "createContact": "Crear un contacto para cada nuevo cliente en atención", "createCase": "Crea un nuevo caso para cada atención", "save": "Guardar", "connect": "Conectar"}}, "contacts": {"title": "Propiedades de contacto", "form": {"title": "Propiedades de contacto", "description": "Complete los campos a continuación con las propiedades de contacto utilizadas en su chatbot para que aparezcan en el perfil de un contacto en Salesforce.", "contactProperty": {"label": "Propiedad del contacto", "placeholder": "Ex: <PERSON><PERSON>ame"}, "entity": {"label": "Entidad", "placeholder": "Ex: Contact"}, "property": {"label": "Propriedad", "placeholder": "Ex: Name"}, "addbutton": "+ Agregar propriedad", "save": "Guardar", "connect": "Conectar"}}, "documentation": {"title": "Documentación"}, "success": {"connection": "¡Salesforce conectado con éxito!", "disconnection": "¡Salesforce se desconectó correctamente!"}, "error": {"noConfiguration": "Debe completar toda la información en la pestaña de configuración para realizar esta integración.", "loadConfiguration": "Error al cargar la configuración.", "disconnection": "Error al desconectar Salesforce. <br> Vuelva a intentarlo.", "connection": "Error al conectar Salesforce. <br> Vuelva a intentarlo.", "concurrence": "¡Solo puede tener un canal de atención conectado! <br> Desconecte el otro canal y vuelva a intentarlo."}}}