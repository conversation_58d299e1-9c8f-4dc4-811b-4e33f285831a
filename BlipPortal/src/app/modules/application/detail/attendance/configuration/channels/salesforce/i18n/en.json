{"salesforce": {"title": "Salesforce Live Agent", "overview": {"title": "Overview", "image": {"alt": "Salesforce's logo", "title": "Salesforce"}, "description": {"1": "Using <a href='https://www.salesforce.com/products/service-cloud/features/live-agent/' target='_blank'>Salesforce Live Agent</a>, you can communicate with your customers instantly.", "2": "<strong>Remember:</strong> when integrating with Salesforce Live Agent, you will not be able to use Blip's Monitoring and Reports resources."}}, "configuration": {"title": "Settings", "form": {"title": "Configure your Salesforce Live Agent", "description": {"1": "To activate your integration, you must <a href='https://www.salesforce.com/' target='_blank'>have a Salesforce account</a>.", "2": "To get the required information below, <a href='https://help.salesforce.com/articleView?id=live_agent_intro_classic.htm&type=5' target='_blank'>check the documentation</a>."}, "apiEndpoint": {"label": "Chat endpoint hostname", "placeholder": "Ex: d.gla5.gus.salesforce.com"}, "organizationId": {"label": "Organization ID", "placeholder": "Ex: 00D21000000767i"}, "deploymentId": {"label": "Deployment ID", "placeholder": "Ex: 523B00000005KXz"}, "buttonId": {"mainlabel": "Button ID (main)", "label": "Button ID", "placeholder": "Ex: 525C00000004h3m", "disclaimer": "Adding more than one button, you need to target them with <a ui-sref=\"auth.application.detail.attendance.rules\">rules</a>. Otherwise all tickets will go to the main button.", "addButton": "Add button"}, "createContact": "Create a new contact for every customer on attendance, if it does not exists", "createCase": "Create a new case for every attendance", "save": "Save", "connect": "Connect"}}, "contacts": {"title": "Contact properties", "form": {"title": "Contact properties", "description": "Fill in the fields below with the contact property used in your chatbot to associate with the Salesforce contact profile.", "contactProperty": {"label": "Contact property", "placeholder": "Ex: <PERSON><PERSON>ame"}, "entity": {"label": "Entity", "placeholder": "Ex: Contact"}, "property": {"label": "Property", "placeholder": "Ex: Name"}, "addbutton": "+ Add property", "save": "Save", "connect": "Connect"}}, "documentation": {"title": "Documentation"}, "success": {"connection": "Salesforce connected successfully!", "disconnection": "Salesforce disconnected successfully!"}, "error": {"noConfiguration": "You must fill all fields in the Settings tab in order to integrate Salesforce.", "loadConfiguration": "Error while loading settings.", "disconnection": "Error while disconnecting Salesforce.<br>Please, try again.", "connection": "Error while connecting Salesforce.<br>Please, try again.", "concurrence": "You can only have one customer service channel connected!<br>Please disconnect from the other channel and try again."}}}