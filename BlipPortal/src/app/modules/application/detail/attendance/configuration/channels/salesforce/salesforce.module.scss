@import '~assets/scss/main';

.paragraph {
    width: 100%;
    margin: 0;
    font-size: 14px;
    line-height: $bp-lh-plus;
    color: $color-content-default;
}
.labelContainer {
    max-width: 40*$m;
}

.section {
    padding: 1*$m 0;
}

.typoLegend{
    margin-bottom: 10px;
}

.typoTitle {
    display: block;
    color: $color-content-default;
    margin-bottom: 1.875rem;
 }

:global(#salesforce-overview-tab) {
    .section {
        display: flex;
        align-items: center;
    }

    .logo {
        width: 11.7*$m;
        height: 8.5$m;

        flex-shrink: 0;

        margin-right: 4*$m;
    }
}

:global(#salesforce-configuration-tab) {
    .form {
        margin: 0;

        .paragraph {
            margin-bottom: 1*$m;
        }

        .input {
            width: 100%;
            max-width: 40*$m;

            display: block;

            margin-bottom: 1*$m;
        }

        .checkboxes{
            display: inline-grid;
            color: $color-content-default;
        }

        .multipleInput {
            width: 100%;
            max-width: 43*$m;

            display: block;

            margin-bottom: 1*$m;
        }

        .footer {
            margin-top: 2*$m;
            width: 100%;
            text-align: right;
            justify-content: flex-end;
            border-top: none;
        }
    }
}

:global(#salesforce-contact-configuration-tab) {
    .contactEntityProperty{
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        margin-left: $m;
    }
    .contactSalesforceFields {
        display: flex;
        flex-direction: row;

        * {
            width: 100%;
            margin-right: 0.5 * $m;
        }
    }

    .footer {
        margin-top: 2*$m;
        padding-top: $m;
        width: 100%;
        height: auto;
        text-align: right;
        justify-content: flex-end;
        border-top: none;
    }

    .actionIcon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        align-self: center;
    }

    span{
        color: $color-content-default;
    }    
}
