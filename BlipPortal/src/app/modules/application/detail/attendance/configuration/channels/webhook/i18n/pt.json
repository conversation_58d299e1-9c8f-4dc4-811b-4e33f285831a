{"deskWebhookProvider": {"title": "Canal Personalizado", "overview": {"title": "Visão Geral", "image": {"alt": "Logo do Canal Personalizado", "title": "Canal Personalizado"}, "description": "Siga os passos da área de configurações para integrar a ferramenta de atendimento humano de sua preferência e agilize a comunicação com os seus clientes."}, "configuration": {"title": "Configurações", "subtitle": "Configurações básicas", "form": {"description": {"1": "<PERSON><PERSON><PERSON> as informações abaixo para encaminhar seus atendimentos para aplicação externa.", "2": "Para mais informações, <a href='https://help.blip.ai/hc/pt-br/articles/360058709894' target='_blank'>consulte a nossa documentação</a>."}, "apiEndpoint": {"label": "URL de Integração", "placeholder": "Ex: https://d.gla5.gut.webhook.com"}, "authenticationToken": {"label": "Token de Autenticação (opcional)", "placeholder": "Ex: 00D21000000767i"}, "teams": {"useBlipRules": "Utilizar regras de atendimento do Blip", "label": "Nome da fila", "placeholder": "Ex: Fila 1", "addButton": "+ <PERSON><PERSON><PERSON><PERSON> fila"}, "save": "<PERSON><PERSON>", "connect": "Conectar"}}, "documentation": {"title": "Documentação", "link": "https://help.blip.ai/hc/pt-br/articles/360058709894"}, "success": {"connection": "Webhook conectado com sucesso!", "disconnection": "Webhook desconectado com sucesso!"}, "error": {"noTeams": "Você deve inserir pelo menos uma fila se optar por usar as regras do Blip", "noConfiguration": "Você deve preencher todos os campos na guia configurações para integrar o provedor Webhook.", "loadConfiguration": "<PERSON><PERSON> ao carregar as configura<PERSON><PERSON><PERSON>.", "disconnection": "Erro ao desconectar o Webhook.<br>Por favor, tente novamente.", "connection": "Erro ao conectar o Webhook.<br>Por favor, tente novamente.", "concurrence": "Você pode ter apenas um canal de atendimento ao cliente conectado!<br>Desconecte do outro canal e tente novamente."}}}