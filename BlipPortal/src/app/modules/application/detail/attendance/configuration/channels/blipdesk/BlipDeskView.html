<page-header id="blip-desk-channel-header" back-button="auth.application.detail.attendance.channels" page-title="{{'blipdesk.title' | translate}}">
    <custom-content>
        <switch id="blip-desk-channel-switch" on-toggle="$ctrl.onSwitch(value)" ng-permission="true" ng-model="$ctrl.isActive">{{'utils.forms.activate' | translate}}</switch>
    </custom-content>
</page-header>
<div blip-desk-blip-desk-channel-page" class="container">
    <article class="bp-card bp-card--left-arrow">
        <content-tabs>
            <tab id="blip-desk-overview-tab" tab-title="{{'utils.misc.overview' | translate}}">
                <section class="{{$ctrl.styles.section}}">
                    <bds-illustration class="{{$ctrl.styles.logo}}"
                        type="brand"
                        name="blip-desk-blue-black-horizontal"
                        alt="{{ 'blipdesk.overview.image.alt' | translate }}"
                        title="{{ 'blipdesk.overview.image.title' | translate }}">
                    </bds-illustration>
                    <bds-typo tag="p" variant="fs-14" class="{{$ctrl.styles.paragraph}}"
                        translate="blipdesk.overview.description"
                        translate-values="{ blipDeskUrl: $ctrl.blipDeskUrl }">
                    </bds-typo>
                </section>
            </tab>
        </content-tabs>
    </article>
</div>
