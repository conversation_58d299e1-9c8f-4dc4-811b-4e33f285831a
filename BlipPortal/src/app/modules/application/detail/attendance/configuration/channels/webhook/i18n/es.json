{"deskWebhookProvider": {"title": "Canal personalizado", "overview": {"title": "Visión General", "image": {"alt": "Logotipo de canal personalizado", "title": "Canal personalizado"}, "description": "Siga los pasos en el área de configuración para integrar la herramienta de atención humana de su elección y agilizar la comunicación con sus clientes."}, "configuration": {"title": "Configuración", "subtitle": "Configuración básica", "form": {"description": {"1": "Complete la siguiente información para reenviar sus atenciones a una aplicación externa.", "2": "Para obtener más información, <a href='https://help.blip.ai/hc/es-mx/articles/360058709894' target='_blank'> vea nuestra documentación </a>."}, "apiEndpoint": {"label": "URL de Integración", "placeholder": "Ex: https://d.gla5.gut.webhook.com"}, "authenticationToken": {"label": "Token de autenticación (opcional)", "placeholder": "Ex: 00D21000000767i"}, "teams": {"useBlipRules": "Utilizar las reglas de atención Blip", "label": "Nombre de la cola", "placeholder": "Ex: Cola 1", "addButton": "+ Agregar cola"}, "save": "Guardar", "connect": "Conectar"}}, "documentation": {"title": "Documentación", "link": "https://help.blip.ai/hc/es-mx/articles/360058709894"}, "success": {"connection": "¡Webhook conectado correctamente!", "disconnection": "¡Webhook desconectado correctamente!"}, "error": {"noTeams": "Debe ingresar al menos una cola si elige usar las reglas de Blip", "noConfiguration": "Debe completar todos los campos en la pestaña de configuración para integrar el proveedor de Webhook.", "loadConfiguration": "Error al cargar la configuración.", "disconnection": "Error al desconectar con Webhook. <br> Vuelva a intentarlo.", "connection": "Error al conectar con Webhook. <br> Vuelva a intentarlo.", "concurrence": "¡Solo puede tener un canal de atención al cliente conectado! <br> Desconéctese del otro canal y vuelva a intentarlo."}}}