import { IController, ILocationService } from 'angular';
import { ConfigurationsService } from 'modules/application/misc/ConfigurationsService';
import { ExternalBlipStoreService } from 'modules/blipStore/ExternalBlipStoreService';
import { IStateService } from 'angular-ui-router';

export const DESK_OWNER_IDENTITY = 'postmaster@desk';
export const BLIPDESK_ACTIVE = 'Lime.IsActive';
export const SALESFORCE_ACTIVE = 'Salesforce';
export const DEFAULT_PROVIDER = 'DefaultProvider';

import * as styles from './configuration.module.scss';

import { AttendanceFeatures } from 'modules/application/detail/attendance/AttendanceFeatures';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { IToggleable } from 'feature-toggle-client';
import { LoadingService } from 'modules/ui/LoadingService';
import { Application } from 'modules/shared/ApplicationTypings';
import { blipStoreStateExtensionsName } from 'modules/blipStore';
import { blipStoreStateExtensionsDetail } from 'modules/blipStore';
import { BlipStoreFeatures } from 'modules/blipStore/BlipStoreFeatures';
import TranslateService from 'modules/translate/TranslateService';
import { Extension } from 'modules/blipStore/resources/ExternalBlipStoreResource';

const CUSTOMER_SERVICE_STATES = {
    'Desk': {
        auth: 'auth.application.detail.attendance.channels.blipdesk',
        trackClick: 'helpdesk-integrations-blipdesk-opened'
    },
    'Salesforce': {
        auth: 'auth.application.detail.attendance.channels.salesforce',
        trackClick: 'helpdesk-integrations-salesforce-opened'
    },
    'Webhook': {
        auth: 'auth.application.detail.attendance.channels.webhook',
        trackClick: 'helpdesk-integrations-webhook-opened'
    },
};

const BLIP_STORE_INDICATED_VIEW_ALL_CLICKED_TRACKING = 'helpdesk-indicated-view-all-clicked';
const BLIP_STORE_INDICATED_EXTENSION_CLICKED_TRACKING = 'helpdesk-indicated-extension-clicked';
const BLIP_STORE_PARAMETER_HELPDESK_JOURNEY = 'HelpdesksCustomerServiceChannels';

export class ConfigurationController implements IController, IToggleable {
    public defaultProvider: string;
    public styles: any;
    private blipStoreStateExtensionsName: string = blipStoreStateExtensionsName;
    private blipStoreStateExtensionsDetail: string = blipStoreStateExtensionsDetail;

    public isDeskSalesforceIntegrationEnabled: boolean;
    public isDeskWebhookIntegrationEnabled: boolean;
    public isBlipStoreBannerServicePageEnabled: boolean;
    public isConfigurationsApplicationsDomainUsageGetEnabled: boolean;
    public extensions: Extension[];

    constructor(
        private application: Application,
        private ConfigurationsService: ConfigurationsService,
        private ExternalBlipStoreService: ExternalBlipStoreService,
        private $state: IStateService,
        private MESSAGINGHUBDOMAIN: string,
        private SegmentService: SegmentService,
        private LoadingService: LoadingService,
        private $location: ILocationService,
        private TranslateService: TranslateService,
    ) { }

    async $onInit() {
        await this.checkFeatures();

        this.styles = styles;
        this.loadConfiguration();
        this.SegmentService.createBotTrack('helpdesk-integrations-opened', this.application);
    }

    async checkFeatures() {
        this.isDeskSalesforceIntegrationEnabled = await AttendanceFeatures.isDeskSalesforceIntegrationEnabled();
        this.isDeskWebhookIntegrationEnabled = await AttendanceFeatures.isDeskWebhookIntegrationEnabled();
        this.isBlipStoreBannerServicePageEnabled = await BlipStoreFeatures.isBlipStoreBannerServicePageEnabled();
        this.isConfigurationsApplicationsDomainUsageGetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsGetEnabled();
    }

    async loadConfiguration() {
        this.LoadingService.startLoading();
        try {
            const language = await this.TranslateService.getCurrentLanguage();

            if (this.isBlipStoreBannerServicePageEnabled === true) {
                await this.GetExtensionsByParameter(language);
            }

            const settings = await this.ConfigurationsService.get(
                `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
                undefined,
                this.isConfigurationsApplicationsDomainUsageGetEnabled
            );
            this.defaultProvider = settings[DEFAULT_PROVIDER];
        } catch (e) {
            this.defaultProvider = undefined;
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    goToStateAndTrack(state) {
        const customerServiceState = CUSTOMER_SERVICE_STATES[state];
        if (!customerServiceState) {
            return;
        }
        this.SegmentService.createBotTrack(
            customerServiceState.trackClick,
            this.application
        );

        this.$state.go(customerServiceState.auth);
    }

    onOpenBlipStoreExtensionDetail(extensionId: string) {
        this.SegmentService.createTrack(BLIP_STORE_INDICATED_EXTENSION_CLICKED_TRACKING, {
            extensionName: extensionId,
        });
        if (this.$location.path() !== '/blip-store') {
            this.$state.go(this.blipStoreStateExtensionsDetail, { id: extensionId });
            window.dispatchEvent(new CustomEvent('mfe-route-change', { detail: '/blip-store' }));
        }
    }

    onOpenBlipStore() {
        this.SegmentService.createTrack(BLIP_STORE_INDICATED_VIEW_ALL_CLICKED_TRACKING);

        if (this.$location.path() !== '/blip-store') {
            this.$state.go(this.blipStoreStateExtensionsName, { collection: 'HighlightEvolveTheBlipDeskExperiencePlugins' });
            window.dispatchEvent(new CustomEvent('mfe-route-change', { detail: '/blip-store' }));
        }
    }

    async GetExtensionsByParameter(language: string): Promise<void> {
        this.extensions = await this.ExternalBlipStoreService.GetExtensionsByParameter(this.application.tenantId, language, BLIP_STORE_PARAMETER_HELPDESK_JOURNEY);
    }
}
