import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IFormController } from 'angular';
import { LoadingService } from 'modules/ui/LoadingService';
import {
    DESK_OWNER_IDENTITY,
    DEFAULT_PROVIDER,
} from 'modules/application/detail/attendance/configuration/ConfigurationController';
import { ConfigurationsService } from 'modules/application/misc/ConfigurationsService';
import { SegmentService, SUCCESS_STATUS, FAILURE_STATUS } from 'modules/application/misc/SegmentService';

import * as styles from './deskwebhook.module.scss';
import { ToastService, ToastType } from 'modules/application/misc/ToastService';
import { DeskConfigurationService } from '../../../DeskConfigurationService';
import { Application } from 'modules/shared/ApplicationTypings';
import { AttendanceFeatures } from 'modules/application/detail/attendance/AttendanceFeatures';

export const Webhook = 'Webhook';

export class WebhookController implements IController {
    public isActive: boolean;
    public styles: any;
    public configurationForm: IFormController;

    public configuration: Object;
    public hasBlipTeams: boolean;
    public blipTeams: string[];
    public isConfigurationsApplicationsDomainUsageGetEnabled: boolean;
    public isConfigurationsApplicationsDomainUsageSetEnabled: boolean;

    constructor(
        private application: Application,
        private LoadingService: LoadingService,
        private ConfigurationsService: ConfigurationsService,
        private DeskConfigurationService: DeskConfigurationService,
        private ToastService: ToastService,
        private SegmentService: SegmentService,
        private MESSAGINGHUBDOMAIN: string
    ) { }

    async $onInit() {
        await this.checkFeatures();
        this.styles = styles;

        this.loadConfiguration();
    }

    async checkFeatures() {
        this.isConfigurationsApplicationsDomainUsageGetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsGetEnabled();
        this.isConfigurationsApplicationsDomainUsageSetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsSetEnabled();
    }

    /**
     * Load bot's customer service channel configuration
     */
    private async loadConfiguration(): Promise<void> {
        try {
            this.LoadingService.startLoading();
            const configuration = await this.ConfigurationsService.get(
                `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
                undefined,
                this.isConfigurationsApplicationsDomainUsageGetEnabled
            );
            this.configuration = { ...configuration };
            this.isActive =
                this.configuration
                && this.configuration[DEFAULT_PROVIDER] === Webhook;

            this.hasBlipTeams =
                this.configuration['Webhook.HasBlipTeams'] &&
                this.configuration['Webhook.HasBlipTeams'].toLowerCase() === 'true';

            if (this.configuration['Webhook.BlipTeams']) {
                this.blipTeams = this.configuration['Webhook.BlipTeams'].split(',');
            } else {
                this.blipTeams = [''];
            }
        } catch (e) {
            if (e.code !== 67) {
                this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.loadConfiguration');
            } else {
                this.configuration = {};
                this.isActive = false;
                this.hasBlipTeams = false;
                this.blipTeams = [''];
            }
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Check if another customer service channel is connected
     *
     * @returns whether a channel other than Webhook is connected or not
     */
    private isOtherChannelConnected(): boolean {
        return this.configuration
            && this.configuration[DEFAULT_PROVIDER]
            && this.configuration[DEFAULT_PROVIDER] !== Webhook;
    }

    /**
     * Submit Configuration tab form
     */
    public submitConfigurationForm(): void {
        if (this.isOtherChannelConnected()) {
            this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.concurrence');
            return;
        }

        this.configuration['Webhook.HasBlipTeams'] = this.hasBlipTeams;
        const validTeams = this.blipTeams?.filter(t => t);
        if (this.hasBlipTeams && validTeams.length > 0) {
            this.configuration['Webhook.BlipTeams'] = validTeams.join(',');
        } else if (this.hasBlipTeams) {
            this.configurationForm.$invalid = true;
            this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.noTeams');
            return;
        }

        this.activateIntegration();
    }

    /**
     * If no other channel is connected, save configuration and activates Webhook
     */
    private async activateIntegration(): Promise<void> {
        if (this.isOtherChannelConnected()) {
            this.isActive = false;
            this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.concurrence');
            return;
        }

        if (this.configurationForm.$invalid) {
            this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.noConfiguration');
            return;
        }

        try {
            this.LoadingService.startLoading();
            this.configuration[DEFAULT_PROVIDER] = Webhook;
            await this.saveConfiguration();
            this.isActive = true;
            this.setBlockToolsCache(true);
            this.registerSuccessfulConnection();
        } catch (e) {
            this.isActive = false;
            this.registerFailedConnection();
        } finally {
            this.configurationForm.$setPristine();
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Delete configuration default customer service channel
     */
    private async deleteIntegration(): Promise<void> {
        try {
            this.LoadingService.startLoading();
            await this.deleteConfiguration();
            this.isActive = false;
            this.setBlockToolsCache(false);
            this.registerSuccessfulDisconnection();
        } catch (e) {
            this.isActive = true;
            this.registerFailedDisconnection();
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Delete configuration's default provider
     */
    private async deleteConfiguration(): Promise<void> {
        await this.ConfigurationsService.deleteSingle(
            `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
            DEFAULT_PROVIDER,
            undefined,
            this.isConfigurationsApplicationsDomainUsageSetEnabled
        );
    }

    /**
     * Set shouldBlockTools' cache value
     *
     * @param value - new boolean value for cache
     */
    private async setBlockToolsCache(value: boolean): Promise<void> {
        try {
            await this.DeskConfigurationService.setBlockToolsCache(this.application, value);
        } catch (e) {}
    }

    /**
     * On switch trigger, activate or delete Webhook integration
     *
     * @param value - new boolean value for cache
     */
    public onSwitch(value): void {
        if (value === undefined) {
            return;
        }

        if (value) {
            this.activateIntegration();
        } else {
            this.deleteIntegration();
        }
    }

    /**
     * Save bot's customer service configuration
     */
    private async saveConfiguration(): Promise<void> {
        await this.ConfigurationsService.set(
            `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
            this.configuration,
            undefined,
            this.isConfigurationsApplicationsDomainUsageSetEnabled
        );
    }

    /**
     * Register successful connection, with toast and segment events
     */
    private registerSuccessfulConnection(): void {
        try {
            this.ToastService.toast(ToastType.Success, 'deskWebhookProvider.success.connection');
            this.SegmentService.createBotTrack('helpdesk-integrations-webhook-connected', this.application, { status: SUCCESS_STATUS });
            this.SegmentService.setAccount({ customerServiceChannel: Webhook });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register failed connection, with toast and segment events
     */
    private registerFailedConnection(): void {
        try {
            this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.connection');
            this.SegmentService.createBotTrack('helpdesk-integrations-Webhook-connected', this.application, { status: FAILURE_STATUS });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register successful disconnection, with toast and segment events
     */
    private registerSuccessfulDisconnection(): void {
        try {
            this.ToastService.toast(ToastType.Success, 'deskWebhookProvider.success.disconnection');
            this.SegmentService.createBotTrack('helpdesk-integrations-webhook-disabled', this.application, { status: SUCCESS_STATUS });
            this.SegmentService.setAccount({ customerServiceChannel: 'None' });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register failed disconnection, with toast and segment events
     */
    private registerFailedDisconnection(): void {
        try {
            this.ToastService.toast(ToastType.Danger, 'deskWebhookProvider.error.disconnection');
            this.SegmentService.createBotTrack('helpdesk-integrations-webhook-disabled', this.application, { status: FAILURE_STATUS });
        } catch (e) {} //Not necessary error handling
    }

    public setFormUnpristine(): void {
        this.configurationForm.$pristine = false;
    }
}
