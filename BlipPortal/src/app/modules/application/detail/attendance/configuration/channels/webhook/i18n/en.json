{"deskWebhookProvider": {"title": "Custom Channel", "overview": {"title": "Overview", "image": {"alt": "Custom Channel's logo", "title": "Custom Channel"}, "description": "Follow the steps in the Settings page to integrate the human service tool of your choice and streamline communication with your customers."}, "configuration": {"title": "Settings", "subtitle": "Basic Settings", "form": {"description": {"1": "Fill in the information below to forward your calls for external application.", "2": "For more information, <a href='https://help.blip.ai/hc/en-us/articles/360058709894' target='_blank'>check the documentation</a>."}, "apiEndpoint": {"label": "Integration URL", "placeholder": "Ex: https://d.gla5.gut.webhook.com"}, "authenticationToken": {"label": "Authentication Token (optional)", "placeholder": "Ex: 00D21000000767i"}, "teams": {"useBlipRules": "Use Blip attendance rules", "label": "Line Name", "placeholder": "Ex: Line 1", "addButton": "+ Add line"}, "save": "Save", "connect": "Connect"}}, "documentation": {"title": "Documentation", "link": "https://help.blip.ai/hc/en-us/articles/360058709894"}, "success": {"connection": "Webhook connected successfully!", "disconnection": "Webhook disconnected successfully!"}, "error": {"noTeams": "You must enter at least one line if you choose to use Blip Rules", "noConfiguration": "You must fill all fields in the Settings tab in order to integrate Webhook provider.", "loadConfiguration": "Error while loading settings.", "disconnection": "Error while disconnecting Webhook.<br>Please, try again.", "connection": "Error while connecting Webhook.<br>Please, try again.", "concurrence": "You can only have one customer service channel connected!<br>Please disconnect from the other channel and try again."}}}