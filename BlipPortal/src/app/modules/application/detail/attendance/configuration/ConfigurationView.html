<page-header id="helpdesk-integrations-header" page-title="{{'attendance-channels.title' | translate}}">
</page-header>

<div id="helpdesk-integrations-page" class="container">
    <div class="row">
        <div class="twelve columns">
            <div class="channels-list">
                <div class="channel-item tc">
                    <card id="blip-desk-card" ng-click="$ctrl.goToStateAndTrack('Desk')" class="card card--with-hover card--square">
                        <bds-illustration id="{{$ctrl.styles.blipDeskLogo}}"
                            class="{{$ctrl.styles.channelLogo}}"
                            type="brand"
                            name="blip-desk-blue-black-horizontal"
                            alt="{{ 'attendance-channels.blipdesk.image.alt' | translate }}"
                            title="{{ 'attendance-channels.blipdesk.image.title' | translate }}">
                        </bds-illustration>
                        <bds-typo tag="h4" variant="fs-16" class="{{$ctrl.styles.typoTitle}}" translate>
                            attendance-channels.blipdesk.title
                        </bds-typo>
                        <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.typoLegend}}" translate>
                            attendance-channels.blipdesk.subtitle
                        </bds-typo>
                        <card-footer class="mt2">
                            <button ui-sref="auth.application.detail.attendance.channels.blipdesk"
                                class="button-hub"
                                ng-class="{ 'connected': $ctrl.defaultProvider === 'Lime' }">
                                    {{
                                        ($ctrl.defaultProvider === 'Lime'
                                            ? 'utils.forms.connected'
                                            : 'utils.forms.connect')
                                        | translate
                                    }}
                            </button>
                        </card-footer>
                    </card>
                </div>

                <div class="channel-item tc" ng-if="$ctrl.isDeskSalesforceIntegrationEnabled === true">
                    <card id="salesforce-card" ng-click="$ctrl.goToStateAndTrack('Salesforce')" class="card card--with-hover card--square">
                        <img id="{{$ctrl.styles.salesforceLogo}}"
                            class="{{$ctrl.styles.channelLogo}}"
                            src="/assets/img/desk/providers/salesforce.svg"
                            alt="{{ 'attendance-channels.salesforce.image.alt' | translate }}"
                            title="{{ 'attendance-channels.salesforce.image.title' | translate }}"
                        />
                        <bds-typo tag="h4" variant="fs-16" class="{{$ctrl.styles.typoTitle}}" translate>
                            attendance-channels.salesforce.title
                        </bds-typo>
                        <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.typoLegend}}" translate>
                            attendance-channels.salesforce.subtitle
                        </bds-typo>
                        <card-footer class="mt2">
                            <button ui-sref="auth.application.detail.attendance.channels.salesforce"
                                class="button-hub"
                                ng-class="{ 'connected': $ctrl.defaultProvider === 'Salesforce' }">
                                    {{
                                        ($ctrl.defaultProvider === 'Salesforce'
                                            ? 'utils.forms.connected'
                                            : 'utils.forms.connect')
                                        | translate
                                    }}
                            </button>
                        </card-footer>
                    </card>
                </div>

                <div class="channel-item tc" ng-if="$ctrl.isDeskWebhookIntegrationEnabled === true">
                    <card id="webhook-card" ng-click="$ctrl.goToStateAndTrack('Webhook')" class="card card--with-hover card--square">
                        <img id="{{$ctrl.styles.webhookLogo}}"
                            class="{{$ctrl.styles.channelLogo}}"
                            src="/assets/img/desk/providers/custom.svg"
                            alt="{{ 'attendance-channels.webhook.image.alt' | translate }}"
                            title="{{ 'attendance-channels.webhook.image.title' | translate }}"
                        />
                        <bds-typo tag="h4" variant="fs-16" class="{{$ctrl.styles.typoTitle}}" translate>
                            attendance-channels.webhook.title
                        </bds-typo>
                        <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.typoLegend}}" translate>
                            attendance-channels.webhook.subtitle
                        </bds-typo>
                        <card-footer class="mt2">
                            <button
                                class="button-hub"
                                ng-class="{ 'connected': $ctrl.defaultProvider === 'Webhook' }">
                                    {{
                                        ($ctrl.defaultProvider === 'Webhook'
                                            ? 'utils.forms.connected'
                                            : 'utils.forms.connect')
                                        | translate
                                    }}
                            </button>
                        </card-footer>
                    </card>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row" ng-if="$ctrl.isBlipStoreBannerServicePageEnabled === true && $ctrl.extensions.length > 0">
        <div class="{{$ctrl.styles.horizontalLine}}"></div>
        <div class="twelve columns">
            <div class="{{$ctrl.styles.ExtensionsTitleSubTitleBox}}">
                <div>
                    <div class="{{$ctrl.styles.ExtensionsTitleBox}}">
                        <bds-typo tag="h3" variant="fs-24" bold="bold" translate>
                            attendance-channels.blipStore.title
                        </bds-typo>
                     </div>
                     <div class="{{$ctrl.styles.ExtensionsButtonBox}}">
                        <div class="{{$ctrl.styles.ExtensionsButton}}">
                            <bds-button size="standard" variant="tertiary" ng-click="$ctrl.onOpenBlipStore()" type="button" arrow="{true}" translate>
                                attendance-channels.blipStore.buttonTitle
                            </bds-button>
                        </div>   
                    </div>
                </div>
                <div class="{{$ctrl.styles.ExtensionsTitleDescriptionBox}}">    
                    <bds-typo variant="fs-14" translate>
                        attendance-channels.blipStore.description
                    </bds-typo>
                </div>
            </div>
        </div>
        <div class="twelve columns">
            <div class="{{$ctrl.styles.ExtensionsCardsList}}">
                <div class="{{$ctrl.styles.ExtensionsItem}}" ng-repeat="extension in $ctrl.extensions">
                    <div class="{{$ctrl.styles.ExtensionCardContainer}}" ng-click="$ctrl.onOpenBlipStoreExtensionDetail(extension.id)">
                        <div class="{{$ctrl.styles.ExtensionCardTopContainer}}">
                            <div class="{{$ctrl.styles.ExtensionCardImageBox}}">
                                <img class="{{$ctrl.styles.ExtensionCardImage}}"
                                    src="{{extension.icon}}"
                                    alt=""
                                    title=""/>
                            </div>
                            <div class="{{$ctrl.styles.ExtensionCardTitleContainer}}">
                                <bds-typo variant="fs-16" class="{{$ctrl.styles.ExtensionCardTitle}}" bold="bold" translate>
                                    {{ extension.name }}
                                </bds-typo>
                                <div class="{{$ctrl.styles.ExtensionCardSubTitle}}">
                                    <div class="{{$ctrl.styles.ExtensionCardAuthor}}">
                                        <bds-typo variant="fs-12" translate>
                                            attendance-channels.blipStore.cardSubTitle
                                        </bds-typo>
                                    </div>
                                    <div>
                                        <bds-typo variant="fs-12" translate>
                                            {{extension.author.name}} 
                                        </bds-typo>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="{{$ctrl.styles.ExtensionCardBody}}">
                            <bds-typo variant="fs-14" class="{{$ctrl.styles.ExtensionCardBodyDescription}}" translate>
                                {{ extension.overview }}
                            </bds-typo>
                        </div>
                        <div class="extension-card-footer">
                            <div class="extension-card-footer-icon">
                                <bds-icon class="color-primary" name="clock" size="medium"></bds-icon>
                            </div>
                            <div class="extension-card-footer-text">
                                <bds-typo variant="fs-14" bold="bold" translate>
                                    {{ extension.isPaid ? ('attendance-channels.blipStore.buttonCardTitlePaid' | translate) : ('attendance-channels.blipStore.buttonCardTitleFree' | translate) }}
                                </bds-typo>
                            </div>
                        </div>
                    </div>
                </div> 
            </div> 
        </div>       
    </div>
</div>