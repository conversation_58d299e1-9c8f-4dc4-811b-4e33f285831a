{"salesforce": {"title": "Salesforce Live Agent", "overview": {"title": "Visão Geral", "image": {"alt": "Logo da Salesforce", "title": "Salesforce"}, "description": {"1": "Comunique-se com seus clientes instantaneamente através do <a href='https://www.salesforce.com/br/products/service-cloud/features/live-agent/' target='_blank'>Salesforce Live Agent</a>!", "2": "<strong>Lembre-se:</strong> ao integrar o Salesforce Live Agent como canal de atendimento, não será possível utilizar os recursos de Monitoramento e Relatórios do Blip."}}, "configuration": {"title": "Configurações básicas", "form": {"title": "Configurar o Salesforce Live Agent", "description": {"1": "Para ativar a integração, você precisa <a href='https://www.salesforce.com/br/' target='_blank'>ter uma conta Salesforce</a>.", "2": "Para saber onde retirar as informações solicitadas abaixo, <a href='https://help.salesforce.com/articleView?id=live_agent_intro_classic.htm&type=5' target='_blank'>consulte a documentação</a>."}, "apiEndpoint": {"label": "Hostname do endpoint do chat", "placeholder": "Ex: d.gla5.gus.salesforce.com"}, "organizationId": {"label": "ID da Organização", "placeholder": "Ex: 00D21000000767i"}, "deploymentId": {"label": "ID de Implementação", "placeholder": "Ex: 523B00000005KXz"}, "buttonId": {"mainlabel": "<PERSON> (principal)", "label": "ID do Botão", "placeholder": "Ex: 525C00000004h3m", "disclaimer": "Adicionando mais de um button, você precisa direcioná-los na área de <a ui-sref=\"auth.application.detail.attendance.rules\">regras de atendimento</a>. Caso contrário todo atendimento irá para o button principal.", "addButton": "+ <PERSON><PERSON><PERSON>r button"}, "createContact": "Criar um contato para cada novo cliente em atendimento", "createCase": "Criar um novo caso para cada atendimento", "save": "<PERSON><PERSON>", "connect": "Conectar"}}, "contacts": {"title": "Propriedades do contato", "form": {"title": "Propriedades do contato", "description": "Preencha os campos abaixo com as propriedades de contato utilizadas no seu chatbot para que elas apareçam no perfil de um contato no Salesforce.", "contactProperty": {"label": "Propriedade do contato", "placeholder": "Ex: <PERSON><PERSON>ame"}, "entity": {"label": "Entidade", "placeholder": "Ex: Contact"}, "property": {"label": "<PERSON><PERSON><PERSON><PERSON>", "placeholder": "Ex: Name"}, "addbutton": "<PERSON><PERSON><PERSON><PERSON> propried<PERSON>", "save": "<PERSON><PERSON>", "connect": "Conectar"}}, "documentation": {"title": "Documentação"}, "success": {"connection": "Salesforce conectado com sucesso!", "disconnection": "Salesforce desconectado com sucesso!"}, "error": {"noConfiguration": "Você precisa preencher todas as informações na aba de configurações para realizar esta integração.", "loadConfiguration": "Erro ao carregar a configuração.", "disconnection": "Erro ao desconectar Salesforce.<br>Por favor, tente novamente.", "connection": "Erro ao conectar Salesforce.<br>Por favor, tente novamente.", "concurrence": "Você só pode ter um canal de atendimento conectado!<br>Desconecte o outro canal e tente novamente."}}}