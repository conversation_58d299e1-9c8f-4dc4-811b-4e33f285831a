@import '~assets/scss/main';

bds-illustration#blipDeskLogo {
    display: inline-block;
    width: 140px;
    height: 45px;
}

img#salesforceLogo {
    width: 6.2*$m;
    height: 4.5*$m;
}

.typoLegend {
    color: $color-content-disable;
}

.typoTitle {
   color: $color-content-default;
}

$grid-spacing: 20px;

.horizontalLine{
    max-width: 48.125rem;
    padding: 0.625rem 0rem 0.625rem 0rem;
    border-top: 1px solid $color-surface-3;
}

.ExtensionCardContainer {
    display: inline-flex;
    flex-direction: column;
    position: static;
    background-color: $color-surface-1;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    transition: box-shadow 0.15s ease;
    min-height: 235px;
    width: 246px;

    &:hover {
        opacity: 1 !important;
        box-shadow:
            0 4px 4px rgb(219, 227, 239),
            0 16px 16px rgb(222, 228, 240),
            0 32px 20px rgb(228, 234, 241);
        transform: translate(0, -4px);
    }
}

.ExtensionsTitleSubTitleBox {
    margin-bottom: 20px;
    max-width: 770px;
}

.ExtensionsTitleBox {
    display: inline-block;
    width: 640px;
}

.ExtensionsTitleDescriptionBox {
    margin-top: -10px;
}

.ExtensionsButtonBox {
    display: inline-block;
    padding-bottom: 10px;
    width: 125px;;
}

.ExtensionsButton {
    display: flex;
    justify-content: flex-end;
}

.ExtensionCardTitle {
    justify-content: space-between;
}

.ExtensionCardImage {
    display: block;
    height: 3.125rem;
    width: 3.125rem;
    border-radius: 4px;
    object-fit: contain;
}

.ExtensionCardTopContainer {
    display: flex;
    flex-direction: row;
    margin: 1.2rem 0.75rem 0.5rem 1.5rem;
}

.ExtensionCardImageBox {
    border: 1px solid $color-surface-3;
    border-radius: 8px;
    padding: 0.25rem;
    max-height: 3.5rem;
}

.ExtensionCardTitleContainer {
    display: flex;
    flex-direction: column;
    margin-left: 0.75rem;
    align-items: flex-start;
    justify-content: center;
    width: 120px;;
}

.ExtensionCardTitle {
    color: $color-content-default;
}

.ExtensionCardSubTitle {
    color: $color-content-disable;
    display: inline-flex;
}

.ExtensionCardAuthor {
    margin-right: 0.188rem;
}

.ExtensionCardBody {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    justify-content: space-between;
}

.ExtensionCardBodyDescription {
    margin: 0rem 1.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
    color: $color-content-disable;
}

.ExtensionCardFooter
{
    margin: 0 auto;
    margin-bottom: 10px;
}

.ExtensionsCardsList {
    @include flex-box;
    align-items: stretch;
    flex-wrap: wrap;
    margin-top: 0;
    margin-left: -8px;
    margin-right: -$grid-spacing;

    .ExtensionsItem {
        width: 250px;
        position: relative;
        margin: 8px 6px 5px;

        .new-extensions {
            position: absolute;
            z-index: 99;
            top: -5px;
            padding-left: 55px;
        }

        &:hover .new-extensions {
            transition: transform .6s ease-in-out;
            transform: translate(0, -4px);
            animation: updown .8s linear infinite;
        }
    }
}

.extension-card-footer-icon
{
    display: inline-block;
    margin-right: 5px;
}

.extension-card-footer-text
{
    display: inline-block;
    margin-top: 3px;
}