import { <PERSON><PERSON><PERSON>roller } from 'angular';
import { LoadingService } from 'modules/ui/LoadingService';
import {
    BLIPDESK_ACTIVE,
    DESK_OWNER_IDENTITY,
    DEFAULT_PROVIDER,
} from 'modules/application/detail/attendance/configuration/ConfigurationController';
import { ConfigurationsService } from 'modules/application/misc/ConfigurationsService';
import { SegmentService, SUCCESS_STATUS, FAILURE_STATUS } from 'modules/application/misc/SegmentService';
import { ToastService, ToastType } from 'modules/application/misc/ToastService';
import * as styles from './blipdesk.module.scss';
import { TenantService } from 'modules/application/tenant/TenantService';
import { AttendanceFeatures } from 'modules/application/detail/attendance/AttendanceFeatures';

export const LimeProvider = 'Lime';
export class BlipDeskController implements IController {
    public configuration: any;
    public isActive: boolean;
    public styles: any;
    public blipDeskUrl: string;
    public isConfigurationsApplicationsDomainUsageGetEnabled: boolean;
    public isConfigurationsApplicationsDomainUsageSetEnabled: boolean;

    constructor(
        private application: any,
        private LoadingService: LoadingService,
        private ConfigurationsService: ConfigurationsService,
        private SegmentService: SegmentService,
        private ToastService: ToastService,
        private MESSAGINGHUBDOMAIN: string,
        BLIP_DESK_URL: string,
        BLIP_DESK_URL_TENANT: string,
        TenantService: TenantService,
    ) {
        this.blipDeskUrl = TenantService.addTenantPrefixToUrl(
            BLIP_DESK_URL_TENANT,
            this.application?.tenantId,
            BLIP_DESK_URL);
    }

    async $onInit() {
        await this.checkFeatures();
        this.styles = styles;
        this.configuration = {};

        this.loadConfiguration();
    }

    async checkFeatures() {
        this.isConfigurationsApplicationsDomainUsageGetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsGetEnabled();
        this.isConfigurationsApplicationsDomainUsageSetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsSetEnabled();
    }

    /**
     * Load bot's customer service channel configuration
     */
    async loadConfiguration(): Promise<void> {
        try {
            this.LoadingService.startLoading();
            const configuration = await this.ConfigurationsService.get(
                `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
                undefined,
                this.isConfigurationsApplicationsDomainUsageGetEnabled
            );
            this.configuration = { ...configuration };
            this.isActive =
                this.configuration[DEFAULT_PROVIDER] == LimeProvider
                    ? true
                    : false;
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Check if another customer service channel is connected
     *
     * @returns whether a channel other than BLiP Desk is connected or not
     */
    private isOtherChannelConnected(): boolean {
        return this.configuration
            && this.configuration[DEFAULT_PROVIDER]
            && this.configuration[DEFAULT_PROVIDER] !== LimeProvider;
    }

    /**
     * If no other channel is connected, save configuration and activates BLiP Desk
     */
    private async activateIntegration(): Promise<void> {
        if (this.isOtherChannelConnected()) {
            this.isActive = false;
            this.ToastService.toast(ToastType.Danger, 'blipdesk.error.concurrence');
            return;
        }

        try {
            this.LoadingService.startLoading();
            this.configuration[DEFAULT_PROVIDER] = LimeProvider;
            this.configuration[BLIPDESK_ACTIVE] = true;
            await this.saveConfiguration();
            this.isActive = true;
            this.registerSuccessfulConnection();
        } catch (e) {
            delete this.configuration[DEFAULT_PROVIDER];
            this.configuration[BLIPDESK_ACTIVE] = false;
            this.isActive = true;
            this.registerFailedConnection();
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Delete configuration default customer service channel
     */
    private async deleteIntegration(): Promise<void> {
        try {
            this.LoadingService.startLoading();
            delete this.configuration[DEFAULT_PROVIDER];
            await this.ConfigurationsService.deleteSingle(
                `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
                DEFAULT_PROVIDER,
                undefined,
                this.isConfigurationsApplicationsDomainUsageSetEnabled
            );
            this.configuration[BLIPDESK_ACTIVE] = false;
            await this.saveConfiguration();
            this.isActive = false;
            this.registerSuccessfulDisconnection();
        } catch (e) {
            this.isActive = true;
            this.configuration[DEFAULT_PROVIDER] = LimeProvider;
            this.configuration[BLIPDESK_ACTIVE] = true;
            this.registerFailedDisconnection();
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Save bot's customer service configuration
     */
    private async saveConfiguration(): Promise<void> {
        await this.ConfigurationsService.set(
            `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
            this.configuration,
            undefined,
            this.isConfigurationsApplicationsDomainUsageSetEnabled
        );
    }

    /**
     * On switch trigger, activate or delete BLiP Desk integration
     *
     * @param value - new boolean value for cache
     */
    public onSwitch(value): void {
        this.isActive = value;

        if (this.isActive) {
            this.activateIntegration();
        } else {
            this.deleteIntegration();
        }

    }

    /**
     * Register successful connection, with toast and segment events
     */
    private registerSuccessfulConnection(): void {
        try {
            this.ToastService.toast(ToastType.Success, 'blipdesk.success.connection');
            this.SegmentService.createBotTrack('helpdesk-integrations-blipdesk-connected', this.application, { status: SUCCESS_STATUS });
            this.SegmentService.setAccount({ customerServiceChannel: 'BLiP Desk' });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register failed connection, with toast and segment events
     */
    private registerFailedConnection(): void {
        try {
            this.ToastService.toast(ToastType.Danger, 'blipdesk.error.connection');
            this.SegmentService.createBotTrack('helpdesk-integrations-blipdesk-connected', this.application, { status: FAILURE_STATUS });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register successful disconnection, with toast and segment events
     */
    private registerSuccessfulDisconnection(): void {
        try {
            this.ToastService.toast(ToastType.Success, 'blipdesk.success.disconnection');
            this.SegmentService.createBotTrack('helpdesk-integrations-blipdesk-disabled', this.application, { status: SUCCESS_STATUS });
            this.SegmentService.setAccount({ customerServiceChannel: 'None' });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register failed disconnection, with toast and segment events
     */
    private registerFailedDisconnection(): void {
        try {
            this.ToastService.toast(ToastType.Danger, 'blipdesk.error.disconnection');
            this.SegmentService.createBotTrack('helpdesk-integrations-blipdesk-disabled', this.application, { status: FAILURE_STATUS });
        } catch (e) {} //Not necessary error handling
    }
}
