import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IFormController } from 'angular';
import { LoadingService } from 'modules/ui/LoadingService';
import {
    BLIPDESK_ACTIVE,
    DESK_OWNER_IDENTITY,
    DEFAULT_PROVIDER,
} from 'modules/application/detail/attendance/configuration/ConfigurationController';
import { ConfigurationsService } from 'modules/application/misc/ConfigurationsService';
import { SegmentService, SUCCESS_STATUS, FAILURE_STATUS } from 'modules/application/misc/SegmentService';

import * as styles from './salesforce.module.scss';
import { ToastService, ToastType } from 'modules/application/misc/ToastService';
import { DeskConfigurationService } from '../../../DeskConfigurationService';
import { Application } from 'modules/shared/ApplicationTypings';
import { AttendanceFeatures } from 'modules/application/detail/attendance/AttendanceFeatures';

export const SalesforceProvider = 'Salesforce';

interface ContactEntity {
    contactProperty: string;
    entityName: string;
    fieldName: string;
}

export class SalesforceController implements IController {
    public isActive: boolean;
    public isLoadingConfiguration: boolean;
    public styles: any;
    public configuration: Object;
    public configurationForm: IFormController;
    public contactsForm: IFormController;

    public createContact: boolean;
    public createCase: boolean;
    public mainButtonId: string;
    public additionalButtonIds: string[];

    public contactEntities: ContactEntity[];
    public isConfigurationsApplicationsDomainUsageGetEnabled: boolean;
    public isConfigurationsApplicationsDomainUsageSetEnabled: boolean;

    constructor(
        private application: Application,
        private LoadingService: LoadingService,
        private ConfigurationsService: ConfigurationsService,
        private DeskConfigurationService: DeskConfigurationService,
        private ToastService: ToastService,
        private SegmentService: SegmentService,
        private MESSAGINGHUBDOMAIN: string
    ) { }

    async $onInit() {
        await this.checkFeatures();
        this.styles = styles;

        // Set default values
        this.mainButtonId = '';
        this.additionalButtonIds = [];
        this.contactEntities = [];

        this.loadConfiguration();
    }

    async checkFeatures() {
        this.isConfigurationsApplicationsDomainUsageGetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsGetEnabled();
        this.isConfigurationsApplicationsDomainUsageSetEnabled = await AttendanceFeatures.isUseConfigurationsDomainOnAttendanceOperationsSetEnabled();
    }

    /**
     * Load bot's customer service channel configuration
     */
    private async loadConfiguration(): Promise<void> {
        try {
            this.LoadingService.startLoading();
            const configuration = await this.ConfigurationsService.get(
                `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
                undefined,
                this.isConfigurationsApplicationsDomainUsageGetEnabled
            );
            this.configuration = { ...configuration };
            this.isActive =
                this.configuration
                && this.configuration[DEFAULT_PROVIDER] == SalesforceProvider
                    ? true
                    : false;
            if (this.configuration['Salesforce.ButtonId']) {
                const buttons = this.configuration['Salesforce.ButtonId'].split(',');
                this.mainButtonId = buttons[0];
                this.additionalButtonIds = buttons.slice(1);
            }

            this.createContact =
                this.configuration['Salesforce.CreateContact'] &&
                this.configuration['Salesforce.CreateContact'].toLowerCase() === 'true';
            this.createCase =
                this.configuration['Salesforce.CreateCase'] &&
                this.configuration['Salesforce.CreateCase'].toLowerCase() === 'true';

            if (this.configuration['Salesforce.ContactEntityMap']) {
                const contactEntityMap = JSON.parse(this.configuration['Salesforce.ContactEntityMap']);
                this.contactEntities = Object
                    .keys(contactEntityMap)
                    .map(key => contactEntityMap[key].map(entity => ({
                            ...entity,
                            contactProperty: key,
                        }))
                    )
                    .reduce((acc, value) => acc.concat(value, []), []);
            }
        } catch (e) {
            console.error(e);
            if (e.code != 67) {
                this.ToastService.toast(ToastType.Danger, 'salesforce.error.loadConfiguration');
            }
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Check if another customer service channel is connected
     *
     * @returns whether a channel other than Salesforce is connected or not
     */
    private isOtherChannelConnected(): boolean {
        return this.configuration
            && this.configuration[DEFAULT_PROVIDER]
            && this.configuration[DEFAULT_PROVIDER] !== SalesforceProvider;
    }

    /**
     * Submit Configuration tab form
     */
    public submitConfigurationForm(): void {
        if (this.isOtherChannelConnected()) {
            this.ToastService.toast(ToastType.Danger, 'salesforce.error.concurrence');
            return;
        }

        let buttons = this.mainButtonId;
        if (this.additionalButtonIds && this.additionalButtonIds.length > 0) {
            buttons = this.mainButtonId.concat(',', this.additionalButtonIds.join(','));
        }
        this.configuration['Salesforce.ButtonId'] = buttons;
        this.configuration['Salesforce.CreateContact'] = this.createContact;
        this.configuration['Salesforce.CreateCase'] = this.createCase;

        this.activateIntegration();
    }

    public changeCreateContact = async (event: CustomEvent) => {
        this.createContact = await event.detail.checked;
        this.setFormUnpristine();
    }

    public changeCreateCase =  async (event: CustomEvent) => {
        this.createCase = await event.detail.checked;
        this.setFormUnpristine();
    }

    /**
     * Submit Contacts tab form
     */
    public submitContactsForm(): void {
        if (this.isOtherChannelConnected()) {
            this.ToastService.toast(ToastType.Danger, 'salesforce.error.concurrence');
            return;
        }

        const contactEntitiesMap = {};
        this.contactEntities.map(c => {
            if (!contactEntitiesMap[c.contactProperty]) {
                contactEntitiesMap[c.contactProperty] = [];
            }
            contactEntitiesMap[c.contactProperty].push({
                entityName: c.entityName,
                fieldName: c.fieldName
            });
        });

        this.configuration['Salesforce.ContactEntityMap'] = JSON.stringify(contactEntitiesMap);
        this.contactsForm.$setPristine();

        this.activateIntegration();
    }

    /**
     * Add new contactEntity
     */
    public addContactEntity(): void {
        this.contactEntities.push({ entityName: '', contactProperty: '', fieldName: '' });
    }

    /**
     * Remove existing contactEntity from list
     * @param index
     */
    public deleteContactEntity(index: number): void {
        this.contactEntities.splice(index, 1);
        this.contactsForm.$pristine = false;

    }

    /**
     * If no other channel is connected, save configuration and activates Salesforce
     */
    private async activateIntegration(): Promise<void> {
        if (this.isOtherChannelConnected()) {
            this.isActive = false;
            this.ToastService.toast(ToastType.Danger, 'salesforce.error.concurrence');
            return;
        }

        if (this.configurationForm.$invalid) {
            this.ToastService.toast(ToastType.Danger, 'salesforce.error.noConfiguration');
            return;
        }

        try {
            this.LoadingService.startLoading();
            this.configuration[DEFAULT_PROVIDER] = SalesforceProvider;
            await this.saveConfiguration();
            this.isActive = true;
            this.setBlockToolsCache(true);
            this.registerSuccessfulConnection();
        } catch (e) {
            this.isActive = false;
            this.registerFailedConnection();
        } finally {
            this.configurationForm.$setPristine();
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Delete configuration default customer service channel
     */
    private async deleteIntegration(): Promise<void> {
        try {
            this.LoadingService.startLoading();
            await this.deleteConfiguration();
            this.isActive = false;
            this.setBlockToolsCache(false);
            this.registerSuccessfulDisconnection();
        } catch (e) {
            this.isActive = true;
            this.registerFailedDisconnection();
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    /**
     * Delete configuration's default provider
     */
    private async deleteConfiguration(): Promise<void> {
        await this.ConfigurationsService.deleteSingle(
            `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
            DEFAULT_PROVIDER,
            undefined,
            this.isConfigurationsApplicationsDomainUsageSetEnabled
        );
    }

    /**
     * Set shouldBlockTools' cache value
     *
     * @param value - new boolean value for cache
     */
    private async setBlockToolsCache(value: boolean): Promise<void> {
        try {
            await this.DeskConfigurationService.setBlockToolsCache(this.application, value);
        } catch (e) {}
    }

    /**
     * On switch trigger, activate or delete Salesforce integration
     *
     * @param value - new boolean value for cache
     */
    public onSwitch(value): void {
        if (value === undefined) {
            return;
        }

        if (value) {
            this.activateIntegration();
        } else {
            this.deleteIntegration();
        }
    }

    /**
     * Save bot's customer service configuration
     */
    private async saveConfiguration(): Promise<void> {
        await this.ConfigurationsService.set(
            `${DESK_OWNER_IDENTITY}.${this.MESSAGINGHUBDOMAIN}`,
            this.configuration,
            undefined,
            this.isConfigurationsApplicationsDomainUsageSetEnabled
        );
    }

    /**
     * Register successful connection, with toast and segment events
     */
    private registerSuccessfulConnection(): void {
        try {
            this.ToastService.toast(ToastType.Success, 'salesforce.success.connection');
            this.SegmentService.createBotTrack('helpdesk-integrations-salesforce-connected', this.application, { status: SUCCESS_STATUS });
            this.SegmentService.setAccount({ customerServiceChannel: 'Salesforce' });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register failed connection, with toast and segment events
     */
    private registerFailedConnection(): void {
        try {
            this.ToastService.toast(ToastType.Danger, 'salesforce.error.connection');
            this.SegmentService.createBotTrack('helpdesk-integrations-salesforce-connected', this.application, { status: FAILURE_STATUS });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register successful disconnection, with toast and segment events
     */
    private registerSuccessfulDisconnection(): void {
        try {
            this.ToastService.toast(ToastType.Success, 'salesforce.success.disconnection');
            this.SegmentService.createBotTrack('helpdesk-integrations-salesforce-disabled', this.application, { status: SUCCESS_STATUS });
            this.SegmentService.setAccount({ customerServiceChannel: 'None' });
        } catch (e) {} //Not necessary error handling
    }

    /**
     * Register failed disconnection, with toast and segment events
     */
    private registerFailedDisconnection(): void {
        try {
            this.ToastService.toast(ToastType.Danger, 'salesforce.error.disconnection');
            this.SegmentService.createBotTrack('helpdesk-integrations-salesforce-disabled', this.application, { status: FAILURE_STATUS });
        } catch (e) {} //Not necessary error handling
    }

    public setFormUnpristine(): void {
        this.configurationForm.$pristine = false;
    }
}
