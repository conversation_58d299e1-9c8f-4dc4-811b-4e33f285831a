import * as angular from 'angular';
import 'angular-ui-router';

import permissions from 'application/detail/permissions.json';

import BlipDeskView from './blipdesk/BlipDeskView.html';
import { BlipDeskController } from './blipdesk/BlipDeskController';

import SalesforceView from './salesforce/SalesforceView.html';
import { SalesforceController } from './salesforce/SalesforceController';

import WebhookView from './webhook/DeskWebhookView.html';
import { WebhookController } from './webhook/DeskWebhookController';

import { IStateProvider } from 'angular-ui-router';

export const BLIPDESK_STATE = 'auth.application.detail.attendance.channels.blipdesk';
export const SALESFORCE_STATE = 'auth.application.detail.attendance.channels.salesforce';
export const WEBHOOK_STATE = 'auth.application.detail.attendance.channels.webhook';

export const channels = angular
    .module('attendance.channels', ['ui.router', 'shared'])
    .config(($stateProvider: IStateProvider) => {
        $stateProvider
        .state(
            BLIPDESK_STATE,
            {
                url: '/blipdesk',
                views: {
                    '<EMAIL>': {
                        template: BlipDeskView,
                        controller: BlipDeskController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.desk.claim,
                },
                params: { area: 'desk' },
            },
        )
        .state(
            SALESFORCE_STATE,
            {
                url: '/salesforce',
                views: {
                    '<EMAIL>': {
                        template: SalesforceView,
                        controller: SalesforceController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.desk.claim,
                },
                params: { area: 'desk' },
            },
        )
        .state(
            WEBHOOK_STATE,
            {
                url: '/webhook',
                views: {
                    '<EMAIL>': {
                        template: WebhookView,
                        controller: WebhookController,
                        controllerAs: '$ctrl',
                    },
                },
                data: {
                    permissionClaim: permissions.desk.claim,
                },
                params: { area: 'desk' },
            },
        );
    }).name;
