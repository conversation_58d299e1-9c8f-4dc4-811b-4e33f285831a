<page-header id="webhook-channel-header" back-button="auth.application.detail.attendance.channels" page-title="{{'deskWebhookProvider.title' | translate}}">
    <custom-content>
        <switch
            id="channel-switch"
            ng-permission="true"
            on-toggle="$ctrl.onSwitch(value)"
            ng-model="$ctrl.isActive">
        </switch>
    </custom-content>
</page-header>

<div id="webhook-channel-page" class="container">
    <article class="bp-card bp-card--left-arrow">
        <content-tabs>
            <tab id="desk-webhook-provider-overview-tab"
                tab-title="{{'deskWebhookProvider.overview.title' | translate}}"
                track-click="helpdesk-integrations-webhook-overview-opened">
                <section class="{{$ctrl.styles.section}}">
                    <img class="{{$ctrl.styles.logo}}"
                        src="/assets/img/desk/providers/custom.svg"
                        alt="{{ 'deskWebhookProvider.overview.image.alt' | translate }}"
                        title="{{ 'deskWebhookProvider.overview.image.title' | translate }}">
                    <bds-typo tag="p" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate>
                        deskWebhookProvider.overview.description
                    </bds-typo>
                </section>
            </tab>

            <tab id="desk-webhook-provider-configuration-tab"
                tab-title="{{'deskWebhookProvider.configuration.title' | translate}}"
                track-click="helpdesk-integrations-webhook-config-opened">
                <section class="{{$ctrl.styles.section}}">
                    <form id="desk-webhook-provider-configuration-form"
                        class="{{$ctrl.styles.form}}"
                        name="$ctrl.configurationForm"
                        ng-submit="$ctrl.submitConfigurationForm()"
                        novalidate>
                        <bds-typo tag="h1" variant="fs-20" bold="semi-bold" class="{{$ctrl.styles.typoTitle}} mb3" translate>
                            deskWebhookProvider.configuration.subtitle
                        </bds-typo>
                        <div class="{{$ctrl.styles.typoLegend}}">
                            <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate>
                                deskWebhookProvider.configuration.form.description.1
                            </bds-typo>
                            <bds-typo tag="span" variant="fs-14" class="{{$ctrl.styles.paragraph}}" translate>
                                deskWebhookProvider.configuration.form.description.2
                            </bds-typo>
                        </div>

                        <blip-input-dpr
                            id="desk-webhook-provider-api-endpoint-input"
                            class="{{$ctrl.styles.input}}"
                            type="text"
                            ng-model="$ctrl.configuration['Webhook.ApiEndpoint']"
                            disabled="$ctrl.channel.isActive"
                            label="{{'deskWebhookProvider.configuration.form.apiEndpoint.label' | translate}}"
                            placeholder="{{'deskWebhookProvider.configuration.form.apiEndpoint.placeholder' | translate}}"
                            required="true">
                        </blip-input-dpr>

                        <blip-input-dpr
                            id="desk-webhook-provider-authentication-token-input"
                            class="{{$ctrl.styles.input}}"
                            type="text"
                            ng-model="$ctrl.configuration['Webhook.AuthenticationToken']"
                            disabled="$ctrl.channel.isActive"
                            label="{{'deskWebhookProvider.configuration.form.authenticationToken.label' | translate}}"
                            placeholder="{{'deskWebhookProvider.configuration.form.authenticationToken.placeholder' | translate}}">
                        </blip-input-dpr>

                        <div class="mt4 {{$ctrl.styles.checkboxes}}">
                            <blip-checkbox id="create-contact-checkbox" ng-model="$ctrl.hasBlipTeams" label="{{'deskWebhookProvider.configuration.form.teams.useBlipRules' | translate}}"></blip-checkbox>
                        </div>

                        <blip-multiple-input
                            ng-if="$ctrl.hasBlipTeams"
                            id="desk-webhook-provider-teams-inputs"
                            class="{{$ctrl.styles.multipleInput}}"
                            ng-model="$ctrl.blipTeams"
                            input-label="{{'deskWebhookProvider.configuration.form.teams.label' | translate}}"
                            input-placeholder="{{'deskWebhookProvider.configuration.form.teams.placeholder' | translate}}"
                            add-button-text="{{'deskWebhookProvider.configuration.form.teams.addButton' | translate}}">
                        </blip-multiple-input>

                        <footer class="{{$ctrl.styles.footer}}">
                            <button
                                ng-if="!$ctrl.isActive"
                                id="webhook-configuration-submit-button"
                                class="bp-btn bp-btn--bot mt4"
                                type="submit"
                                ng-disabled="$ctrl.configurationForm.$invalid"
                                translate>
                                deskWebhookProvider.configuration.form.connect
                            </button>
                            <button
                                ng-if="$ctrl.isActive"
                                id="webhook-configuration-submit-button"
                                class="bp-btn bp-btn--bot mt4"
                                type="submit"
                                ng-disabled="$ctrl.configurationForm.$pristine || $ctrl.configurationForm.$invalid"
                                translate>
                                deskWebhookProvider.configuration.form.save
                            </button>
                        </footer>
                    </form>
                </section>
            </tab>

            <tab id="webhook-documentation-tab"
                tab-title="{{'deskWebhookProvider.documentation.title' | translate}}"
                tab-href="{{'deskWebhookProvider.documentation.link' | translate}}"
                track-click="helpdesk-integrations-webhook-doc-opened">
            </tab>
        </content-tabs>
    </article>
</div>
