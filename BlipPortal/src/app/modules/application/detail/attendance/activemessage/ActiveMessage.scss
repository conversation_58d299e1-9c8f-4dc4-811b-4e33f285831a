@import '~assets/scss/main';
@import '~assets/scss/utils/module';

.ml-8 {
    margin-left: 8px;
}

.not-found {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.edit-dropdown {
    color: $bp-color-cloud;
    font-size: $bp-fs-4;

    .edit-params {
        font-size: $bp-fs-5;
        color: #000;
        width: 38*$m;
        margin-bottom: 0;
    }
}

.params-table tbody td {
    height: 5.15*$m !important;
}

.pre-line {
    white-space: pre-line;
}

.message-text {
    overflow: auto;
    overflow-x: hidden;
    height: 290px;
}

.text-blue {
    color: #3f7de8;
}

.text-italic {
    font-style: italic;
}

.text-line-through {
    text-decoration: line-through;
}

.invalid-flow {
	color: $color-extend-reds-delete;
}

.banner-text {
    margin-top: 12px;
}

.return-flow-column {
    display: flex;
    justify-content: center;
}

.banner-padding {
    padding-top: 24px;
    padding-bottom: 24px;
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    padding-top: 24px;
}

.modal-content {
    display: 'flex';
    align-items: 'center'
}