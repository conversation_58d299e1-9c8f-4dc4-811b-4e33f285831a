<page-header
    id="helpdesk-active-message-header"
    page-title="{{'modules.application.detail.attendance.activeMessage.pageTitle' | translate }}">
</page-header>
<div id="helpdesk-active-message-page" class="container">
    <bds-paper class="pa4" id="helpdesk-active-message-page-template-message">
        <div class="row flex items-center mb0 w-100">
            <bds-typo
                bold="bold"
                variant="fs-20"
                margin="false"
                class="flex items-center w-70"
                translate>
                templateMessage.title
            </bds-typo>
            <div class="flex flex-row w-30 items-center">
                <bds-input
                    type="text"
                    value="{{ $ctrl.filterTemplateName }}"
                    placeholder="{{ 'templateMessage.search' | translate }}"
                    icon="search"
                    id="input-search-value"
                    custom-events="[{ event:'bdsChange', cb: $ctrl.debouncedSearch }]">
                </bds-input>
            </div>
        </div>
        <div class="row flex mt3 mb0 w-100">
            <strong class="mr3">{{ 'templateMessage.filterBy' | translate }}:</strong>
            <dropdown-item
                item-title="{{ 'templateMessage.returnFlow' | translate }} <bds-icon name='arrow-down' theme='solid' size='x-small'></bds-icon>"
                hide-icon="true"
                close-on-click="false"
                id="return-flow-filter-dropdown"
                class="dib left">
                <form class="filter-form">
                    <div class="w-100 ph4 pt2">
                        <bds-autocomplete
                            id="bdsAutocomplete_returnFlow_filter"
                            label="{{ 'templateMessage.returnFlow' | translate }}"
                            placeholder="{{ 'templateMessage.returnFlow' | translate }}"
                            custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.selectReturnFlowFilter }]"
                        ></bds-autocomplete>
                    </div>
                    <div class="bp-divider-h w-100 bp-bg-breeze mb2 mt4"></div>
                    <div class="w-100 flex flex-row justify-end pt1 ph4">
                        <button id="return-flow-filter-cancel-button" class="bp-btn bp-btn--text-only bp-btn--city mr3" ng-click="$ctrl.clearFilterInput()" translate>utils.forms.cancel</button>
                        <button id="return-flow-filter-apply-button" class="bp-btn bp-btn--text-only bp-btn--bot" ng-click="$ctrl.addReturnFlowFilterClearInput()" ng-disabled="!$ctrl.returnFlowSelected" translate>utils.forms.apply</button>
                    </div>
                </form>
            </dropdown-item>
            <dropdown-item
                item-title="{{ 'templateMessage.status' | translate }} <bds-icon name='arrow-down' theme='solid' size='x-small'></bds-icon>"
                hide-icon="true"
                close-on-click="false"
                id="status-filter-dropdown"
                class="dib left ml-8">
                <form class="filter-form">
                    <div class="w-100 ph4 pt2">
                        <bds-autocomplete
                            id="bdsAutocomplete_status_filter"
                            label="{{ 'templateMessage.status' | translate }}"
                            placeholder="{{ 'templateMessage.status' | translate }}"
                            custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.selectStatusFilter }]"
                        ></bds-autocomplete>
                    </div>
                    <div class="bp-divider-h w-100 bp-bg-breeze mb2 mt4"></div>
                    <div class="w-100 flex flex-row justify-end pt1 ph4">
                        <button id="status-filter-cancel-button" class="bp-btn bp-btn--text-only bp-btn--city mr3" ng-click="$ctrl.clearFilterInput()" translate>utils.forms.cancel</button>
                        <button id="status-filter-apply-button" class="bp-btn bp-btn--text-only bp-btn--bot" ng-click="$ctrl.addStatusFilterClearInput()" ng-disabled="!$ctrl.statusSelected" translate>utils.forms.apply</button>
                    </div>
                </form>
            </dropdown-item>
        </div>
        <div class="row flex mt0 mb0 w-100">
            <div class="twelve columns">
                <div id="filter-chips" class="query-filters">
                    <chips id="return-flow-chips" data="$ctrl.filteredReturnFlowNames" on-remove="$ctrl.removeReturnFlowName($data, $removedItem)"></chips>
                    <chips id="status-chips" data="$ctrl.filteredStatus" on-remove="$ctrl.removeStatus($data, $removedItem)"></chips>
                </div>
            </div>
        </div>
        <div
            id="helpdesk-active-message-page-template-message-not-found"
            class="row flex mt5 mb5 w-100 not-found"
            ng-if="$ctrl.notFound">
            <img alt="" src="/assets/img/empty-box.svg" />
            <bds-typo
                bold="bold"
                variant="fs-12"
                margin="false"
                class="text-center">
                {{ 'templateMessage.notFound' | translate }}
            </bds-typo>
        </div>
        <div
            id="helpdesk-active-message-page-template-message-not-found"
            class="row flex mt5 mb5 w-100 not-found"
            ng-if="$ctrl.notFoundWithFilter">
            <bds-icon
                theme="outline"
                size="xxx-large"
                name="search">
            </bds-icon>
            <bds-typo
                bold="bold"
                variant="fs-12"
                margin="false"
                class="mt4 mb4 text-center">
                {{ 'templateMessage.notFoundFilter' | translate }}
            </bds-typo>
            <bds-button
                variant="primary"
                translate
                ng-click="$ctrl.clearAllFilters()">
                templateMessage.clearFilters
            </bds-button>
        </div>
        <bds-banner class="banner-padding" ng-if="$ctrl.hasInvalidReturnFlow" variant="error">
            <div class="banner-text">
                {{ ::'templateMessage.table.body.invalidStateBanner' | translate }}
            </div>
            <bds-button variant='secondary' ng-click="$ctrl.toggleStateIdUpdateModal()">
                {{ ::'templateMessage.table.header.updateStateButton' | translate }}
            </bds-button>
        </bds-banner>
        <div ng-if="!$ctrl.notFound && !$ctrl.notFoundWithFilter" class="mt3">
            <table class="params-table bp-table w-100" aria-describedby="">
                <thead>
                    <th class="tl adjust w-25">
                        {{ ::'templateMessage.table.header.name' | translate }}
                    </th>
                    <th class="tl adjust w-10">
                        {{ ::'templateMessage.table.header.language' | translate }}
                    </th>
                    <th class="tl adjust">
                        {{ ::'templateMessage.table.header.message' | translate }}
                    </th>
                    <th class="tl adjust w-15">
                        {{ ::'templateMessage.table.header.returnFlow' | translate }}
                    </th>
                    <th class="tl adjust w-15">
                        {{ ::'templateMessage.table.header.status' | translate }}
                    </th>
                    <th class="tl adjust w-5">
                    </th>
                </thead>
                <tbody ng-mouseleave="$ctrl.hoverCurrentParams = undefined">
                    <tr ng-repeat="messageTemplateWithParams in $ctrl.approvedMessageTemplatesWithParams track by messageTemplateWithParams.template.Id"
                        ng-mouseenter="$ctrl.hoverCurrentParams = messageTemplateWithParams"
                        class="params-row">
                        <td class="truncate" title="{{ messageTemplateWithParams.template.Name }}">{{ messageTemplateWithParams.template.Name }}</td>
                        <td>{{ messageTemplateWithParams.template.Language }}</td>
                        <td>
                            <div class="flex justify-between items-center">
                                <div class="truncate">
                                    {{ $ctrl.getMessageFromTemplate(messageTemplateWithParams.template) }}
                                </div>
                                <a ng-click="$ctrl.toggleTemplateMessageModal()">{{ 'templateMessage.table.body.open' | translate }}</a>
                            </div>
                        </td>
                        <td class="return-flow-column">
                            <bds-tooltip tooltip-text="{{ 'templateMessage.table.body.invalidStateId' | translate }}"
                                position="top-center" ng-if="!messageTemplateWithParams.params.isValid">
                                <bds-icon name="attention" size="small" theme="solid" class="invalid-flow">
                                </bds-icon>
                            </bds-tooltip>
                            <div class="truncate">
                                {{ $ctrl.getFlowStateName(messageTemplateWithParams.params) }}
                            </div>
                        </td>
                        <td>
                            <bds-tooltip
                                position="top-center"
                                tooltip-text="{{ 'templateMessage.table.body.stateIdNotFound' | translate }}"
                                ng-if="!$ctrl.singleReturnFlow && !messageTemplateWithParams.params.stateId">
                                <bds-switch
                                    id="{{ messageTemplateWithParams.template.Id }}-fake-edit-switch-out"
                                    ng-checked="false"
                                    ng-disabled="true">
                                </bds-switch>
                            </bds-tooltip>
                            <bds-switch
                                id="{{ messageTemplateWithParams.template.Id }}-edit-switch-out"
                                custom-events="[{ event:'bdsChange', cb: $ctrl.toggleMessageTemplateWithParams }]"
                                ng-checked="$ctrl.isEnabled(messageTemplateWithParams)"
                                ng-if="messageTemplateWithParams.params.stateId || $ctrl.singleReturnFlow">
                            </bds-switch>
                        </td>
                        <td>
                            <dropdown-item
                                close-on-click="false"
                                custom-icon="Edit"
                                color-icon="gray"
                                width-icon="20px"
                                height-icon="20px"
                                id="edit-dropdown"
                                class="dib right edit-dropdown"
                                direction="$ctrl.dropdownDirection(messageTemplateWithParams)"
                                ng-if="$ctrl.canShowEditButton(messageTemplateWithParams.template.Id)"
                                on-open="$ctrl.dropDownOpened()"
                                on-close="$ctrl.dropDownClosed()">
                                <form class="edit-params mb0">
                                    <div class="w-100 text-left ml4 mt2 mb2">
                                        <strong class="fs-12">{{ 'templateMessage.table.editDropDown.title' | translate }}</strong>
                                    </div>
                                    <div class="w-100 ph4 pt2">
                                        <bds-autocomplete
                                            id="bdsAutocomplete_edit_form_return_flow_{{ messageTemplateWithParams.template.Id }}"
                                            label="{{ 'templateMessage.table.editDropDown.returnFlow' | translate }}"
                                            options="{{ $ctrl.editReturnFlow }}"
                                            placeholder="{{ 'templateMessage.table.editDropDown.placeholder' | translate }}"
                                            custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.selectEditReturnFlow }, { event:'bdsChange', cb: $ctrl.selectEditReturnFlow }]"
                                            value="{{ $ctrl.selectEditReturnFlowValue(messageTemplateWithParams) }}">
                                        </bds-autocomplete>
                                    </div>
                                    <div class="bp-divider-h w-100 bp-bg-breeze mb2 mt4"></div>
                                    <div class="w-100 flex flex-row justify-end pt1 ph4">
                                        <button id="status-filter-cancel-button" class="bp-btn bp-btn--text-only bp-btn--city mr3"
                                            ng-click="$ctrl.clearEditInput(messageTemplateWithParams)" translate>templateMessage.table.editDropDown.cancelButton
                                        </button>
                                        <button id="status-filter-apply-button" class="bp-btn bp-btn--text-only bp-btn--bot"
                                            ng-click="$ctrl.applyReturnFlowEdit(messageTemplateWithParams)"
                                            ng-disabled="!$ctrl.isDropDownEditButtonEnabled(messageTemplateWithParams)" variant="primary" translate>
                                            templateMessage.table.editDropDown.editButton
                                        </button>
                                    </div>
                                </form>
                            </dropdown-item>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </bds-paper>
</div>
<bds-modal ng-show="$ctrl.openDetailModal || $ctrl.openStateIdUpdate"
    size="dynamic"
    open="{{$ctrl.openDetailModal}}"
    custom-events="[{ event: 'bdsModalChanged' , cb: $ctrl.toggleTemplateMessageModal }]"
    close-button="{{ !$ctrl.openStateIdUpdate }}">
    <div>
        <div>
            <bds-typo
                bold="bold"
                variant="fs-20"
                margin="false"
                class="flex w-100 mb4"
                translate>
                {{ $ctrl.modalTitle }}
            </bds-typo>
        </div>
        <div ng-if="$ctrl.openDetailModal" class="message-text">
            <bds-typo
                bold="normal"
                variant="fs-14"
                margin="false"
                class="flex w-100 pre-line"
                ng-bind-html="$ctrl.modalBody">
            </bds-typo>
        </div>
        <div class="modal-content" ng-if="$ctrl.openStateIdUpdate">
            <p>{{ ::'templateMessage.table.modal.updateStateDescription' | translate }}</p>
            <form class="edit-params mb0">
                <div class="w-100 pt2">
                    <bds-autocomplete
                        id="bdsAutocomplete_edit_form_update_return_flow"
                        label="{{ 'templateMessage.table.editDropDown.returnFlow' | translate }}"
                        options="{{ $ctrl.editReturnFlow }}"
                        placeholder="{{ 'templateMessage.table.editDropDown.placeholder' | translate }}"
                        custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.selectUpdateReturnFlow }]"
                        value="{{ $ctrl.updateReturnFlowValue }}">
                    </bds-autocomplete>
                </div>
            </form>
            <bds-modal-action class="modal-buttons">
                <bds-button variant="secondary" ng-click="$ctrl.toggleStateIdUpdateModal()">
                    {{ ::'templateMessage.table.editDropDown.cancelButton' | translate }}
                </bds-button>
                <bds-button variant="primary" ng-click="$ctrl.messageTemplateUpdateStateId()"
                    ng-disabled="!$ctrl.updateReturnFlowValue"
                    bds-loading="{{ $ctrl.isUpdatingReturnFlow }}">
                    {{ ::'templateMessage.table.modal.updateStateButton' | translate }}
                </bds-button>
            </bds-modal-action>
        </div>
    </div>
</bds-modal>
