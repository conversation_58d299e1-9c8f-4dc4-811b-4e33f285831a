import { IBlipScope } from 'interfaces';
import { IRootScopeService } from 'angular';
import { debounce } from 'data/function';

import { Application } from 'modules/shared/ApplicationTypings';
import { LoadingService as _LoadingService } from 'modules/ui/LoadingService';

import { DeskConfigurationService } from '../DeskConfigurationService';
import { DeskControllerBase } from '../DeskControllerBase';
import { SegmentService as _SegmentService } from 'modules/application/misc/SegmentService';
import { FilterTypes } from '../FilterTypes';

import { ApprovedMessageTemplatesWithParams } from '../models/ApprovedMessageTemplatesWithParams';

import { ActiveMessageSegmentEventsConstants } from 'modules/application/detail/attendance/AttendanceSegmentEvents';

import './ActiveMessage.scss';
import { MessageTemplate } from '../models/MessageTemplate';
import { MessageTemplateParams } from '../models/MessageTemplateParams';
import { ActiveMessageConstants } from './ActiveMessageConstants';
import { ActiveMessageDeskSharedService } from 'modules/shared/ActiveMessageDesk/ActiveMessageDeskSharedService';
import { BlipToastDuration, BlipToastService } from 'modules/shared/BlipToastService';

export interface IAutocompleteElement {
    id: string;
    options: any[];
    element?: HTMLBdsAutocompleteElement;
}

export class ActiveMessageDeskController extends DeskControllerBase {
    displayReturnFlow: any;
    returnFlowSelected: any;
    returnFlowCollection: any[];
    returnFlowConstantCollection: any[];
    filteredReturnFlowNames: any[];
    displayStatus: any;
    statusSelected: any;
    statusCollection: any[];
    statusConstantCollection: any[];
    filteredStatus: any[];
    filterTemplateName: string;
    autoCompleteElementList: IAutocompleteElement[];
    lastSearch: Map<string, any>;
    notFound: boolean;
    notFoundWithFilter: boolean;
    approvedMessageTemplatesWithParams: ApprovedMessageTemplatesWithParams[];
    approvedMessageTemplatesWithParamsCache: ApprovedMessageTemplatesWithParams[];
    editReturnFlow: any[];
    hoverCurrentParams: ApprovedMessageTemplatesWithParams;
    editingParams: ApprovedMessageTemplatesWithParams;
    debouncedSearch: Function;
    openDetailModal: boolean;
    openStateIdUpdate: boolean;
    modalTitle: string;
    modalBody: string;
    isDropDownOpened: boolean;
    singleReturnFlow: any;
    updateReturnFlowValue: any;
    hasInvalidReturnFlow: any = undefined;
    isUpdatingReturnFlow: boolean = false;

    constructor(
        protected application: Application,
        protected DeskConfigurationService: DeskConfigurationService,
        protected LoadingService: _LoadingService,
        protected $scope: IBlipScope,
        private SegmentService: _SegmentService,
        private $rootScope: IRootScopeService,
        private ActiveMessageDeskSharedService: ActiveMessageDeskSharedService,
        private $timeout: any,
        private $translate: any,
        private BlipToastService: BlipToastService
    ) {
        super(
            application,
            LoadingService,
            DeskConfigurationService,
            $scope
        );

        this.debouncedSearch = debounce(this.searchTemplateName, 500);
    }

    async $onInit() {
        this.displayReturnFlow = '';
        this.returnFlowSelected = '';
        this.returnFlowCollection = [];
        this.filteredReturnFlowNames = [];
        this.displayStatus = '';
        this.statusSelected = '';
        this.statusCollection = [];
        this.statusConstantCollection = [];
        this.filteredStatus = [];
        this.filterTemplateName = '';
        this.autoCompleteElementList = [];
        this.lastSearch = new Map<string, any>();
        this.notFound = false;
        this.notFoundWithFilter = false;
        this.editReturnFlow = [];
        this.openDetailModal = false;
        this.openStateIdUpdate = false;
        this.isDropDownOpened = false;
        this.hoverCurrentParams = undefined;
        this.editingParams = undefined;
        this.isUpdatingReturnFlow = false;

        this.startLoading();

        await Promise.all([
            this.getReturnFlow(),
            this.getStatus()
        ]);
        this.initializeAndBindAutocompleteList();
        await this.query();

        this.stopLoading();

        this.SegmentService.createApplicationTrack({
            trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_OPENED,
            application: this.application
        });
    }

    private async query(): Promise<any> {
        const filters = this.getFilters();
        const messageTemplateWithParams = await this.ActiveMessageDeskSharedService.get(filters);
        this.approvedMessageTemplatesWithParams = messageTemplateWithParams?.resource?.items ?? [];
        this.approvedMessageTemplatesWithParams = this.approvedMessageTemplatesWithParams.map((state: any): any => ({
            params: {
                isValid: this.isValidFlowState(state.params),
                editingState: this.singleReturnFlow ? this.singleReturnFlow.value.id : '',
                ...state.params
            },
            template: state.template
        }));

        this.hasInvalidReturnFlow = this.approvedMessageTemplatesWithParams.find((msgTemplate: any) => !msgTemplate.params.isValid);
        this.approvedMessageTemplatesWithParamsCache = this.approvedMessageTemplatesWithParams;
        this.filterTemplateListByName(this.filterTemplateName);

        this.notFound = (this.approvedMessageTemplatesWithParams.length == 0);
        this.notFoundWithFilter = false;

        if (this.notFound && filters) {
            this.notFound = false;
            this.notFoundWithFilter = true;
        }
    }

    private getFilters(): string {
        const filter: string[] = [];

        if (this.filteredReturnFlowNames.length > 0) {
            filter.push(`stateId=${this.filteredReturnFlowNames.map((state: any) => state.item.value.id).join(',')}`);
        }

        if (this.filteredStatus.length === 1) {
            const enabled = this.filteredStatus[0].item.value.toUpperCase() === 'HABILITADO';
            filter.push(`enabled=${enabled}`);
        }

        if (this.filterTemplateName) {
            filter.push('templateNameFrontOnly=true');
        }

        return filter.join('&');
    }

    private initializeAndBindAutocompleteList(): void {
        this.autoCompleteElementList = [
            {
                id: '#bdsAutocomplete_returnFlow_filter',
                options: this.returnFlowCollection
            },
            {
                id: '#bdsAutocomplete_status_filter',
                options: this.statusCollection
            }
        ];

        this.autoCompleteElementList.forEach((autoComplete: IAutocompleteElement) => {
            this.bindAutocomplete(autoComplete.id);
            this.lastSearch.set(autoComplete.id.replace('#', ''), {});
        });
    }

    private bindAutocomplete(id: string, options?: string): void {
        const autoComplete: IAutocompleteElement = this.autoCompleteElementList.find((autocompleteElement: IAutocompleteElement) => autocompleteElement.id === id);
        const element: HTMLBdsAutocompleteElement = autoComplete?.element ?? document.querySelector(autoComplete.id);
        element.options = options || JSON.stringify(autoComplete.options);
        autoComplete.element = element;
    }

    private clearAutocompleteElements(): void {
        this.autoCompleteElementList.forEach((autoComplete: IAutocompleteElement) => {
            if (autoComplete.element) {
                autoComplete.element.value = undefined;
            }
        });
    }

    private getStatus(): void {
        const status = ['Habilitado', 'Desabilitado'];
        this.statusCollection = status.map((statusType: string): any => ({
            text: statusType,
            value: statusType,
            label: statusType
        }));
        this.statusConstantCollection = this.statusCollection;
    }

    private async getReturnFlow(): Promise<any> {
        const returnFlow = await this.ActiveMessageDeskSharedService.getReturnFlow();
        this.returnFlowConstantCollection = returnFlow?.resource?.states?.items ?? [];
        this.returnFlowConstantCollection = this.returnFlowConstantCollection.map((state: any): any => ({
            text: state.name,
            value: state,
            label: state.name
        }));
        this.editReturnFlow = this.returnFlowConstantCollection.map((state: any): any => ({
            text: state.text,
            value: state.value.id,
            label: state.text
        }));
        this.returnFlowCollection = this.returnFlowConstantCollection;
        this.singleReturnFlow = this.returnFlowConstantCollection.length === 1 ? this.returnFlowConstantCollection[0] : undefined;
    }

    private startLoading(): void {
        if (!this.LoadingService.isLoadingGlobal() || !this.LoadingService.isLoadingLocal()) {
            this.LoadingService.startLoading();
        }
    }

    private stopLoading(): void {
        if (this.LoadingService.isLoadingGlobal() || this.LoadingService.isLoadingLocal()) {
            this.LoadingService.stopLoading();
        }
    }

    searchTemplateName = (event: any) => {
        this.filterTemplateName = event.detail.value;
        this.filterTemplateListByName(this.filterTemplateName);

        this.notFound = (this.approvedMessageTemplatesWithParams.length == 0);
        this.notFoundWithFilter = false;

        if (this.notFound) {
            this.notFound = false;
            this.notFoundWithFilter = true;
        }

        this.createFilterTrack(FilterTypes.TemplateName);

        this.$timeout(this.$scope.$apply());
    }

    filterTemplateListByName(namePart: string): void {
        if (namePart) {
            this.approvedMessageTemplatesWithParams = this.approvedMessageTemplatesWithParamsCache.filter(params => params.template.Name.indexOf(namePart) > -1);
        } else {
            this.approvedMessageTemplatesWithParams = this.approvedMessageTemplatesWithParamsCache;
        }
    }

    createFilterTrack(filteredBy: FilterTypes): void {
        if (FilterTypes.ReturnFlowName === filteredBy) {
            this.SegmentService.createApplicationTrack({
                trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_FILTER_RETURN_FLOW,
                application: this.application
            });
        }

        if (FilterTypes.Status === filteredBy) {
            this.SegmentService.createApplicationTrack({
                trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_FILTER_STATUS,
                application: this.application
            });
        }

        if (FilterTypes.TemplateName === filteredBy) {
            this.SegmentService.createApplicationTrack({
                trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_SEARCH,
                application: this.application
            });
        }
    }

    clearFilterInput(): void {
        this.$rootScope.$broadcast('ToggleDropdownItem');
        this.clearAutocompleteElements();
    }

    selectReturnFlowFilter = (event: any): void => {
        const selectedValue: string = event.detail?.value;
        this.returnFlowSelected = selectedValue || '';
    }

    selectStatusFilter = (event: any): void => {
        const selectedValue: string = event.detail?.value;
        this.statusSelected = selectedValue || '';
    }

    async addReturnFlowFilterClearInput(): Promise<any> {
        this.createFilterTrack(FilterTypes.ReturnFlowName);
        this.addReturnFlowFilter(this.returnFlowSelected);
        this.clearFilterInput();

        await this.reload();
    }

    async addStatusFilterClearInput(): Promise<any> {
        this.createFilterTrack(FilterTypes.Status);
        this.addStatusFilter(this.statusSelected);
        this.clearFilterInput();

        await this.reload();
    }

    addReturnFlowFilter(item: any): void {
        if (!item) {
            return;
        }

        const removedItem = this.returnFlowCollection.find((returnFlow: any) => returnFlow.value.id == item.id);
        this.returnFlowCollection = this.returnFlowCollection.filter((returnFlow: any) => returnFlow.value.id !== this.returnFlowSelected.id);
        this.filteredReturnFlowNames = this.filteredReturnFlowNames.concat({
            text: removedItem.text,
            item: removedItem
        });

        this.bindAutocomplete('#bdsAutocomplete_returnFlow_filter', JSON.stringify(this.returnFlowCollection));
    }

    addStatusFilter(item: any): void {
        if (!item) {
            return;
        }

        const removedItem = this.statusCollection.find((status: any) => status.value == item);
        this.statusCollection = this.statusCollection.filter((status: any) => status.value !== item);
        this.filteredStatus = this.filteredStatus.concat({
            text: removedItem.text,
            item: removedItem,
        });

        this.bindAutocomplete('#bdsAutocomplete_status_filter', JSON.stringify(this.statusCollection));
    }

    async removeReturnFlowName(items: any, removedItem: any): Promise<any> {
        this.returnFlowCollection = this.returnFlowCollection.concat(removedItem.item);
        this.filteredReturnFlowNames = items;

        await this.reload();

        this.bindAutocomplete('#bdsAutocomplete_returnFlow_filter', JSON.stringify(this.returnFlowCollection));
    }

    async removeStatus(items: any, removedItem: any): Promise<any> {
        this.statusCollection = this.statusCollection.concat(removedItem.item);
        this.filteredStatus = items;

        await this.reload();

        this.bindAutocomplete('#bdsAutocomplete_status_filter', JSON.stringify(this.statusCollection));
    }

    async clearAllFilters(): Promise<any> {
        this.filterTemplateName = '';
        this.filteredReturnFlowNames = [];
        this.filteredStatus = [];

        this.returnFlowCollection = this.returnFlowConstantCollection;
        this.statusCollection = this.statusConstantCollection;

        this.initializeAndBindAutocompleteList();

        this.notFound = false;
        this.notFoundWithFilter = false;

        await this.reload();
    }

    async reload() {
        this.startLoading();
        await this.query();
        this.stopLoading();
    }

    getMessageFromTemplate(template: MessageTemplate, addStyles: boolean = false): string {
        if (!template) {
            return '';
        }

        const header = template.Components.find((component: any) => component.Type === 'HEADER');
        const body = template.Components.find((component: any) => component.Type === 'BODY');
        const footer = template.Components.find((component: any) => component.Type === 'FOOTER');

        const message: string[] = [header?.Text, body?.Text, footer?.Text];

        const styleMessage = this.formatText(
            message.filter((messagePart: any) => messagePart)
                .join(ActiveMessageConstants.NEW_LINE)
                .replace(/\\n/g, ActiveMessageConstants.NEW_LINE),
            addStyles
        );

        return styleMessage;
    }

    formatText(text: string, addStyles: boolean): string {
        if (!text) {
            return '';
        }

        text = text.replace(/<[^>]+>/gm, '');

        if (addStyles) {
            text = text.replace(/\*([^*]+)\*/g, '<strong>$1</strong>');
            text = text.replace(/\_([^*]+)\_/g, '<span class="text-italic">$1</span>');
            text = text.replace(/\~([^*]+)\~/g, '<span class="text-line-through">$1</span>');
            text = text.replace(/\{\{\d+\}\}/g, '<span class="text-italic text-blue">$&</span>');
        } else {
            text = text.replace(/\*([^*]+)\*/g, '$1');
            text = text.replace(/\_([^*]+)\_/g, '$1');
            text = text.replace(/\~([^*]+)\~/g, '$1');
        }

        return text;
    }

    getFlowStateName(messageTemplateParams?: MessageTemplateParams): string {
        if (!messageTemplateParams || !messageTemplateParams.stateId) {
            if (this.singleReturnFlow) {
                return this.singleReturnFlow.value.name;
            }
            return '';
        }

        const flowState = this.returnFlowConstantCollection.find((state: any) => state.value?.id === messageTemplateParams.stateId);

        return flowState ? flowState.value.name : '';
    }

    selectEditReturnFlowValue = (messageTemplateWithParams: ApprovedMessageTemplatesWithParams): string => {
        const hasSavedStateId = !!messageTemplateWithParams.params.stateId;
        if (!hasSavedStateId && !messageTemplateWithParams.params.editingState) {
            return undefined;
        }

        const value = messageTemplateWithParams.params.isValid && hasSavedStateId
            ? messageTemplateWithParams.params.stateId
            : this.singleReturnFlow
                ? this.singleReturnFlow.value.id
                : undefined;

        return value;
    }

    selectEditReturnFlow = (event: any): void => {
        if (!this.hoverCurrentParams.params) {
            this.hoverCurrentParams.params = {};
        }

        this.hoverCurrentParams.params.editingState = event.detail?.value;
    }

    selectUpdateReturnFlow = (event: any): void => {
        this.updateReturnFlowValue = event.detail?.value;
    }

    clearEditInput(messageTemplateWithParams: ApprovedMessageTemplatesWithParams): void {
        const elementId = `#bdsAutocomplete_edit_form_return_flow_${messageTemplateWithParams.template.Id}`;
        const element: HTMLBdsAutocompleteElement = document.querySelector(elementId);
        element.value = undefined;

        this.$rootScope.$broadcast('ToggleDropdownItem');

        if (messageTemplateWithParams.params) {
            messageTemplateWithParams.params.editingState = undefined;

            if (this.singleReturnFlow) {
                messageTemplateWithParams.params.editingState = this.singleReturnFlow.value.id;
            }
        }

        this.editingParams = undefined;
        this.isDropDownOpened = false;
    }

    async applyReturnFlowEdit(messageTemplateWithParams: ApprovedMessageTemplatesWithParams): Promise<any> {
        this.startLoading();

        if (!this.hoverCurrentParams.params) {
            this.hoverCurrentParams.params = {
                isEnabled: false
            };
        }

        messageTemplateWithParams.params.templateId = messageTemplateWithParams.params.templateId ?? this.hoverCurrentParams.template.Id;
        messageTemplateWithParams.params.stateId = messageTemplateWithParams.params.editingState;
        messageTemplateWithParams.params.isValid = this.isValidFlowState(messageTemplateWithParams.params);

        this.SegmentService.createApplicationTrack({
            trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_EDIT_SETTINGS,
            application: this.application,
            payload: {
                templateType: this.getTemplateType(this.hoverCurrentParams.template)
            }
        });

        await this.ActiveMessageDeskSharedService.save(messageTemplateWithParams.params);

        this.clearEditInput(messageTemplateWithParams);
        this.stopLoading();
        await this.reload();
    }

    isValidFlowState(messageTemplateParams?: MessageTemplateParams): boolean  {
        if (!messageTemplateParams || !messageTemplateParams.stateId) {
            return true;
        }

        const flowState = this.returnFlowConstantCollection.find((state: any) => state.value?.id === messageTemplateParams.stateId);
        return flowState !== undefined;
    }

    toggleMessageTemplateWithParams = async (event: any): Promise<any> => {
        this.startLoading();

        await this.saveMessageTemplateWithParams(event.detail.checked);

        this.stopLoading();
    }

    async saveMessageTemplateWithParams(checked) {
        if (!this.hoverCurrentParams.params.stateId) {
            this.hoverCurrentParams.params = {
                stateId: this.singleReturnFlow ? this.singleReturnFlow.value.id : '',
                templateId: this.hoverCurrentParams.template.Id
            };
        }
        this.hoverCurrentParams.params.isEnabled = checked;

        this.SegmentService.createApplicationTrack({
            trackEvent: checked ? ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_ON : ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_OFF,
            application: this.application,
            payload: {
                templateType: this.getTemplateType(this.hoverCurrentParams.template)
            }
        });

        await this.ActiveMessageDeskSharedService.save(this.hoverCurrentParams.params);
    }

    isEnabled(approvedMessageTemplatesWithParams: ApprovedMessageTemplatesWithParams): boolean {
        return approvedMessageTemplatesWithParams.params !== undefined &&
            approvedMessageTemplatesWithParams.params.isEnabled;
    }

    isDropDownEditButtonEnabled(approvedMessageTemplatesWithParams: ApprovedMessageTemplatesWithParams): boolean {
        const hasParams = !!approvedMessageTemplatesWithParams.params;
        const isEditing = hasParams && !!approvedMessageTemplatesWithParams.params.editingState;

        return hasParams && isEditing;
    }

    canShowEditButton(templateId: string): boolean {
        return this.isDropDownOpened ?
            this.editingParams?.template?.Id === templateId :
            this.hoverCurrentParams?.template?.Id === templateId;
    }

    toggleTemplateMessageModal = async () => {
        this.openDetailModal = !this.openDetailModal;

        if (this.openDetailModal) {
            const model: string = await this.$translate('templateMessage.table.modal.model');
            this.modalTitle = `${model} "${this.hoverCurrentParams.template.Name}"`;
            this.modalBody = this.getMessageFromTemplate(this.hoverCurrentParams.template, true);

            this.SegmentService.createApplicationTrack({
                trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_SHOW,
                application: this.application,
                payload: {
                    templateType: this.getTemplateType(this.hoverCurrentParams.template)
                }
            });
        }
    }

    toggleStateIdUpdateModal = async () => {
        this.openStateIdUpdate = !this.openStateIdUpdate;

        if (this.openStateIdUpdate) {
            this.updateReturnFlowValue = this.singleReturnFlow ? this.singleReturnFlow.value.id : '';
            this.modalTitle = await this.$translate('templateMessage.table.modal.updateStateTitle');
        }
    }

    dropDownOpened = () => {
        this.isDropDownOpened = true;
        this.editingParams = this.hoverCurrentParams;
    }

    dropDownClosed = () => {
        this.isDropDownOpened = false;
        this.editingParams = undefined;
    }

    getTemplateType = (template: MessageTemplate) => {
        const headerComponent = template.Components.filter(c => c.Type.toUpperCase() == 'HEADER');

        if (headerComponent?.length == 1) {
            const format = headerComponent[0].Format;
            if (format) {
                return format.toLowerCase();
            }

            return 'text';
        }

        return '';
    }

    async messageTemplateUpdateStateId() {
        if (this.isUpdatingReturnFlow) {
            return;
        }

        this.startLoading();

        this.SegmentService.createApplicationTrack({
            trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_UPDATE_INVALID_STATEID,
            application: this.application
        });
        this.isUpdatingReturnFlow = true;

        try {
            const invalidMessageTemplates = this.approvedMessageTemplatesWithParamsCache.filter(
                (msgTemplate: ApprovedMessageTemplatesWithParams) => !msgTemplate.params.isValid);

            invalidMessageTemplates.forEach(async (msgTemplate: ApprovedMessageTemplatesWithParams) =>
                await this.updateSingleMessageTemplate(msgTemplate));

            this.BlipToastService.show('success', {
                msg: await this.$translate('templateMessage.table.modal.updateSuccess')
            });
        } catch (e) {
            this.SegmentService.createApplicationTrack({
                trackEvent: ActiveMessageSegmentEventsConstants.MESSAGE_TEMPLATE_UPDATE_INVALID_STATEID_ERROR,
                application: this.application
            });
            console.log(e);

            this.BlipToastService.show('danger', {
                title: await this.$translate('templateMessage.table.modal.error.title'),
                msg: await this.$translate('templateMessage.table.modal.error.description'),
                duration: BlipToastDuration.WITH_TITLE.NO_ACTION
            });
        }

        this.stopLoading();
        await this.reload();
        this.isUpdatingReturnFlow = false;
        this.openStateIdUpdate = false;
    }

    async updateSingleMessageTemplate(msgTemplate: ApprovedMessageTemplatesWithParams) {
        msgTemplate.params.stateId = this.updateReturnFlowValue;
        this.hoverCurrentParams = msgTemplate;
        await this.saveMessageTemplateWithParams(msgTemplate.params.isEnabled);
    }

    dropdownDirection = (template: ApprovedMessageTemplatesWithParams) => {
        const rowIndex = this.approvedMessageTemplatesWithParams.indexOf(template);

        if (this.approvedMessageTemplatesWithParams.length > 11 &&
            rowIndex > this.approvedMessageTemplatesWithParams.length - 4) {
            return 'top';
        }

        return 'bottom';
    }
}
