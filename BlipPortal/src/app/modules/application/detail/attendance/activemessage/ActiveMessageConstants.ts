export const ActiveMessageConstants = {
    // Commands domain
    DOMAIN_AUTHORITY_NAME: 'postmaster',
    DESK_DOMAIN: 'desk.msging.net',
    // URIs
    URI_GET_STATES: '/active-message/get-current-owner-flow-id-and-states?onlyDeskState=true',
    URI_GET_MESSAGE_TEMPLATE_PARAMS: '/active-message/message-templates-params',
    URI_SET_MESSAGE_TEMPLATE_PARAMS: '/active-message/message-templates-params',
    URI_GET_VALIDATE_ACTIVE_MESSAGE: '/active-message/validate-settings',
    URI_GET_VALIDATE_ROUTERID: '/active-message/validate-router',
    // MISC
    MIME_TYPE: 'application/vnd.iris.desk.messagetemplateparams+json',
    NEW_LINE: '\n',
    WHATSAPP_CONNECTED: '/active-message/is-whatsapp-channel-connected',
    DESK_POSTMASTER: '<EMAIL>'
};
