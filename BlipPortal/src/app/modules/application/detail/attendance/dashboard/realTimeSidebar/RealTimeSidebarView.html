<div id="node-content-tab"
    class="real-time-message-bar sidebar-content-component right-entrance-animation position-right fixed z-max">
    <div class="flex flex-column h-100 w-100">
        <div class="sidebar-content-header bp-c-white ph5 pv3">
            <div class="sidebar-helper-header">
                <div class="sidebar-helper-header__top mb4 flex flex-row justify-between items-end flex-nowrap">
                    <h3 class="mb0 box-title truncate w-70 pr2"
                        translate
                        translate-values="{ user: $ctrl.ticket.customerName }">modules.application.detail.attendance.analytics.page.thread.title</h3>
                    <div class="sidebar-helper-header__actions flex flex-row flex-shrink-0 items-center">
                        <bds-theme-provider theme="dark" class="flex">
                            <div ng-auth-write class="mr2">
                                <div alt="{{'transferTicket.tooltip.info' | translate}}">
                                    <div class="thread-button br-100 flex flex-row flex-shrink-0 items-center justify-center"
                                    ng-class="{'thread-button-disabled': $ctrl.ticket.status == 'ClosedAttendant' || $ctrl.ticket.status == 'ClosedClient'}"
                                    ng-click="$ctrl.onTransferTicket($ctrl.ticket, $event)">
                                        <bds-tooltip
                                            position="bottom-center"
                                            tooltip-text="{{::'transferTicket.tooltip.info' | translate}}">
                                            <bds-icon class="transfer-icon" name="transfer" theme="outline"></bds-icon>
                                        </bds-tooltip>
                                    </div>
                                </div>
                            </div>
                            <div ng-auth-write class="mr4">
                                <div alt="{{'closeTicket.tooltip' | translate}}">
                                    <div class="thread-button br-100 flex flex-row flex-shrink-0 items-center justify-center"
                                    ng-class="{'thread-button-disabled': $ctrl.ticket.status == 'ClosedAttendant' || $ctrl.ticket.status == 'ClosedClient'}"
                                    ng-click="$ctrl.onCloseTicket($ctrl.ticket, $event)">
                                        <bds-tooltip
                                            position="bottom-center"
                                            tooltip-text="{{::'closeTicket.tooltip' | translate}}">
                                            <bds-icon class="check-icon" name="check" theme="outline"></bds-icon>
                                        </bds-tooltip>
                                    </div>
                                </div>
                            </div>
                        </bds-theme-provider>
                        <div
                            alt="{{'utils.forms.close' | translate}}"
                            ng-click="$ctrl.close()"
                            class="h-100 flex flex-row items-center flex-shrink-0 realtime-close">
                            <i class="icon-close bp-lh-simple flex-shrink-0"></i>
                        </div>
                    </div>
                </div>
                <div class="divider-h flex"></div>
                <div class="mt3 flex flex-row items-center justify-between">
                    <div class="w-30 flex flex-column items-start">
                        <span class="w-100 box-subtitle truncate"
                            translate>modules.application.detail.attendance.analytics.page.thread.status.title</span>
                        <span class="w-100 box-subtitle truncate" translate>{{$ctrl.getStatusTranslation($ctrl.ticket.status)}}</span>
                    </div>
                    <div class="w-30 flex flex-column items-start">
                        <span class="w-100 box-subtitle truncate"
                            translate>modules.application.detail.attendance.analytics.page.thread.team</span>
                        <span class="w-100 box-subtitle truncate">{{$ctrl.teamName || '-'}}</span>
                    </div>
                    <div class="w-30 flex flex-column items-start">
                        <span class="w-100 box-subtitle truncate"
                            translate>modules.application.detail.attendance.analytics.page.thread.agent</span>
                        <span class="w-100 box-subtitle truncate">{{$ctrl.ticket.agentName || '-'}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div ng-if="$ctrl.isLoadingThread || $ctrl.isLoadingThreadSpinner" class="loader-container tc flex align-center justify-center">
            <bds-loading-spinner size="standard" color="main" class="ma-auto"></bds-loading-spinner>
        </div>
        <bds-button-icon 
            ng-show="$ctrl.showLoadMoreRecentButton && !($ctrl.isLoadingThread || $ctrl.isLoadingThreadSpinner)" 
            class="go-to-more-recent-button ma-auto" 
            variant="primary" 
            ng-click="$ctrl.goToMostRecentMessage()"
            icon="arrow-ball-down">
        </bds-button-icon>
        <div class="thread-body h-100 pa0">
            <thread-messages
                messages="$ctrl.realTimeMessages"
                is-loading-thread="$ctrl.isLoadingThread"
                scroll-to-bottom="true"
                load-more="$ctrl.loadMoreMessages($event)"></thread-messages>
        </div>
    </div>
</div>
