@import '~assets/scss/main';

.real-time-message-bar.sidebar-content-component {
    overflow: hidden;
    box-shadow: 0 0px 20px 0px #00000052;
    background-color: $color-surface-2;

    .loader-container {
        background-color: rgba(255, 255, 255, 0.5);
        z-index: 1;
        position: absolute;
        height: 100%;
        width: 100%;
    }

    .go-to-more-recent-button {
        position: absolute;
        bottom: 20px;
        left: 50%;
        margin-left: -25px;
        z-index: 1;
        opacity: 0.5;
    }

    .go-to-more-recent-button:hover {
        opacity: 1;
    }

    .sidebar-content-header {
        background-color: $color-content-default;
        height: 125px;

        .sidebar-helper-header__top {
            height: 31px;
            .realtime-close {
                height: 31px;
            }
        }
    }

    .thread-body {
        height: calc(100% - 125px);

        .messages {
            height: 100%;
            width: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 1*$m 2*$m 4*$m 3*$m;
        }
    }

    .collection .slideshow-container .slideshow-list {
        padding-left: 0;
        padding-right: 30px;
    }

    .icon-close {
        padding-top: 0;
        height: 16px;
    }
}
