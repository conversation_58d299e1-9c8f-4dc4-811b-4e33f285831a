import * as moment from 'moment';
import * as uuid from 'uuid';
import * as DefaultAvatar from 'assets/img/icons/avatar-default.png';
import { HistoryService } from 'modules/application/detail/attendance/history/HistoryService';
import './RealTimeSidebar.scss';
import { TicketStatusEnum, DashboardService } from 'modules/application/detail/attendance/dashboard/DashboardService';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { TeamService } from 'modules/application/detail/attendance/team/TeamService';
import { AttendanceConstants } from '../../AttendanceConstants';
import { translate } from 'angular';
import { ApplicationService2 } from 'modules/application/ApplicationService2';
import { asyncFetchMediaWithAuth } from 'modules/shared/helpers/Media';
import { MessagingHubFeatures } from 'modules/messaginghub/MessagingHubFeatures';
import { UserFeatures } from 'modules/application/detail/users/UserFeatures';
import AuthenticationService from 'modules/login/AuthenticationService';
import { CallsService } from 'modules/shared/calls/CallsService';
import { TranscriptionMfeService } from 'modules/shared/transcription/TranscriptionMfeService';
import { DashboardFeatures } from '../DashboardFeatures';
import { CallsFeatures } from 'modules/calls/CallsFeatures';
import { GeneralSettingsConstants } from '../../generalsettings/GeneralSettingsConstants';
import { DeskConfigurationService } from '../../../attendance/DeskConfigurationService';

enum StatusEnum {
    queued = 'modules.application.detail.attendance.analytics.page.thread.status.queued',
    assigned = 'modules.application.detail.attendance.analytics.page.thread.status.assigned',
    inAttendance = 'modules.application.detail.attendance.analytics.page.thread.status.inAttendance',
    transferred = 'modules.application.detail.attendance.analytics.page.thread.status.transferred',
    closed = 'modules.application.detail.attendance.analytics.page.thread.status.closed',
}

const TUNNEL_MSGING_DOMAIN = 'tunnel.msging.net';

export class RealTimeSidebarController {
    private realTimeMessages: any[];
    public threadDataReplied: any = {};
    private defaultAvatar: typeof DefaultAvatar;
    private canLoadMore: boolean = true;
    private take: number;
    private skip: string;
    private skipDate: string;
    private customerMsgingURI: string;
    private customerDeskURI: string;
    private botURI: string;
    private subscribed: boolean = false;
    private threadWrapper: any;
    private isLoadingThread: boolean;
    private isLoadingThreadSpinner: boolean = false;
    private showLoadMoreRecentButton: boolean = false;
    private scrollThread: any;
    private transcriptionData: Record<string, any> = {};
    public ownerId: string;
    public tunnelAlternativeId: string;
    public translations: any;
    public isGetMessagingHubCommandWithDelegation: boolean = false;
    public isDisableGetRepliedMessageById: boolean = false;
    public isAudioTranscriptionCopilotEnabled: boolean = false;
    public isCallRecordingTranscriptionEnabled: boolean = false;
    public isCareCopilotEnabled: boolean = false;
    public isTenantBotBlipCopilotEnabled: boolean = false;

    private loadMessagesTimeout: number;
    public teamName: string;

    constructor(
        public close,
        private $timeout,
        private $rootScope,
        private $scope,
        private TeamService: TeamService,
        private HistoryService: HistoryService,
        private DashboardService: DashboardService,
        private application,
        private $translate: translate.ITranslateService,
        private from,
        private to,
        private ticket,
        private onUpdateTicket,
        private onTransferTicket,
        private onCloseTicket,
        private MessagingHubService: MessagingHubService,
        private canTransferWithoutAgents: boolean,
        private ApplicationService2: ApplicationService2,
        private AuthenticationService: AuthenticationService,
        private CallsService: CallsService,
        private TranscriptionMfeService: TranscriptionMfeService,
        private DeskConfigurationService: DeskConfigurationService,
        private MESSAGINGHUBDOMAIN: string,
    ) {
        this.botURI = from;
        this.customerMsgingURI = to;
        this.customerDeskURI = `${encodeURIComponent(to)}@desk.msging.net`;
        this.realTimeMessages = [];
        this.take = 20;
        this.defaultAvatar = DefaultAvatar;
        this.$init();
    }

    async $init() {
        await this.loadFeatures();
        try {
            this.translations = await this.loadTranslations();
        } catch (e) {
            console.error(e);
        }

        const contactId = this.ticket.customerIdentity;
        const extra = {
            ticket_id: this.ticket.id,
            agent_identity: this.ticket.agentIdentity,
            team: this.ticket.team,
            bot_id: this.application.shortName,
            tenant_id: this.application.tenantId,
            cluster: this.application.cluster,
        };

        this.transcriptionData = {
            audioEnabled: this.isAudioTranscriptionEnabled(),
            callRecordingEnabled: this.isCallRecordingTranscriptionEnabled,
            onAsyncTranscribe: url => {
                const extraPayload = {...extra, source: 'copilot'};
                this.TranscriptionMfeService.show({ url, extraPayload, contactId });
                return '';
            },
            onOpenMfeModal: data => {
                const extraPayload = {...extra, source: data.source ?? 'calls'};
                this.TranscriptionMfeService.show({...data, extraPayload, contactId});
            },
        };

        try {
            if (this.customerMsgingURI.endsWith('@tunnel.msging.net')) {
                const data = await this.HistoryService.getTunnelAccount(this.customerMsgingURI);
                this.tunnelAlternativeId = data.alternativeAccount;
                this.ownerId = data.extras ? data.extras['tunnel.owner'] : undefined;
            }
            this.isLoadingThread = true;
            const items = await this.getThread();
            if (items) {
                this.realTimeMessages.unshift(...items.reverse());
            }
        } catch (e) {
            console.error(e);
        } finally {
            this.$timeout(() => {
                this.threadWrapper = document.getElementById(
                    'thread-wrapper',
                ) as HTMLDivElement;
                this.scrollThread = this.debounce(this.scrollThreadMessage.bind(this));
                this.threadWrapper.addEventListener('scroll', this.scrollThread);
            });
        }

        try {
            this.subscribeToMessagingEvents();
            const deregisterUpdateTicket = this.$rootScope.$on('UPDATE_TICKET', (_, { ticket }) => {
                this.updateTicket(ticket);
            });
            this.$scope.$on('$destroy', () => {
                this.unsubscribeToMessagingEvents();
                deregisterUpdateTicket();
                if (this.loadMessagesTimeout) {
                    clearTimeout(this.loadMessagesTimeout);
                }
            });
        } catch (e) {
            console.error(e);
        } finally {
            this.isLoadingThread = false;
        }
        this.updateIfTeamNameIsDirectTransfer(this.ticket.team);
    }

    async loadFeatures(): Promise<void> {
        const [
            isGetMessagingHubCommandWithDelegation,
            isDisableGetRepliedMessageById,
            isAudioTranscriptionCopilotEnabled,
            isCallRecordingTranscriptionEnabled,
            isCareCopilotEnabled,
            isTenantBotBlipCopilotEnabled
        ] = await Promise.all([
            MessagingHubFeatures.GetMessagingHubCommandWithDelegation(),
            UserFeatures.isDisableGetRepliedMessageById(),
            DashboardFeatures.isAudioTranscriptionCopilotEnabled(),
            CallsFeatures.isCallRecordingTranscriptionEnabled(),
            this.DeskConfigurationService.getDeskConfiguration(GeneralSettingsConstants.CARE_COPILOT_ENABLED, false),
            this.DeskConfigurationService.getDeskConfiguration(GeneralSettingsConstants.TENANT_BOT_BLIP_COPILOT_ENABLED, false)
        ]);
        this.isGetMessagingHubCommandWithDelegation = isGetMessagingHubCommandWithDelegation;
        this.isDisableGetRepliedMessageById = isDisableGetRepliedMessageById;
        this.isAudioTranscriptionCopilotEnabled = isAudioTranscriptionCopilotEnabled;
        this.isCallRecordingTranscriptionEnabled = isCallRecordingTranscriptionEnabled;
        this.isCareCopilotEnabled = isCareCopilotEnabled;
        this.isTenantBotBlipCopilotEnabled = isTenantBotBlipCopilotEnabled;
    }

    isAudioTranscriptionEnabled() {
        return this.isAudioTranscriptionCopilotEnabled
            && this.isCareCopilotEnabled
            && this.isTenantBotBlipCopilotEnabled;
    }

    scrollThreadMessage(event) {
        event.stopPropagation();
        const scrollIsOnBottom = ((event.srcElement.scrollHeight - event.target.clientHeight - event.srcElement.scrollTop) <= 1);
        this.$timeout(() => {
            this.showLoadMoreRecentButton = !scrollIsOnBottom;
        });
    }

    debounce(func, wait = 100) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(this, args);
            }, wait);
        };
    }

    async goToMostRecentMessage() {
        if (this.realTimeMessages && this.realTimeMessages.length > 0) {
            this.isLoadingThreadSpinner = true;
            this.showLoadMoreRecentButton = false;
            this.$timeout(() => {
                this.isLoadingThreadSpinner = false;
                this.loadMessagesTimeout = setTimeout(() => {
                    this.threadWrapper.scrollTop = this.threadWrapper.scrollHeight;
                });
            }, 700);
        }
    }

    async loadMoreMessages({ wrapper }) {
        try {
            this.isLoadingThreadSpinner = true;
            const last = wrapper.scrollHeight;
            const msgs = await this.getThread();
            if (msgs) {
                this.realTimeMessages.unshift(...msgs.reverse());
                this.loadMessagesTimeout = setTimeout(() => {
                    const curr = wrapper.scrollHeight;
                    wrapper.scrollTop = curr - last;
                });
            }
        } catch (err) {
            console.error(err);
        } finally {
            this.isLoadingThreadSpinner = false;
        }
    }

    async getThread() {
        if (!this.canLoadMore) {
            return;
        }

        if (this.realTimeMessages.length > 0) {
            this.skip = this.realTimeMessages[0].document.id;
            this.skipDate = this.realTimeMessages[0].document.date;
        }

        const getFromOriginator = this.ownerId && this.isGetMessagingHubCommandWithDelegation;
        const identity = getFromOriginator ? this.customerMsgingURI : this.tunnelAlternativeId || this.customerMsgingURI;
        const ownerIdentity = getFromOriginator ? undefined : this.ownerId;
        const { items } = await this.HistoryService.getThreads(
            identity,
            this.skip,
            this.skipDate,
            this.take,
            ownerIdentity,
            getFromOriginator,
        );

        if (items.length == 0) {
            this.canLoadMore = false;
            return;
        }

        const authorization = this.isGetMessagingHubCommandWithDelegation
            ? `${this.AuthenticationService.auth.tokenType} ${this.AuthenticationService.auth.token}`
            : this.ApplicationService2.getAuthHeaderValue(this.application);

        const asyncFetchMediaFunction = asyncFetchMediaWithAuth(this.customerMsgingURI, authorization);

        const isGetTicketHistoryMergedEnabled = await UserFeatures.isShowingActiveMessageTemplateContent();

        const messages = items.map((item) => {
            if (isGetTicketHistoryMergedEnabled) {
                const contentType = item?.content?.type;
                if (contentType) {
                    item.content.type = (contentType === 'template' ? 'template-content' : contentType);
                }
            }

            const message = {
                document: {
                    id: item.id,
                    type: item.type,
                    content: item.content,
                    date: item.date,
                    direction: item.direction,
                    metadata: item.metadata,
                },
                editable: false,
                deletable: false,
                translations: this.translations,
                position: item.direction === 'sent' ? 'left' : 'right',
                date: moment(item.date).format('L - HH:mm'),
                hideOptions: true,
                photo:
                    item.direction === 'sent'
                        ? this.application.imageUri || this.defaultAvatar
                        : false,
                ticket: item.type === 'application/vnd.iris.ticket+json' ? item.content.id : '',
                asyncFetchMedia: asyncFetchMediaFunction,
                onAsyncFetchSession: undefined,
                transcription: this.transcriptionData,
            };

            if (item.type === this.CallsService.CALLS_MEDIA_MESSAGE_TYPE) {
                message.onAsyncFetchSession = this.CallsService.onAsyncFetchSession(`${this.application.shortName}@${this.MESSAGINGHUBDOMAIN}`);
            }
            return message;
        });

        if (!this.isDisableGetRepliedMessageById) {
            await this.fillRepliedMessages(messages, identity, ownerIdentity, getFromOriginator);
        }
        return messages;
    }

    async fillRepliedMessages(items: any[], identity: string, ownerIdentity: string, getFromOriginator: boolean) {
        const replies = items.filter(x => x.document.type === 'application/vnd.lime.reply+json');
        replies.forEach(async reply => {
            const repliedMessageId = reply.document.content.inReplyTo.id;
            const repliedMessage = this.threadDataReplied[repliedMessageId];
            if (!repliedMessage) {
                const response = await this.HistoryService.getThreadMessage(ownerIdentity, identity, repliedMessageId, getFromOriginator);
                if (response) {
                    this.threadDataReplied[repliedMessageId] = this.formatRepliedMessageContent(response);
                } else {
                    this.threadDataReplied[repliedMessageId] = {
                        id: repliedMessageId,
                        failedToLoad: true
                    };
                }
            }
            reply.document.content.inReplyTo = this.threadDataReplied[repliedMessageId];
        });
    }

    formatRepliedMessageContent(repliedMessageDocument: any) {
        let type = repliedMessageDocument.type;
        let content = repliedMessageDocument.content;
        if (type ===  'application/vnd.lime.reply+json') {
            type = repliedMessageDocument.content.replied.type;
            content = repliedMessageDocument.content.replied.value;
        }
        return  {
            id: repliedMessageDocument.id,
            value: content,
            type: type,
            direction: repliedMessageDocument.direction,
        };
    }

    async updateTicket(newTicket) {
        if (!newTicket) {
            return;
        }

        let agentName;
        try {
            agentName = await this.getTicketAgentName(newTicket.agentIdentity);
        } catch (e) {
            console.error(e);
            agentName = undefined;
        }

        const oldTicket = this.ticket;

        this.ticket = {
            ...oldTicket,
            ...newTicket,
            agentName
        };

        this.onUpdateTicket(this.ticket);
    }

    async getTicketAgentName(agentIdentity) {
        if (!agentIdentity) {
            return undefined;
        }
        try {
            const { fullName } = await this.TeamService.getAccount(
                agentIdentity,
            );
            return fullName || undefined;
        } catch (e) {
            return decodeURIComponent(
                agentIdentity.split('@')[0],
            );
        }
    }

    isTicketWaitingForAnswer() {
        return !this.ticket.attendanceTime && !this.ticket.hasFirstResponse && (this.ticket.status === 'Waiting' || this.ticket.status === 'Assigned');
    }

    async getTicket(ticketId: string) {
        try {
            const ticket = await this.DashboardService.getTicket(
                this.application,
                ticketId
            );

            this.updateTicket(ticket);
        } catch (e) {
            console.error(e);
        }
    }

    processMessage(item) {
        const sentByUser = item && item.from ? item.from.startsWith(this.customerMsgingURI) : false;
        if (item.isChatMessage && !sentByUser && this.isTicketWaitingForAnswer()) {
            const ticket = this.ticket;
            ticket.hasFirstResponse = true;
            ticket.status = 'Open';
            this.updateTicket(ticket);
            this.getTicket(this.ticket.id);
        }

        const authorization = this.isGetMessagingHubCommandWithDelegation
            ? `${this.AuthenticationService.auth.tokenType} ${this.AuthenticationService.auth.token}`
            : this.ApplicationService2.getAuthHeaderValue(this.application);

        const asyncFetchMediaFunction = asyncFetchMediaWithAuth(this.customerMsgingURI, authorization);

        const message = {
            document: {
                id: item.id,
                type: item.type,
                content: item.content,
            },
            editable: false,
            deletable: false,
            translations: this.translations,
            position: sentByUser ? 'right' : 'left',
            date: moment(item.date).format('L - HH:mm'),
            hideOptions: true,
            photo:
                (sentByUser || item.type === 'application/vnd.iris.ticket+json')
                    ? false
                    : this.application.imageUri || this.defaultAvatar,
            ticket:
                item.type === 'application/vnd.iris.ticket+json' ? item.content.id : '',
            asyncFetchMedia: asyncFetchMediaFunction,
            onAsyncFetchSession: undefined,
            transcription: this.transcriptionData,
        };

        if (item.type === this.CallsService.CALLS_MEDIA_MESSAGE_TYPE) {
            message.onAsyncFetchSession = this.CallsService.onAsyncFetchSession(`${this.application.shortName}@${this.MESSAGINGHUBDOMAIN}`);
        }

        return message;
    }

    async loadTranslations(): Promise<any> {
        return {
            messageTemplate: await this.$translate(
                'utils.misc.messageTemplate',
            ),
            audioTranscription: {
                action: await this.$translate(
                    'utils.misc.audioTranscription.action',
                ),
                loading: await this.$translate(
                    'utils.misc.audioTranscription.loading',
                ),
                fullTranscription: await this.$translate(
                    'utils.misc.audioTranscription.fullTranscription',
                ),
                title: await this.$translate(
                    'utils.misc.audioTranscription.title',
                ),
                readMore: await this.$translate(
                    'utils.misc.audioTranscription.readMore',
                ),
                readLess: await this.$translate(
                    'utils.misc.audioTranscription.readLess',
                ),
            },
            userWaitingAttendance: await this.$translate(
                'utils.misc.userWaitingAttendance',
            ),
            replyText: await this.$translate(
                'utils.misc.replyText',
            ),
            callsPermissionRequestBodyTitle: await this.$translate(
                'utils.misc.calls.callsPermissionRequestBodyTitle',
            ),
            callsPermissionRequestBodyText: await this.$translate(
                'utils.misc.calls.callsPermissionRequestBodyText',
            ),
            callsPermissionRequestLeftActionLabel: await this.$translate(
                'utils.misc.calls.callsPermissionRequestLeftActionLabel',
            ),
            callsPermissionRequestRightActionLabel: await this.$translate(
                'utils.misc.calls.callsPermissionRequestRightActionLabel',
            ),
            callsPermissionReplyAcceptText: await this.$translate(
                'utils.misc.calls.callsPermissionReplyAcceptText',
            ),
            callsPermissionReplyRejectText: await this.$translate(
                'utils.misc.calls.callsPermissionReplyRejectText',
            )
        };
    }

    filterAndTreatCommand(command) {
        if (!command || command.method === 'get') {
            return;
        }

        if (command.type === 'application/vnd.lime.message+json') {
            if (!command.resource) {
                return;
            }

            if (command.resource.type === 'application/vnd.lime.redirect+json') {
                const resource = command.resource.content.context.value;
                if (!resource || resource.status !== 'ClosedAttendant') {
                    return;
                }
                const closedTicket = {
                    ...command.resource.content.context.value
                };
                closedTicket.hasFirstResponse = false;
                this.updateTicket(closedTicket);
            } else if (command.resource.type === 'application/vnd.iris.ticket+json' && command.resource.content.status === 'Transferred') {
                //Detected ticket transferred from BLiP Desk
                const ticket = {
                    ...command.resource.content
                };

                const commandInfo = {
                    id: command.resource.id,
                    type: command.resource.type
                };

                this.treatTransferredTicket(ticket, commandInfo);
            } else if (command.resource.type === 'application/vnd.lime.chatstate+json' ||
                (command.uri.endsWith(this.customerDeskURI) && !command.resource.from.includes(TUNNEL_MSGING_DOMAIN))) {
                return;
            } else {
                const message = command.resource;
                message.isChatMessage = true;
                this.addMessageToChat(message);
            }
        } else if (command.type === 'application/vnd.iris.ticket+json') {
            if (!command.resource || command.resource.status === 'Open') {
                return;
            }

            //Detected ticket transferred from BLiP Portal
            const ticket = {
                ...command.resource
            };

            const commandInfo = {
                id: command.id,
                type: command.type
            };

            this.treatTransferredTicket(ticket, commandInfo);
        }
    }

    async treatTransferredTicket(newTicket: any, command: any) {
        if (newTicket.sequentialId < this.ticket.sequentialId) {
            return;
        }
        if (newTicket.sequentialId === this.ticket.sequentialId || newTicket.status === 'Transferred') {
            try {
                const parentSequentialId = newTicket.parentSequentialId || newTicket.sequentialId;
                const latestTicket = await this.DashboardService.getTicketByParentId(
                    this.application,
                    parentSequentialId
                );
                newTicket = latestTicket;
            } catch (e) {
                console.error(e);
                return;
            }
        } else {
            newTicket.hasFirstResponse = false;
            newTicket.agentIdentity = undefined;
            newTicket.agentName = undefined;
        }
        const transferMessage = {
            id: command.id,
            type: command.type,
            content: newTicket
        };
        this.addMessageToChat(transferMessage);
        this.updateTicket(newTicket);
    }

    addMessageToChat(message) {
        const isMessageAlreadyInChat = this.realTimeMessages.find((m) => {
            return m.id === message.id;
        });
        if (isMessageAlreadyInChat) {
            return;
        }
        this.realTimeMessages = this.realTimeMessages.concat(
            this.processMessage(message)
        );
    }

    subscribeToMessagingEvents() {
        if (this.subscribed) {
            return;
        }

        this.MessagingHubService.addCommandReceiver(true, (command) => {
            this.filterAndTreatCommand(command);
        });

        this.MessagingHubService.sendCommand({
            id:  uuid.v4(),
            method: 'subscribe',
            uri: `lime://${this.botURI}/pipeline/sent-message/${this.customerMsgingURI}?local=false`
        });

        this.MessagingHubService.sendCommand({
            id:  uuid.v4(),
            method: 'subscribe',
            uri: `lime://${this.botURI}/pipeline/received-message/${this.customerMsgingURI}?local=false`
        });

        this.MessagingHubService.sendCommand({
            id:  uuid.v4(),
            method: 'subscribe',
            uri: `lime://${this.botURI}/pipeline/received-message/${this.customerDeskURI}?local=false`
        });

        this.subscribed = true;
    }

    unsubscribeToMessagingEvents() {
        if (!this.subscribed) {
            return;
        }

        this.MessagingHubService.sendCommand({
            id:  uuid.v4(),
            method: 'unsubscribe',
            uri: `lime://${this.botURI}/pipeline/sent-message/${this.customerMsgingURI}`
        });

        this.MessagingHubService.sendCommand({
            id:  uuid.v4(),
            method: 'unsubscribe',
            uri: `lime://${this.botURI}/pipeline/received-message/${this.customerMsgingURI}`
        });

        this.MessagingHubService.sendCommand({
            id:  uuid.v4(),
            method: 'unsubscribe',
            uri: `lime://${this.botURI}/pipeline/received-message/${this.customerDeskURI}`
        });

        this.MessagingHubService.clearCommandReceivers();
        this.subscribed = false;
    }

    getStatusTranslation(status) {
        switch (status) {
            case TicketStatusEnum.Waiting:
            case TicketStatusEnum.Assigned:
            case TicketStatusEnum.Transferred:
                return StatusEnum.queued;
            case TicketStatusEnum.Open:
                return (this.ticket.attendanceTime || this.ticket.hasFirstResponse) ? StatusEnum.inAttendance : StatusEnum.assigned;
            case TicketStatusEnum.ClosedAttendant:
            case TicketStatusEnum.ClosedClient:
                return StatusEnum.closed;
            default:
                return '-';
        }
    }

    public async updateIfTeamNameIsDirectTransfer(team: string): Promise<void> {
        this.teamName = team === AttendanceConstants.DIRECT_TRANSFER_TEAM ? await this.$translate('directTransfer') : team;
    }
}
