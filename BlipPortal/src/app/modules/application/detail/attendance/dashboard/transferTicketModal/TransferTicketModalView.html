<div class="modal transfer-ticket-modal z-max">
    <div class="modal-overlay" ng-click="$ctrl.close()"></div>
    <div class="modal-dialog modal-sm layout-center-y">
        <div class="modal-toolbar">
            <button ng-click="$ctrl.close()" type="button" class="no-style">
                <i class="icon-close bp-c-rooftop"></i>
            </button>
        </div>
        <form ng-submit="$ctrl.closeModal()">
            <div class="modal-content-container">
                <div class="icon-container">
                    <img class="icon-transfer" src="/assets/img/transfer-arrow.svg">
                    <bds-illustration  class="icon-smile" type="blip-solid" name="smiley"></bds-illustration>
                </div>
                <div class="select-ticket-container">
                    <span class="title-field" translate>transferTicket.title</span>
                    <div class="radio-field" ng-if="$ctrl.isAttendantTransferEnabled">
                        <bds-radio label="{{ 'transferTicket.radio.team' | translate }}" ng-checked="$ctrl.isTeam"
                        custom-events="[{ event:'bdsClickChange', cb: $ctrl.updateTeam }]"></bds-radio>
                        <bds-radio label="{{ 'transferTicket.radio.attendant' | translate }}" ng-checked="!$ctrl.isTeam"
                        custom-events="[{ event:'bdsClickChange', cb: $ctrl.updateTeam }]"></bds-radio>
                    </div>
                    <span class="only-team-text-content" ng-if="!$ctrl.isAttendantTransferEnabled" translate>transferTicket.description</span>
                    <div class="select-field">
                        <div class="display-list" ng-if="$ctrl.isTeam">
                            <bds-autocomplete
                                placeholder="{{ 'transferTicket.select.placeholderTeam' | translate }}"
                                options="{{$ctrl.currentList}}"
                                custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.onSelectedItemUpdate }]"
                            ></bds-autocomplete>
                        </div>
                        <div class="display-list" ng-if="!$ctrl.isTeam">
                            <bds-autocomplete
                                placeholder="{{ 'transferTicket.select.placeholderAttendant' | translate }}"
                                options="{{$ctrl.currentList}}"
                                custom-events="[{ event:'bdsSelectedChange', cb: $ctrl.onSelectedItemUpdate }]"
                            ></bds-autocomplete>
                        </div>
                    </div>
                </div>
            </div>
            <div class="buttons-container">
                <bds-button ng-click="$ctrl.close()" variant="secondary" translate>utils.forms.cancel</bds-button>
                <bds-tooltip position="top-center"
                tooltip-text="{{ $ctrl.invalidTooltipText }}" ng-disabled="!$ctrl.selectedItemIsInvalid">
                    <bds-button class="primary-button" 
                    ng-disabled="$ctrl.isLoading || !$ctrl.selectedItem || $ctrl.selectedItemIsInvalid || (!$ctrl.isTeam && !$ctrl.isAttendantTransferEnabled)"
                    type="submit" variant="primary" translate>button.transfer</bds-button>
                </bds-tooltip>
            </div>
        </form>
    </div>
</div>
