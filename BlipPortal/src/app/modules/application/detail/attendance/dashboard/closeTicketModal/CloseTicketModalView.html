<div class="modal close-modal z-max">
    <div class="modal-overlay" ng-click="$ctrl.close()"></div>
    <div class="modal-dialog modal-sm layout-center-y">
        <div class="modal-toolbar">
            <button ng-click="$ctrl.close()" type="button" class="no-style">
                <i class="icon-close bp-c-rooftop"></i>
            </button>
        </div> 
        <div class="modal-content-container">
            <div class="icon-container">
                <bds-illustration  class="icon-sleepy" type="blip-solid" name="sleepy"></bds-illustration>
            </div>
            <div class="select-ticket-container">
                <span class="title-field" translate>closeTicket.title</span>
                <span class="only-team-text-content" translate>Ticket #{{$ctrl.ticket.sequentialId}}</span>
                <div class="w-70" ng-if="$ctrl.tags.length === 0 || !$ctrl.hasTags">
                    <p class="bp-fs-6 mb2" translate>closeTicket.detail.noTags</p>
                    <p class="bp-fs-6 bp-fw-bold mb0" translate>closeTicket.continue</p>
                </div>
                <div class="select-field">
                    <div class="display-list" ng-if="$ctrl.tags.length > 0 && $ctrl.hasTags">
                        <span class="bp-fs-6" translate>closeTicket.detail.withTags</span>
                        <blip-tags
                        ng-model="$ctrl.inputTags" placeholder="{{ 'closeTicket.tagSelect.placeholder' | translate }}"
                        can-add-options="false"
                        can-change-background="false" can-remove-tags="true" options="$ctrl.tags"
                        on-tag-added="$ctrl.updateTagsInput()" on-tag-removed="$ctrl.updateTagsInput()" mode="full"></blip-tags>
                    </div>
                </div>
            </div>
        </div>
        <div class="buttons-container z-0">
            <bds-button variant="secondary" ng-click="$ctrl.close(false)" translate>utils.forms.cancel</bds-button>
            <bds-button ng-click="$ctrl.close($ctrl.selectedTags || true)" loading="$ctrl.isLoading" ng-disabled="$ctrl.isLoading || ($ctrl.mustInputTag && !$ctrl.inputTags.length)"
            class="primary-button" button-value="{{'utils.forms.closeTicket' | translate}}"
            type="submit" variant="primary"></bds-button>
        </div>
    </div>
</div>
