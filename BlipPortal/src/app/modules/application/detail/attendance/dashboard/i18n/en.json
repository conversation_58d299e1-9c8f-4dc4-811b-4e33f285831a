{"monitoring": "Monitoring", "filterBy": "Filter by:", "team": "Line", "teamPlaceholder": "Enter the name of the queue", "attendant": "Agent", "attendantPlaceHolder": "Enter the name of the agent", "ticketsPerHour": {"title": "Open tickets per hour", "notFound": "No tickets were open today."}, "transferTicket": {"detail": "Choose the line that will receive this service. <br/> The transfer generates a new ticket number.", "teamSelect": {"label": "Line", "placeholder": "Select a line", "noAgentsAvailable": "No agents available", "agentAvailable": "1 agent available", "agentsAvailable": "{{count}} agents available"}, "title": "Transfer ticket", "modalTitle": "Transfer ticket", "description": "Pick a line to receive this ticket.", "radio": {"team": "Line", "attendant": "Agent"}, "select": {"label": "Line", "placeholderTeam": "Select line", "placeholderAttendant": "Select agent", "agentAvailable": "{{count}} Available", "agentsAvailable": "{{count}} Available", "attendantOnline": "Available", "attendantAbsent": "Unavailable"}, "errorMsg": {"1": "Error while loading transfer modal. Please, try again.", "2": "Cannot transfer to lines without available agents."}, "tooltip": {"info": "Transfer", "error": {"offlineAgents": "You can only transfer tickets to online agents", "teamWithoutOnlineAgents": "It is not possible to transfer to lines with no online agents"}}}, "closeTicket": {"title": "Close ticket", "tooltip": "Close", "detail": {"noTags": "Closing a ticket resets user interactions with the bot. <br/> New interactions generate a new Ticket.", "withTags": "Choose the tags for the attendance."}, "continue": "Do you want to continue?", "tagSelect": {"label": "Tags", "placeholder": "Select the tags"}}, "errors": {"refreshData": "Error while refreshing monitoring data.", "refreshChart": "Error while refreshing chart."}, "disableAutoRefreshMonitoring": {"describe": "The automatic updates have been <strong>disabled</strong> to upgrade performance and reduce loading time. Update your data at the “refresh” button or ", "click": "click here"}, "viewMore": "view more", "searchTickets": "Search tickets, attendants or lines", "directTransfer": "Direct transfer", "button": {"transfer": "Transfer ticket"}, "helper": {"title": "What is the monitoring?", "body": "Manage your operation in real time, tracking metrics and service tickets.", "doc": {"text": "See docs", "link": ""}, "confirm": "OK. I understood"}, "search": {"ticket": "Search ticket"}, "attendantStatus": {"online": "Online", "pause": "Away", "invisible": "Invisible", "offline": "Offline"}, "attendantStatusCall": {"video": "In a video call", "voice": "In a phone call"}, "customerFilter": {"label": "Contact", "placeholder": "Type the contact’s name, email or phone", "placeholderLike": "Type part of the contact’s name, email or phone"}, "tableHeader": {"customer": "Client"}}