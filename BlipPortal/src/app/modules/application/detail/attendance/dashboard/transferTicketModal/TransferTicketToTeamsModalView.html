<div class="modal transfer-modal z-max">
    <div class="modal-overlay" ng-click="$ctrl.close()"></div>
    <div class="modal-dialog modal-sm layout-center-y">
        <div class="modal-toolbar">
            <button ng-click="$ctrl.close()" type="button" class="no-style">
                <i class="icon-close bp-c-rooftop"></i>
            </button>
        </div>
        <form class="ma0" name="$ctrl.attendantForm" ng-submit="$ctrl.close($ctrl.selectedTeam)">
            <div class="modal-body mb4 flex flex-column items-center">
                <h1 class="bp-c-city mb2" translate>transferTicket.title</h1>
                <h2 class="bp-c-rooftop mb4 bp-fs-5" translate>Ticket #{{$ctrl.ticket.sequentialId}}</h1>
                <span class="bp-c-rooftop bp-fs-6 mb3" translate>transferTicket.detail</span>
                <div class="w-70">
                    <blip-select
                        ng-model="$ctrl.selectedTeam"
                        label="{{ 'transferTicket.teamSelect.label' | translate }}"
                        mode="autocomplete"
                        placeholder="{{ 'transferTicket.teamSelect.placeholder' | translate }}"
                        can-add-options="false"
                        options="$ctrl.teams"/>
                    <span ng-class="{'disabled': !$ctrl.selectedTeam || !$ctrl.isInvalidTeam()}"
                    class="ma1 bp-fs-7 bp-c-warning fl" translate> transferTicket.errorMsg.2 </span>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="bp-btn bp-btn--text-only bp-btn--city mr3" ng-click="$ctrl.close()" translate>utils.forms.cancel</button>
                <button loading="$ctrl.isLoading" ng-disabled="$ctrl.isLoading || !$ctrl.selectedTeam || $ctrl.isInvalidTeam()"
                    type="submit" class="bp-btn bp-btn--bot" button-value="{{'utils.forms.transfer' | translate}}"></button>
            </div>
        </form>
    </div>
</div>
