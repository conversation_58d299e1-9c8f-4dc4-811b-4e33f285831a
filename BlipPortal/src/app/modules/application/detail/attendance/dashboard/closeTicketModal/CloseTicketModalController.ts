import { Ticket } from 'modules/application/detail/attendance/models/Ticket';

import '../Modal.scss';

export class CloseTicketModalController {
    public tags: any[];
    public inputTags: any[];
    public selectedTags: any[];
    public hasTags: boolean;
    public isTagsRequired: boolean;
    public canCloseTicket: boolean;
    private mustInputTag: boolean;

    constructor(
        public close,
        public ticket: Ticket,
        tags: any[],
        hasTags: boolean,
        isTagsRequired: boolean
    ) {
        this.tags = tags;
        this.selectedTags = [];
        this.hasTags = hasTags;
        this.isTagsRequired = isTagsRequired;
        this.mustInputTag = this.isTagsRequired && this.tags.length > 0;
        this.inputTags = [];
        this.init();
    }

    init() {
        if (!this.ticket) {
            this.close();
        }
    }

    updateTagsInput() {
        this.selectedTags = this.inputTags.map(({ label }) => label);
    }
}
