import { Ticket } from 'modules/application/detail/attendance/models/Ticket';
import { Team } from 'modules/application/detail/attendance/models/Team';
import '../Modal.scss';
import './TransferTicketToTeamsModalView.scss';
import { translate } from 'angular';

export class TransferTicketToTeamsModalController {
    public teams: { label: string, value: string, description: string, onlineAgents: number }[];
    public selectedTeam: string;

    constructor(
        private $translate: translate.ITranslateService,
        public close: any,
        public ticket: Ticket,
        teams: Team[],
        public canTransferWithoutAgents: boolean = true,
    ) {
        this.init(teams);
    }

    async init(teams: Team[]) {
        if (!this.ticket) {
            this.close();
        }

        const teamPromises = teams.map(async (team) => {
            return {
                label: team.name || '',
                value: team.name || '',
                description: await this.getAgentsOnlineTranslation(team.agentsOnline),
                onlineAgents: team.agentsOnline,
            };
        });

        this.teams = await Promise.all(teamPromises);
    }

    async getAgentsOnlineTranslation(agentsOnline: number): Promise<string> {
        try {
            if (agentsOnline === 0) {
                return await this.$translate(
                    'transferTicket.teamSelect.noAgentsAvailable'
                );
            } else if (agentsOnline === 1) {
                return await this.$translate(
                    'transferTicket.teamSelect.agentAvailable'
                );
            } else if (agentsOnline > 1) {
                return await this.$translate(
                    'transferTicket.teamSelect.agentsAvailable',
                    { count: agentsOnline }
                );
            }
        } catch (e) {
            return '';
        }

        return '';
    }

    isInvalidTeam() {
        if (this.selectedTeam && !this.canTransferWithoutAgents) {
            const team = this.teams.find(t => t.value === this.selectedTeam);
            return team.onlineAgents === 0;
        }
        return false;
    }
}
