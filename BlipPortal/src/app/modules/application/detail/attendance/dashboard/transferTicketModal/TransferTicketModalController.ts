import { Ticket } from 'modules/application/detail/attendance/models/Ticket';
import { Team } from 'modules/application/detail/attendance/models/Team';
import { Attendant } from 'modules/application/detail/attendance/models/Attendant';
import '../Modal.scss';
import './TransferTicketModalView.scss';
import { Option } from 'blip-ds/dist/types/components/selects/select-interface';
import { IScope, translate } from 'angular';
import { AttendantStatus } from '../attendantStatus';

export interface TransferOption extends Option {
    isActive: boolean;
}

export class TransferTicketModalController {
    public teams: TransferOption[];
    public attendants: TransferOption[];
    public currentList: string;
    public selectedItem = undefined;
    public isTeam = true;
    public selectedItemIsInvalid: boolean;
    public invalidTooltipText: string;

    constructor(
        public $translate: translate.ITranslateService,
        public close: any,
        public ticket: Ticket,
        teams: Team[],
        attendants: Attendant[],
        public isAttendantTransferEnabled: boolean,
        public canTransferWithoutAgents: boolean,
        private $rootScope: IScope
    ) {
        this.init(teams, attendants);
    }

    async init(teams: Team[], attendants: Attendant[]): Promise<void> {
        if (!this.ticket) {
            this.close();
        }
        await this.fillTeams(teams);
        await this.fillAttendants(attendants);
        this.setCurrentList();
    }

    private setCurrentList(): void {
        this.currentList = this.isTeam ? JSON.stringify(this.teams) : JSON.stringify(this.attendants);
    }

    private async fillTeams(teams: Team[]): Promise<void> {
        const teamPromises: Promise<TransferOption>[] = teams.map(async (team) => {
            return {
                label: team.name,
                value: team.name,
                isActive: team.agentsOnline > 0,
                status: team.agentsOnline === 1
                    ? await this.$translate('transferTicket.select.agentAvailable', { count: team.agentsOnline })
                    : await this.$translate('transferTicket.select.agentsAvailable', { count: team.agentsOnline })
            };
        });

        this.teams = await Promise.all(teamPromises);
    }

    private async fillAttendants(attendants: Attendant[]): Promise<void> {
        const attendantPromises: Promise<TransferOption>[] = attendants ? attendants.map(async (attendant) => {
            const isActive = attendant.status === AttendantStatus.ONLINE;
            return {
                value: attendant.identity,
                label: attendant.fullName || attendant.email.split('@')[0],
                isActive,
                status: isActive
                ? await this.$translate('transferTicket.select.attendantOnline')
                : await this.$translate('transferTicket.select.attendantAbsent')
            };
        }) : [];

        this.attendants = await Promise.all(attendantPromises);
    }

    public async isInvalidTeam(): Promise<void> {
        if (this.selectedItem && !this.canTransferWithoutAgents) {

            if (this.isTeam) {
                this.selectedItemIsInvalid = !this.teams.find(team => team.value === this.selectedItem).isActive;

            } else if (!this.isTeam) {
                this.selectedItemIsInvalid = !this.attendants.find(attendant => attendant.value === this.selectedItem).isActive;

            }
            if (this.selectedItemIsInvalid) {
                this.invalidTooltipText = this.isTeam ? await this.$translate('transferTicket.tooltip.error.teamWithoutOnlineAgents') : await this.$translate('transferTicket.tooltip.error.offlineAgents');
                return;
            }
        }
        this.selectedItemIsInvalid = false;
    }

    onSelectedItemUpdate = async (event) => {
        this.selectedItem = event.detail?.value;
        await this.isInvalidTeam();
    }

    updateTeam = () => {
        this.isTeam = !this.isTeam;
        this.setCurrentList();
        this.selectedItem = undefined;
        this.$rootScope.$apply();
    }

    closeModal = () => {
        this.close(
            {
                recipient: this.selectedItem,
                isTeam: this.isTeam
            }
        );
    }
}
