@import "~assets/scss/variables";

.transfer-ticket .disabled {
    visibility: hidden;
    cursor: default;
}

.modal.transfer-ticket-modal {

    .modal-dialog {
      overflow-y: unset;

      &.modal-sm {
        width: 656px;
        height: 432px;
        border-radius: 8px;
        justify-content: normal;
        position: relative;
        &::before {
          content: "";
          left: 0;
          top: 0;
          bottom: 0;
          position: absolute;
          background: url('../../../../../../assets/img/waves.svg') no-repeat;
          height: 100%;
          background-size: 100%;
          width: 62%;
          z-index: -1;
          border-radius: 8px 0 0 8px;
        }

        .modal-content-container {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          height: 190px;
          width: 100%;
          vertical-align: top;
          margin-top: 54px;
          margin-left: 6px;
          position: relative;
          z-index: 1;

          .icon-container {
              display: flex;
              flex-direction: row;

              .icon-smile {
                  background-repeat: no-repeat;
                  width: 156px;
                  height: 168px;
                  outline: none;
                  margin-top: 25px;
              }

              .icon-transfer{
                  background-repeat: no-repeat;
                  width: 56px;
                  height: 56px;
                  align-self: flex-start;
                  border: 0;
                  outline: none;
              }
          }

          .select-ticket-container {
              display: flex;
              flex-direction: column;
              padding: 30px 46px 0 0;

              .title-field {
                  align-self: flex-start;
                  font-weight: bold;
                  font-size: 20px;
                  line-height: 28px;
                  color: $color-content-default;
                  width: 295px;
                  padding-bottom: 5px;
              }

              .radio-field {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                width: 185px;
              }

              .only-team-text-content {
                width: 307px;
                font-size: 14px;
                line-height: 22px;
                color: $color-content-default;
                padding-top: 5px;
              }

              .select-field {
                height: 40px;
                padding-top: 14px;

                .display-list{
                  position: fixed;
                  width: 279px;
                }
              }
          }
        }

        .buttons-container {
          display: flex;
          flex-direction: row;
          padding-top: 56px;
          padding-right: 2px;
          justify-content: flex-end;

          bds-tooltip {
            margin-left: 8px;
          }

          bds-button {
            z-index: 0;
          }
        }
      }
    }
  }
