import { AttendanceHourContainer } from '../models/AttendanceHourContainer';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';

export interface IAttendanceHourService {
    set(attendanceHour: AttendanceHourContainer): Promise<any>;
}

export class AttendanceHourService implements IAttendanceHourService {
    constructor(
        private MessagingHubService: MessagingHubService,
        private MESSAGINGHUBDOMAIN: string,
    ) {}

    /**
     * Add or update and attendance hour to an application
     * @param attendanceHour
     */
    async set(attendanceHour: AttendanceHourContainer): Promise<any> {
        return await this.MessagingHubService.sendCommand({
            method: 'set',
            to: `postmaster@desk.${this.MESSAGINGHUBDOMAIN}`,
            type: 'application/vnd.iris.desk.attendance-hour-container+json',
            uri: '/attendance-hour',
            resource: attendanceHour
        });
    }
}
