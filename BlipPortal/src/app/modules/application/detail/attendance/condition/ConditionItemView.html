<div class="flex flex-row items-top">
    <div class="condition-item" ng-class="{'first-condition' : $ctrl.conditionIndex == 0}">
        <div class="w-100 property mb3" ng-class="{'property-border' : $ctrl.condition.property.includes('Extras')}">
            <bds-select
                value="{{$ctrl.condition.property}}"
                options="{{$ctrl.properties}}"
                class="w-100 br4"
                custom-events="[{ event:'bdsChange', cb: $ctrl.onChangeProperty }]"
                label="{{ 'conditions.modal.if' | translate }}"
                ng-disabled="!$ctrl.canEditRule">
            </bds-select>
            <bds-input
                ng-if="$ctrl.condition.property.includes('Extras')"
                value="{{$ctrl.condition.extrasProperty}}"
                type="text"
                label="Contact.Extras"
                custom-events="[{ event:'bdsChange', cb: $ctrl.onChangeExtrasPropertyDebounced }]"
                ng-disabled="!$ctrl.canEditRule"
                danger="{{ !$ctrl.condition.extrasProperty.length }}"
                class="w-100 br4 extras-property">
            </bds-input>
        </div>

        <div class="flex items-center w-100 mb3 condition">
            <bds-select
                value="{{$ctrl.condition.relation}}"
                options="{{$ctrl.relations}}"
                class="w-100 br4"
                custom-events="[{ event:'bdsChange', cb: $ctrl.onChangeRelation }]"
                label="{{ 'conditions.modal.condition' | translate }}"
                ng-disabled="!$ctrl.canEditRule">
            </bds-select>
        </div>
        <div class="flex items-center w-100 mb3 value">
            <bds-input-chips id="{{$ctrl.ruleId}}-condition-{{$ctrl.conditionIndex}}-values-tags"
                type="text"
                chips="{{ $ctrl.condition.values }}"
                danger="{{ $ctrl.canEditRule && !$ctrl.condition.values.length }}"
                label="{{ 'utils.misc.value' | translate }}"
                custom-events="[{ event:'bdsChangeChips', cb: $ctrl.onChangeValues }]"
                delimiters="\r\n?|\n/,\|;/"
                ng-disabled="!$ctrl.canEditRule"
                class="w-100 br4"
                error-message="{{ 'conditions.emptyFieldErrorMessage' | translate }}">
            </bds-input-chips>
        </div>
    </div>
    <div class="justify-center items-center mt4" id="condition-item-remove-button-container">
        <bds-tooltip class="remove-condition-tooltip" position="top-left" tooltip-text="{{ 'conditions.removeCondition' | translate }}">
            <bds-button-icon variant="secondary"
                ng-disabled="!$ctrl.canEditRule"
                ng-if="$ctrl.conditionIndex>0"
                icon="delete"
                size="short"
                ng-click="$ctrl.onDeleteCondition()">
            </bds-button-icon>
        </bds-tooltip>
    </div>
</div>
