import * as angular from 'angular';
import { <PERSON><PERSON><PERSON>roll<PERSON> } from 'angular';
import { Application } from 'modules/shared/ApplicationTypings';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { arraysEqual } from 'data/array';
import { debounce } from 'data/function';
import { LabeledValue } from '../models/LabeledValue';
import { RulesConstants } from '../rules/RulesConstants';
import { ToastType } from 'modules/application/misc/ToastService';
import { BlipToastOptions, BlipToastService } from 'modules/shared/BlipToastService';

export class ConditionItemController implements IController {
    public currentState: string;
    public valuesEmpty: boolean;
    public properties: LabeledValue[];
    public relations: LabeledValue[];
    public data: any;
    public condition: any;
    public conditionIndex: number;
    public ruleId: any;
    public application: Application;
    public onChangeExtrasPropertyDebounced: (...args: any[]) => void;
    public canEditRule: boolean;
    public onEdit: (obj: any) => void;
    public onDelete: (obj: any) => void;
    public onCancel: (obj: any) => void;

    constructor(
        private SegmentService: SegmentService,
        private $translate: any,
        private BlipToastService: BlipToastService,
    ) {
        this.onChangeExtrasPropertyDebounced = debounce(this.onChangeExtrasProperty, 250);
    }

    async $onInit() {
                this.condition = angular.copy(this.data);
    }

    onChangeProperty = async (event) => {
        if (!this.canEditRule) {
            this.noPermissionValidate();
            return;
        }

        if (event?.detail?.value && this.condition.property !== event.detail.value) {
            this.condition.property = event.detail.value;
            this.tryEdit();
        }
    }

    onChangeExtrasProperty = async (event) => {
        if (!this.canEditRule) {
            this.noPermissionValidate();
            return;
        }

        this.condition.extrasProperty = event.detail.value ?? '';

        if (this.condition.extrasProperty) {
            this.tryEdit();
        }
    }

    onChangeRelation = async (event) => {
        if (!this.canEditRule) {
            this.noPermissionValidate();
            return;
        }

        if (!event || this.condition.relation === event.detail?.value) {
            return;
        } else {
            this.condition.relation = event.detail.value;
        }

        this.tryEdit();
    }

    onChangeValues = async (event) => {
        if (!this.canEditRule) {
            this.noPermissionValidate();
            return;
        }

        if (!event || arraysEqual(this.condition.values, event.detail?.data)) {
            return;
        } else {
            this.condition.values = event.detail?.data || [];
            this.valuesEmpty = this.condition.values.length === 0;
        }

        this.tryEdit();

        if (this.valuesEmpty) {
            await this.showToast('conditions.cannotSaveEmptyValues', ToastType.Warning);
            return;
        }
    }

    tryEdit = async () => {
        try {
            if (this.onEdit) {
                await this.onEdit({ $condition: this.condition });
                if (this.condition.property?.includes(RulesConstants.EXTRAS)) {
                    this.condition.property = RulesConstants.CONTACT_EXTRAS;
                }
            }
        } catch (e) {
            await this.showToast('conditions.warning', ToastType.Warning);
        }
    }

    noPermissionValidate = async () => {
        this.condition = angular.copy(this.data);
    }

    async onDeleteCondition() {
        if (!this.canEditRule) {
            return;
        }
        try {
            this.onDelete({ $conditionIndex: this.conditionIndex });

        } catch (e) {
            await this.showToast('conditions.warning', ToastType.Warning);
        }
    }

    async showToast(messageReference: string, type: ToastType) {
        const msg = await this.$translate(messageReference);
        const toastOptions: BlipToastOptions = { msg };
        await this.BlipToastService.show(type, toastOptions);
    }
}
