export class ConditionLoadValues {
    constructor( private $translate: any ) { }

    public async loadProperties() {
        return [
            {
                value: 'Message',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.property.message',
                ),
            },
            {
                value: 'Contact.Name',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.property.contact.name',
                ),
            },
            {
                value: 'Contact.Email',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.property.contact.email',
                ),
            },
            {
                value: 'Contact.Extras',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.property.contact.extras',
                ),
            },
        ];
    }

    public async loadRelations() {
        return [
            {
                value: 'Contains',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.relation.contains',
                ),
            },
            {
                value: 'NotContains',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.relation.notContains',
                ),
            },
            {
                value: 'Equals',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.relation.equals',
                ),
            },
            {
                value: 'NotEquals',
                label: await this.$translate(
                    'modules.application.detail.attendance.rules.relation.notEquals',
                ),
            },
        ];
    }

    public async loadOperators() {
        return [
            {
                value: 'Or',
                label: (await this.$translate(
                    'utils.misc.or',
                ) as string).toUpperCase(),
            },
            {
                value: 'And',
                label: (await this.$translate(
                    'utils.misc.and',
                ) as string).toUpperCase(),
            },
        ];
    }
}
