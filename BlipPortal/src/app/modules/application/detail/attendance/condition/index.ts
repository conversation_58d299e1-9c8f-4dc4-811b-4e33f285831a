import template from './ConditionItemView.html';
import { ConditionItemController } from './ConditionItemController';

export const ConditionItemNewComponent = {
    require: {
        ngModel: '?ngModel',
    },
    controller: ConditionItemController,
    controllerAs: '$ctrl',
    template,
    bindings: {
        data: '<',
        application: '<',
        canEditRule: '<',
        canSeeRule: '<',
        onEdit: '&?',
        onDelete: '&?',
        onCancel: '&?',
        elementId: '@?',
        ruleId: '<',
        conditionIndex: '<',
        properties: '<',
        relations: '<',
    },
};
