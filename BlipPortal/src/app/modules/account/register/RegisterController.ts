import './Register.scss';

import { IStateService } from 'angular-ui-router';

import AuthenticationService from 'modules/login/AuthenticationService';
import TranslateService from 'modules/translate/TranslateService';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { ITimeoutService } from 'angular';
import { AccountService2, UserAccount } from '../AccountService2';
import { IToggleable } from 'feature-toggle-client';

export class RegisterController implements IToggleable {
    registerError: { data: { properties: { passwordConfirmation: any[] } } };
    account: any;
    loading: boolean;
    hasAcceptedTermsAndPolicy: boolean;
    employeesNumber: { label: string; value: string; }[];
    paramToken: string;
    createAccountKey: string;

    constructor(
        private $state: IStateService,
        private AccountService2: AccountService2,
        private AuthenticationService: AuthenticationService,
        private $location: any,
        private TranslateService: TranslateService,
        private SegmentService: SegmentService,
        private $timeout: ITimeoutService,
    ) {
        'ngInject';

        this.loading = false;

        this.paramToken = this.$location.search().token;
        this.account = new UserAccount({
            email: this.paramToken && this.$location.search().email,
        });
        this.hasAcceptedTermsAndPolicy = false;
        this.checkFeatures();
        this.setEmployeesNumber();
    }

    /**
     * Register variables based on feature flags
     */
    async checkFeatures(): Promise<void> {
        this.createAccountKey = 'create-account-B';
    }

    /**
     * Set employees number based on static array to be used on blipSelect
     */
    setEmployeesNumber(): void {
        this.employeesNumber = [
            { label: '1 - 19', value: '1 - 19' },
            { label: '20 - 49', value: '20 - 49' },
            { label: '50 - 249', value: '50 - 249' },
            { label: '250 - 499', value: '250 - 499' },
            { label: '500+', value: '500+' }
        ];
    }

    /**
     * Get segment payload based on feature flags
     */
    getSegmentPayload(account: UserAccount): { account: {} } {
        return {
            account: {
                name: account.fullName,
                email: account.email,
                companyWebsite: account.extras.companyWebsite,
                companySize: account.extras.companySize,
                whatsAppPhoneNumber: account.whatsAppPhoneNumber ? account.whatsAppPhoneNumber : ''
            }
        };
    }

    /**
     * Register new user or update user that comes from team invite
     */
    async register(): Promise<void> {
        this.loading = true;
        this.account.emailTemplate = {
            link: `${this.$location.protocol()}://${
                location.host
            }/#/activate/${encodeURIComponent(this.account.email)}?token=%token%`,
            cultureInfo: await this.TranslateService.getCurrentLanguage(),
        };

        try {
            /**
             * Set account culture
             */
            const newAccount = {
                ...this.account,
                culture: this.TranslateService.getCurrentLanguage()
            };

            this.SegmentService.setAccount({
                ...newAccount
            });

            /**
             * Save user account
             */
            if (this.paramToken) {
                await this.resetAccountAndRegister(newAccount);
            } else {
                /**
                 * Login with new account created
                 */
                await this.AuthenticationService.login(
                    newAccount.email,
                    newAccount.password,
                );

                await this.AccountService2.fetchAndSetAccount();
                const payload = this.getSegmentPayload(newAccount);

                this.SegmentService.createTrack(this.createAccountKey, payload);
            }

            /**
             * Go to applications list
             */
            this.$state.go('auth.application.list');
        } catch (e) {
            this.registerError = e;
            console.error(e);
        } finally {
            this.$timeout(() => {
                this.loading = false;
            }, 1000);
        }
    }

    /**
     * Reset account, update with account infos and login
     */
    private async resetAccountAndRegister(newAccount: UserAccount): Promise<void> {

        const { password, ...updatedAccount } = newAccount;

        /**
         * Login with new account created
         */
        await this.AuthenticationService.login(
            newAccount.email,
            newAccount.password,
        );

        if (!this.AuthenticationService.getAccount() || this.AuthenticationService.isAccountPending()) {
            await this.AccountService2.save(this.account.email);
        }

        const payload = this.getSegmentPayload(newAccount);
        this.SegmentService.createTrack(this.createAccountKey, payload);
    }
}
