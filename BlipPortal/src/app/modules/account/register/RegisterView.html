<div class="login-wrapper account-register w-100 h-100">
    <div class="columns h-100 login-form-container">
        <bds-illustration type="brand" name="blip-ballon-blue-black-horizontal" class="pl4 pa5 pl6-l header-logo">
        </bds-illustration>
        <div class="form-wrapper login-form-wrapper ph4 ph6-l">
            <h1 class="padding mb3">Level up your chatbot</h1>
            <span>{{ 'account-register.headings.alredyHaveAccount' | translate }} <a class="fw6" ui-sref="anon.login">{{'account-register.headings.login' | translate}}</a></span>
            <form name="$ctrl.registerForm" ng-submit="$ctrl.register($ctrl.registerForm)" novalidate>
                <div class="form-group pt3 pb3">
                    <blip-input-dpr ng-model="$ctrl.account.fullName"
                        parent-form="$ctrl.registerForm"
                        field-id="fullName"
                        field-name="fullName"
                        minlength="6"
                        maxlength="100"
                        required="true"
                        type="text"
                        label="{{'account-register.forms.fullName' | translate}}">
                    </blip-input-dpr>
                    <error-messages form="$ctrl.registerForm" field="fullName" error="$ctrl.registerError"></error-messages>
                </div>
                <div class="form-group pb3">
                    <blip-input-dpr ng-model="$ctrl.account.email"
                        parent-form="$ctrl.registerForm"
                        field-id="email"
                        field-name="email"
                        minlength="5"
                        maxlength="100"
                        required="true"
                        type="email"
                        label="{{'account-register.forms.email' | translate}}">
                    </blip-input-dpr>
                    <error-messages form="$ctrl.registerForm" field="email" error="$ctrl.registerError"></error-messages>
                </div>
                <div class="form-group pb3">
                    <blip-input-dpr ng-model="$ctrl.account.password"
                        parent-form="$ctrl.registerForm"
                        field-id="password"
                        field-name="password"
                        required="true"
                        minlength="6"
                        maxlength="100"
                        input-autocomplete="off"
                        type="password"
                        show-password-strength="true"
                        placeholder="{{'account-register.forms.passwordRegisterHint' | translate}}"
                        label="{{'account-register.forms.password' | translate}}">
                    </blip-input-dpr>
                    <error-messages form="$ctrl.registerForm" field="password" error="$ctrl.registerError"></error-messages>
                    <small translate>account-register.forms.passRecomendation</small>
                </div>
                <div class="form-group pb3">
                    <blip-input-dpr ng-model="$ctrl.account.extras.companyWebsite"
                        parent-form="$ctrl.registerForm"
                        field-id="companyWebsite"
                        field-name="companyWebsite"
                        required="true"
                        minlength="2"
                        maxlength="50"
                        input-autocomplete="off"
                        placeholder="{{'account-register.forms.companyWebsiteHint' | translate}}"
                        label="{{'account-register.forms.companyWebsite' | translate}}">
                    </blip-input-dpr>
                    <error-messages form="$ctrl.registerForm" field="companyWebsite" error="$ctrl.registerError"></error-messages>
                </div>
                <div class="form-group pb3">
                    <blip-select ng-model="$ctrl.account.extras.companySize"
                        options="$ctrl.employeesNumber"
                        placeholder="{{'account-register.forms.select' | translate}}"
                        label="{{'account-register.forms.employeesNumber' | translate}}">
                    </blip-select>
                </div>
                <div class="form-group pb3">
                    <blip-input-dpr ng-model="$ctrl.account.whatsAppPhoneNumber"
                        parent-form="$ctrl.registerForm"
                        field-id="whatsAppPhoneNumber"
                        field-name="whatsAppPhoneNumber"
                        maxlength="20"
                        type="text"
                        input-autocomplete="off"
                        placeholder="{{'account-register.forms.whatsAppPhoneNumberHint' | translate}}"
                        label="{{'account-register.forms.whatsAppPhoneNumber' | translate}}">
                    </blip-input-dpr>
                    <error-messages form="$ctrl.registerForm" field="whatsAppPhoneNumber" error="$ctrl.registerError"></error-messages>
                    <small translate>account-register.forms.whatsAppPhoneNumberInstructions</small>
                </div>
                <div class="pv3 ph2">
                    <blip-checkbox ng-auth-write
                        ng-model="$ctrl.hasAcceptedTermsAndPolicy"
                        label="{{'account-register.forms.accepTermsOfServiceAndPrivacyPolicy' | translate}}">
                    </blip-checkbox>
                </div>
                <div class="row pt3">
                    <div class="w-100 w-55-l right column" style="float: right;">
                        <button type="submit"
                            loading="$ctrl.loading"
                            button-value="{{'account-register.forms.createAccount' | translate}}"
                            class="bp-btn bp-btn--true bp-btn--large u-full-width"
                            ng-disabled="$ctrl.loading || !$ctrl.hasAcceptedTermsAndPolicy || $ctrl.registerForm.$invalid">
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="columns h-100 dn db-l login-banner-container">
        <div class="login-bubbles-img-container">
            <img class="login-bubbles-img-1" src="/assets/img/bubbles/bubble-01.svg" alt="">
            <img class="login-bubbles-img-2" src="/assets/img/bubbles/bubble-02.svg" alt="">
            <img class="login-bubbles-img-3" src="/assets/img/bubbles/bubble-03.svg" alt="">
        </div>
    </div>
</div>
