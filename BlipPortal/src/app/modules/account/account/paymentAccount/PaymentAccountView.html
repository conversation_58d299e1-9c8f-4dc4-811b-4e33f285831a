<div class="mh2 user-payment-container">
    <div class="flex flex-row justify-between w-100">
        <div class="flex flex-column w-60">
            <span class="fw5 bp-fs-4 bp-c-city" translate
                >account.tabs.paymentAccount.title</span
            >
            <p class="bp-fs-6" translate>
                account.tabs.paymentAccount.subtitle
            </p>
        </div>
        <span class="flex flex-column w-40">
            <span class="flex flex-row items-center justify-end">
                <span class="bp-fs-5 fw5 mr2 bp-c-city">R$</span>
                <span class="bp-fs-3 fw5 bp-c-onix">5400,00</span>
            </span>
            <span class="flex justify-end bp-fs-7 bp-c-true fw7" translate
                >account.tabs.paymentAccount.avaiableCredits</span
            >
        </span>
    </div>
    <div class="w-60">
        <ul class="no-style">
            <li
                ng-repeat="application in $ctrl.applications track by application.shortName"
            >
                <div class="flex flex-row items-center justify-between ma3">
                    <div class="flex items-center">
                        <img
                            src="{{application.imageUri || $ctrl.defaultAvatar}}"
                            alt="profile"
                            width="40"
                            height="40"
                            class="br-100"
                        />
                        <span class="ml3">{{ application.shortName }}</span>
                        <span ng-if="application.owner == $ctrl.mineBlipIdentity" class="bp-c-rooftop bp-fs-8 ml2" translate>account.tabs.paymentAccount.owner</span>
                    </div>
                    <switch
                        on-toggle="$ctrl.onToggle(value, $index)"
                        ng-model="application.isPaymentAccount"
                    ></switch>
                </div>
                <div class="bp-divider-h"></div>
            </li>
        </ul>
    </div>
</div>
