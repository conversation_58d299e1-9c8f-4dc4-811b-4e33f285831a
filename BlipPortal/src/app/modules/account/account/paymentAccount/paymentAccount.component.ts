import { IComponentController } from 'angular';
import template from './PaymentAccountView.html';
import { ApplicationService2 } from 'modules/application/ApplicationService2';
import DefaultAvatar from 'assets/img/icons/avatar-default.png';
import { LoadingService } from 'modules/ui/LoadingService';
import { ConfirmationModal } from 'modules/ui';
import { AccountService2 } from 'modules/account/AccountService2';
import { PaymentAccountService } from '../PaymentAccountService';

class PaymentAccountController implements IComponentController {
    public applications: any;
    public defaultAvatar: any;
    private confirmLabel: string;
    private cancelLabel: string;
    private mineBlipIdentity: string;

    constructor(
        private ModalService,
        private LoadingService: LoadingService,
        private AccountService2: AccountService2,
        private ApplicationService2: ApplicationService2,
        private PaymentAccountService: PaymentAccountService,
        private $translate: any,
    ) {
        this.defaultAvatar = DefaultAvatar;
        this.$onInit();
    }

    async $onInit() {
        await this.getCurrentUserAccount();
        await this.getApplications();
        this.confirmLabel = await this.$translate('utils.forms.confirm');
        this.cancelLabel = await this.$translate('utils.forms.cancel');
    }

    async getCurrentUserAccount() {
        const me = await this.AccountService2.me();
        this.mineBlipIdentity = `${encodeURIComponent(me.email)}@blip.ai`;
    }

    async getApplications() {
        try {
            this.LoadingService.startLoading();

            const applications = await this.ApplicationService2.getApplications();

            const promises = [];
            for (const item of applications) {
                const promise = this.ApplicationService2.getApplicationPaymentAccount(
                    item.shortName,
                )
                    .then((resource) => {
                        if (item && resource) {
                            item.owner = resource.owner;
                            item.paymentAccount = resource.paymentAccount;
                        }
                    })
                    .catch((error) => {
                        console.error(error);
                    });
                promises.push(promise);
            }

            await Promise.all(promises);

            this.applications = applications.map((application) => ({
                ...application,
                isPaymentAccount:
                    application.paymentAccount === this.mineBlipIdentity,
            }));
        } catch (e) {
            console.error(e);
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async onToggle(value: boolean, index: number) {
        this.applications[index].isPaymentAccount = !value;
        const application = this.applications[index];
        if (value) {
            const confirmSet = await this.showConfirmSetPaymentAccountModal(
                application.shortName,
            );
            if (confirmSet) {
                await this.setPaymentAccount(
                    application.shortName,
                    index,
                    value,
                );
            }
        } else {
            //Check if it is owner and show can't leave modal
            if (application.owner === this.mineBlipIdentity) {
                await this.showUnableToLeaveModal(application.shortName);
                return;
            }

            const confirmLeave = await this.showLeavePaymentAccountModal(
                application.shortName,
            );
            if (confirmLeave) {
                await this.leavePaymentAccount(
                    application.shortName,
                    index,
                    value,
                );
            }
        }
    }

    async showConfirmSetPaymentAccountModal(
        shortName: string,
    ): Promise<boolean> {
        const title = await this.$translate(
            'account.tabs.paymentAccount.modal.setPaymentAccount.title',
        );
        const body = await this.$translate(
            'account.tabs.paymentAccount.modal.setPaymentAccount.body',
            { shortName },
        );

        const modal = await this.ModalService.showModal(
            ConfirmationModal({
                title: {
                    text: title,
                },
                body,
                buttons: {
                    confirm: {
                        text: this.confirmLabel,
                    },
                    cancel: {
                        text: this.cancelLabel,
                    },
                },
            }),
        );

        const confirmation = await modal.close;

        return confirmation;
    }

    async setPaymentAccount(shortName: string, index: number, value: boolean) {
        try {
            this.LoadingService.startLoading();
            await this.PaymentAccountService.set(
                shortName,
                this.mineBlipIdentity,
            );
            this.applications[index].isPaymentAccount = value;
        } catch (error) {
            console.error(error);
            //Show ngToast
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async showLeavePaymentAccountModal(shortName: string): Promise<boolean> {
        const title = await this.$translate(
            'account.tabs.paymentAccount.modal.leavePaymentAccount.title',
        );
        const body = await this.$translate(
            'account.tabs.paymentAccount.modal.leavePaymentAccount.body',
            { shortName },
        );

        const modal = await this.ModalService.showModal(
            ConfirmationModal({
                title: {
                    text: title,
                },
                body,
                buttons: {
                    confirm: {
                        text: this.confirmLabel,
                    },
                    cancel: {
                        text: this.cancelLabel,
                    },
                },
            }),
        );

        const confirmation = await modal.close;

        return confirmation;
    }

    async leavePaymentAccount(
        shortName: string,
        index: number,
        value: boolean,
    ) {
        try {
            this.LoadingService.startLoading();
            await this.PaymentAccountService.delete(shortName);
            this.applications[index].isPaymentAccount = value;
        } catch (error) {
            console.error(error);
            //Show ngToast
        } finally {
            this.LoadingService.stopLoading();
        }
    }

    async showUnableToLeaveModal(shortName: string) {
        const title = await this.$translate(
            'account.tabs.paymentAccount.modal.unableToLeave.title',
        );
        const body = await this.$translate(
            'account.tabs.paymentAccount.modal.unableToLeave.body',
            { shortName },
        );
        const ok = await this.$translate(
            'account.tabs.paymentAccount.modal.unableToLeave.ok',
        );

        const modal = await this.ModalService.showModal(
            ConfirmationModal({
                title: {
                    text: title,
                },
                body,
                buttons: {
                    confirm: {
                        text: ok,
                    },
                    cancel: {
                        text: this.cancelLabel,
                    },
                },
            }),
        );

        await modal.close;
    }
}

export const PaymentAccountComponent = {
    template,
    controller: PaymentAccountController,
    controllerAs: '$ctrl',
    bindings: {
        application: '<?',
    },
};
