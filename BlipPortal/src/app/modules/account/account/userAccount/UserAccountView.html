<form ng-submit="$ctrl.save()"
    id="accountForm"
    name="$ctrl.accountForm"
    class="m0">
    <div class="mh2">
        <div class="row mt4">
            <div class="six columns">
                <blip-input-dpr ng-model="$ctrl.account.email"
                    disabled="true"
                    field-id="email"
                    label="{{'account.forms.email' | translate}}"
                    parent-form="$ctrl.accountForm"  /></blip-input-dpr>
            </div>
            <div class="six columns">
                <div class="form-group">
                    <blip-input-dpr ng-model="$ctrl.account.company"
                        minlength="2"
                        maxlength="50"
                        on-change="$ctrl.debouncedSave()"
                        field-name="company"
                        field-id="company"
                        required="true"
                        parent-form="$ctrl.accountForm"
                        label="{{'account.forms.company' | translate}}"></blip-input-dpr>
                </div>
            </div>

        <div class="row mv4">
            <div class="six columns">
                <div class="form-group pb0">
                    <blip-input-dpr ng-if="$ctrl.language == 'pt'"
                        ng-model="$ctrl.account.phoneNumber"
                        on-change="$ctrl.debouncedSave()"
                        type="text"
                        name="phoneNumber"
                        field-name="phoneNumber"
                        field-id="phoneNumber"
                        label="{{'account.forms.phoneNumber' | translate}}"
                        parent-form="$ctrl.accountForm"
                        ng-maxlength="20"
                        ui-br-phone-number-mask
                        required
                    ></blip-input-dpr>
                    <blip-input-dpr ng-if="$ctrl.language == 'en'"
                        ng-model="$ctrl.account.phoneNumber"
                        on-change="$ctrl.debouncedSave()"
                        type="text"
                        name="phoneNumber"
                        field-name="phoneNumber"
                        field-id="phoneNumber"
                        label="{{'account.forms.phoneNumber' | translate}}"
                        parent-form="$ctrl.accountForm"
                        ng-maxlength="20"
                        ui-us-phone-number-mask
                        required
                        ></blip-input-dpr>
                    </div>
            </div>

            <div class="six columns">
                <blip-select ng-model="$ctrl.account.culture"
                    label="{{'account.tabs.userAccount.title' | translate}}"
                    on-select-option="$ctrl.debouncedSave()"
                    options="[
                                    { label: 'Português', value: 'pt-BR'},
                                    { label: 'English', value: 'en-US'},
                                ]"></blip-select>
            </div>
        </div>

        <div class="row">
            <div class="six columns">
                <div class="form-group">
                    <checkbox class="fl"
                        refer="teste"
                        ng-model="$ctrl.hasWhatsAppPhoneNumber"
                        ng-change="$ctrl.setWhatsAppPhoneNumber()">
                        <span translate>account.tabs.userAccount.whatsAppTips</span>
                        <img class="ml2"
                            src="/assets/img/channelsv3/whatsapp.svg" />
                    </checkbox>
                </div>

                <div class="form-group mt4"
                    ng-if="$ctrl.hasWhatsAppPhoneNumber">
                    <blip-input-dpr ng-if="$ctrl.language == 'pt'"
                        ng-model="$ctrl.account.whatsAppPhoneNumber"
                        on-change="$ctrl.debouncedSave()"
                        type="text"
                        name="whatsAppPhoneNumber"
                        field-name="whatsAppPhoneNumber"
                        field-id="whatsAppPhoneNumber"
                        label="{{'account.forms.whatsAppPhoneNumber' | translate}}"
                        parent-form="$ctrl.accountForm"
                        ng-maxlength="20"
                    ></blip-input-dpr>
                    <blip-input-dpr ng-if="$ctrl.language == 'en'"
                        ng-model="$ctrl.account.whatsAppPhoneNumber"
                        on-change="$ctrl.debouncedSave()"
                        type="text"
                        name="whatsAppPhoneNumber"
                        field-name="whatsAppPhoneNumber"
                        field-id="whatsAppPhoneNumber"
                        label="{{'account.forms.whatsAppPhoneNumber' | translate}}"
                        parent-form="$ctrl.accountForm"
                        ng-maxlength="20"
                    ></blip-input-dpr>
                    <error-messages form="$ctrl.accountForm"
                        field="whatsAppPhoneNumber"
                        error="$ctrl.error" />
                </div>
            </div>
        </div>

        <div class="row mt5">
            <div class="twelve columns">
                <div class="row row-compact fr flex items-center">
                    <span class="mr5">
                        <strong>
                            <a ng-click="$ctrl.openChangePasswordModal()"
                                translate>account.forms.changePassword</a>
                        </strong>
                    </span>
                </div>
            </div>
        </div>
    </div>
</form>
