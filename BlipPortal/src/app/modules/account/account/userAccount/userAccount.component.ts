import template from './UserAccountView.html';
import {
    IComponentController,
    IRootScopeService,
    IScope,
    IFormController,
} from 'angular';
import { SegmentService } from 'modules/application/misc/SegmentService';
import TranslateService from 'modules/translate/TranslateService';
import { LoadingService } from 'modules/ui/LoadingService';
import { AccountUpdated } from 'modules/account/account/AccountController';
import { debounce } from 'data/function';
import { EventEmitter } from 'modules/shared/EventEmitter';

class UserAccountController implements IComponentController {
    saveAccountError: any;
    language: string;
    accountForm: IFormController;
    error: any;
    hasWhatsAppPhoneNumber: boolean;
    account: any;
    debouncedSave: (...args: any[]) => void;
    setSave: ($event) => void;
    constructor(
        private $scope: IScope,
        private $timeout,
        private $rootScope: IRootScopeService,
        private ngToast: any,
        private ModalService: any,
        private LoadingService: LoadingService,
        private $translate: any,
        private TranslateService: TranslateService,
        private SegmentService: SegmentService,
    ) {
        'ngInject';

        this.language = TranslateService.getCurrentLanguage();
        switch (this.language) {
            case 'pt':
                this.account.culture = 'pt-BR';
                break;
            case 'en':
            default:
                this.account.culture = 'en-US';
                break;
        }

        if (this.account.whatsAppPhoneNumber) {
            this.hasWhatsAppPhoneNumber = true;
        }

        this.debouncedSave = debounce(this.save, 500);

        this.$scope.$watch('$ctrl.account.fullName', (newVal, oldVal) => {
            if (newVal && newVal !== oldVal) {
                this.debouncedSave();
            }
        });
    }

    async save() {
        if (this.accountForm.$invalid) {
            if (
                Object.keys(this.accountForm.$error).some(
                    (key) => key === 'brPhoneNumber',
                )
            ) {
                const key = 'brPhoneNumber';
                this.$timeout(() => {
                    this.accountForm.$error[key].map((err) => {
                        this.accountForm[err.$name].$invalid = true;
                        this.accountForm[err.$name].$valid = false;
                    });
                });
            }

            return;
        }

        this.accountForm.$setPristine();
        this.accountForm.$setSubmitted();
        this.setSave(EventEmitter({ isSaving: true }));

        try {
            this.$rootScope.$emit(AccountUpdated);

            await this.SegmentService.createTrack(
                'update-account',
                this.account,
            );

            const { fullName, ...newAccount } = this.account;

            if (newAccount.culture) {
                //Update language
                let userLanguage;
                switch (newAccount.culture.startsWith('pt')) {
                    case true:
                        userLanguage = 'pt';
                        break;
                    case false:
                        userLanguage = 'en';
                        break;
                }

                this.TranslateService.setCurrentLanguage(userLanguage);
                this.language = this.TranslateService.getCurrentLanguage();
            }
        } catch (err) {
            this.saveAccountError = err;
            throw err;
        } finally {
            this.setSave(EventEmitter({ isSaving: false }));
        }
    }

    async openChangePasswordModal() {
    }

    setWhatsAppPhoneNumber() {
        if (!this.hasWhatsAppPhoneNumber) {
            this.account.whatsAppPhoneNumber = undefined;
        }
        this.debouncedSave();
    }
}

export const UserAccountComponent = {
    template,
    controller: UserAccountController,
    controllerAs: '$ctrl',
    bindings: {
        account: '<',
        setSave: '&',
    },
};
