export class MockPaymentAccount {
    private cache: { [key: string]: { owner: string; paymentAccount: string } };

    constructor() {
        this.cache = {
            testelouis: {
                owner:  '<EMAIL>',
                paymentAccount: '<EMAIL>',
            },
            bliptestcards: {
                owner:  '<EMAIL>',
                paymentAccount: '<EMAIL>',
            },
            testezendeskbug: {
                owner:  '<EMAIL>',
                paymentAccount: '<EMAIL>',
            },
            botastral: {
                owner: '<EMAIL>',
                paymentAccount: '<EMAIL>',
            },
            botdeatendimentohmg: {
                owner:  '<EMAIL>',
                paymentAccount: '<EMAIL>',
            }
        };
    }

    set(key: string, value: any): void {
        this.cache[key] = value;
    }

    get(key: string): any {
        return this.cache[key];
    }

    delete(key: string): void {
        delete this.cache[key];
    }
}
