<div class="flex flex-column w-100 min-vh-flex account-container">
    <div class="container flex-noshrink">
        <div class="row">
            <div class="twelve columns">
                    <page-header>
                        <div class="flex flex-row items-center justify-between">

                            <div class="flex items-center">
                                <letter-avatar class="account-editing-avatar ml4"
                                text="$ctrl.account.fullName"></letter-avatar>
                                <input id="fullName"
                                name="fullName"
                                class="bp-fs-4 u-editable-field"
                                required
                                auto-expand="initial"
                                ng-model="$ctrl.account.fullName"
                                ng-minlength="6"
                                ng-maxlength="50" />
                                <error-messages form="$ctrl.accountForm"
                                field="fullName"
                                error="$ctrl.error" />
                            </div>
                            <div>
                                <div ng-show='!$ctrl.isSaving'>
                                    <img id='saving-img' src="{{$ctrl.saveCheck}}" width="15">
                                    <span id='saving-text' translate>utils.misc.savedMsg</span>
                                </div>
                                <div ng-show='$ctrl.isSaving'>
                                    <img id='saving-img' class='spin' src="{{$ctrl.saveSpinner}}" width="15">
                                    <span id='saving-text' translate>utils.misc.savingMsg</span>
                                </div>
                            </div>
                        </div>

                    </page-header>
                    <div class="u-bubble-wrapper">
                        <content-tabs>
                            <div class="ph4">
                                <tab tab-title="{{ 'account.tabs.userAccount.title' | translate}}">
                                    <user-account account="$ctrl.account" set-save="$ctrl.setSave($event)"></user-account>
                                </tab>
                                <tab ng-if="$ctrl.hasPayerAccountTab" tab-title="{{ 'account.tabs.paymentAccount.tabTitle' | translate}}">
                                    <payment-account application="$ctrl.application"></payment-account>
                                </tab>
                            </div>
                        </content-tabs>
                    </div>
            </div>
        </div>
    </div>
    <blip-footer></blip-footer>
</div>
