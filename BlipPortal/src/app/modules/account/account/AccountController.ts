import {
    IFormController,
} from 'angular';

export const AccountUpdated = 'AccountUpdated';
import * as saveCheck from 'assets/img/checked.png';
import * as saveSpinner from 'assets/img/loading3.png';
import { PayerAccountActionFeatures } from './AccountActionFeatures';

export class AccountController {
    saveAccountError: any;
    language: string;
    accountForm: IFormController;
    error: any;
    hasWhatsAppPhoneNumber: boolean;
    isSaving: boolean = false;
    saveCheck: any;
    saveSpinner: any;
    hasPayerAccountTab: any;
    constructor() {
        this.saveCheck = saveCheck;
        this.saveSpinner = saveSpinner;
        this.checkTabs();
    }

    async checkTabs() {
        try {
            this.hasPayerAccountTab = await PayerAccountActionFeatures.payerAccountActionApplicationEnabled();
        } catch (error) {
            console.error(error);
        }
    }

    setSave({ isSaving }) {
        this.isSaving = isSaving;
    }
}
