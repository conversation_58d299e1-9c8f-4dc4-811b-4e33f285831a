import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { MockPaymentAccount } from './MockPaymentAccount';

export class PaymentAccountService {
    private MockPaymentAccount: MockPaymentAccount;

    constructor(
        private MessagingHubService: MessagingHubService
    ) {
        this.MockPaymentAccount = new MockPaymentAccount();
    }

    /**
     * Set a new payment account for application with shortName parameter
     * @param shortName
     * @param paymentAccount
     */
    async set(shortName: string, paymentAccount: string) {
        return await new Promise((resolve) => {
            setTimeout(() => {
                const application = this.MockPaymentAccount.get(shortName);
                this.MockPaymentAccount.set(shortName, {...application, paymentAccount: paymentAccount});
                // @ts-ignore
                resolve();
            }, 1000);
        });
    }

    /**
     * Delete the current payment account for application with shortName parameter
     * @param shortName
     */
    async delete(shortName: string) {
        return await new Promise((resolve) => {
            setTimeout(() => {
                const application = this.MockPaymentAccount.get(shortName);
                this.MockPaymentAccount.set(shortName, {...application, paymentAccount: undefined});
                // @ts-ignore
                resolve();
            }, 1000);
        });
    }
}
