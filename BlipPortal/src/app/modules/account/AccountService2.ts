import { BlipService } from 'modules/messaginghub/BlipService';
import AuthenticationService from 'modules/login/AuthenticationService';
import { IRootScopeService } from 'angular';
import TranslateService from 'modules/translate/TranslateService';
import { AccountUpdated } from './account/AccountController';

export const addUsersMessageId = 'add-new-users-to-list';

const blipAccountIdentity = '<EMAIL>';

export class UserAccount {
    id: number;
    identity: string;
    address: string;
    email: string;
    fullName: string;
    password?: string;
    phoneNumber: string = '**********';
    whatsAppPhoneNumber?: string;
    culture: string;
    isPending: boolean;
    company: string;
    position?: string;
    expiration?: string;
    city?: string;
    created: string;
    userProfile?: string;
    extras?: any;
    token?: string;
    photoUri?: string;

    constructor(props?: Partial<UserAccount>) {
        Object.assign(this, props);
    }
}

export interface UserBlipAccount {
    alternativeAccount: string;
    culture: string;
    email: string;
    extras: { [x: string]: any };
    userProfile: string;
    fullName: string;
    identity: string;
    phoneNumber: string;
    creationDate: string;
    EmailConfirmed: boolean;
}

export interface BlipAccountUser {
    id?: string;
    country?: string;
    state?: string;
    city?: string;
    fullName?: string;
    email?: string;
    culture?: string;
    timeZoneName?: string;
    photoUri?: string;
    companySite?: string;
    companyNumberOfEmployees?: number;
}

/**
 * Class representing a plan
 * @class
 */
export class Plan {
    ownerIdentity: string;
    id: string;
    name: string;
    extras: {};
}

/**
 * Class representing a subscription
 * @class
 */
export class Subscription {
    ownerIdentity: string;
    id: string;
    subscriber: string;
    planId: string;
    startDate: string;
    endDate: string;
    lastChangeDate: string;
    isPending: boolean;
    isActive: boolean;
}

export class AccountService2 {
    waitForUserPromise: { resolve: (param?: any) => void; reject: () => any; promise: Promise<unknown>; };

    constructor(
        private BlipService: BlipService,
        private BLIP_DOMAIN: string,
        private MESSAGINGHUBDOMAIN,
        private AuthenticationService: AuthenticationService,
        private $rootScope: IRootScopeService & { applicationUser: UserBlipAccount },
        private TranslateService: TranslateService,
    ) {
        'ngInject';
        this.init();
    }

    async init() {
        this.createInitializationPromise();
        await this.refreshUser();
        this.waitForUserPromise.resolve();
    }

    private createInitializationPromise() {
        this.waitForUserPromise = {
            resolve: () => undefined,
            reject: () => undefined,
            promise: new Promise(() => undefined)
        };
        const promise = new Promise((resolve, reject) => {
            this.waitForUserPromise.resolve = resolve;
            this.waitForUserPromise.reject = reject;
        });
        this.waitForUserPromise.promise = promise;
    }

    waitForInitialization(): Promise<any> {
        return this.waitForUserPromise.promise;
    }

    async save(email) {
        let data = undefined;

        try {
            this.connect();

            const identity = encodeURIComponent(email + '@' + this.BLIP_DOMAIN);

            const accountBlip = await this.me();
            data = await this.BlipService.sendCommand({
                method: 'set',
                type: 'application/vnd.lime.account+json',
                uri: '/account',
                resource: {
                    ...accountBlip,
                    culture: accountBlip.culture,
                    identity: identity,
                    extras: {
                        ...accountBlip.extras,
                        acceptedTermsOfServiceVersion: '1.0.0',
                        acceptedPrivacyPolicyVersion: '1.0.0',
                    },
                },
            });

            await this.BlipService.sendMessage({
                type: 'text/plain',
                content: '#addlist',
                to: '<EMAIL>',
                id: addUsersMessageId,
            });

            await this.setUpAccount();
        } catch (e) {
            console.error(e);
        }

        return data;
    }

    mergeExtras(extras) {
        return this.setExtras(extras, false);
    }

    /**
     * Get account using identity name and domain
     * @param application
     * @param identity
     */
     async getAccount(identity: string): Promise<any> {
        const identitySplit = identity.split('@');
        const name = identitySplit[0];
        const domain = identitySplit[1] || this.BLIP_DOMAIN;

        return await this.BlipService.sendCommand(
            {
                method: 'get',
                to: `postmaster@${domain}`,
                uri: `lime://${domain}/accounts/${encodeURIComponent(name)}`,
            },
            5000,
        );
    }

    getAccountToken() {
        return this.AuthenticationService.token;
    }

    async setExtras(extras, forceUpdate: boolean = true) {
        try {
            this.connect();

            const accountBlip = await this.me();

            if (!forceUpdate) {
                extras = {
                    ...accountBlip.extras,
                    ...extras
                };
            }

            await this.BlipService.sendCommand({
                method: 'set',
                type: 'application/vnd.lime.account+json',
                uri: '/account',
                resource: {
                    ...accountBlip,
                    extras: extras,
                },
            });

            await this.refreshUser();
        } catch (e) {
            console.error(e);
        }
    }

    async refreshUser(): Promise<void> {
        try {
            await this.connect();
            this.$rootScope.applicationUser = await this.BlipService.sendCommand({
                method: 'get',
                uri: '/account',
            });
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Retrieve Blip Account
     */
    async me(): Promise<UserBlipAccount> {
        await this.waitForInitialization();
        return this.$rootScope.applicationUser;
    }

    /**
     * This method can only be called when $rootScope.applicationUser is defined
     * Retrieve Blip Account Sync
     */
    meSync(): UserBlipAccount {
        return this.$rootScope.applicationUser;
    }

    async get(identifier: string, domain: string = this.BLIP_DOMAIN) {
        this.connect();

        return await this.BlipService.sendCommand({
            method: 'get',
            to: `postmaster@${domain}`,
            uri: `lime://${domain}/accounts/${identifier}`,
        });
    }

    async setUpAccount() {
        try {
            this.connect();

            await this.BlipService.sendCommand({
                to: `postmaster@portal.${this.BLIP_DOMAIN}`,
                method: 'set',
                type: 'application/vnd.lime.account+json',
                uri: '/account',
                resource: {},
            });

            await this.refreshUser();
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Gets the active subscription of the account.
     * @async @public
     * @function getActiveSubscription
     * @returns {Promise<Subscription>} activeSubscription
     */
    async getActiveSubscription(): Promise<Subscription> {
        try {
            this.connect();

            return await this.BlipService.sendCommand(
                {
                    to: `postmaster@portal.${this.BLIP_DOMAIN}`,
                    method: 'get',
                    uri: '/active-subscription',
                },
                10000,
            );
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Gets the plan of the account.
     * @async @public
     * @function getPlan
     * @returns {Promise<Plan>} plan
     */
    async getAccountPlan(): Promise<Plan> {
        try {
            this.connect();

            const { planId } = await this.getActiveSubscription();
            return await this.getPlan(planId);
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Get the plan of the tenant
     * @async @public
     * @function getTenantPlan
     * @returns {Promise<Plan>} plan
     */
    async getTenantPlan(tenantId: string): Promise<Plan> {
        try {
            this.connect();

            const { planId } = await this.BlipService.sendCommand(
                {
                    to: `postmaster@portal.${this.BLIP_DOMAIN}`,
                    method: 'get',
                    uri: `/tenants/${encodeURIComponent(tenantId)}/subscription`
                },
                10000,
            );

            return await this.getPlan(planId);
        } catch (e) {
            console.error(e);
        }
    }

    async getPlan(planId: string): Promise<Plan> {
        try {
            this.connect();

            const { items: plans } = await this.BlipService.sendCommand(
                {
                    to: `postmaster@portal.${this.BLIP_DOMAIN}`,
                    method: 'get',
                    uri: `/plans?$filter=Id%20eq%20%27${planId}%27`,
                },
                10000,
            );

            if (plans && plans.length > 0) {
                return plans[0];
            }

            return new Plan();
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Gets the application payment account.
     */
    async getPaymentAccount(application: any): Promise<any> {
        try {
            this.connect();

            return await this.BlipService.sendCommand({
                to: `postmaster@portal.${this.BLIP_DOMAIN}`,
                method: 'get',
                uri: `/applications/${application.applicationJson.identifier}@${this.MESSAGINGHUBDOMAIN
                    }/payment-account`,
            });
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Checks the connection with BlipService.
     * If the connection does not exist, creates a new one.
     * @async @public
     * @function connect
     */
    public async connect() {
        if (!this.BlipService.isConnected()) {
            if (!this.AuthenticationService.email) {
                await this.AuthenticationService.waitForInitialization();
            }
            await this.BlipService.connect(
                encodeURIComponent(this.AuthenticationService.email),
                this.AuthenticationService.token,
            );
        }
    }

    setUpdatedAccountListener() {
        try {
            this.BlipService.onConnect(() => {
                this.BlipService.addMessageReceiver(
                    this.onReceiveUpdatedAccountMessage.bind(this),
                    m => m.from.startsWith(blipAccountIdentity) && m.type === 'application/vnd.blip.account-updated+json'
                );
            });
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * Sends invitation for user added to team
     * @async @public
     * @function inviteUser
     * @param user
     */
    public async inviteUser(user: any) {
        try {
            this.connect();

            await this.BlipService.sendCommand({
                to: `postmaster@portal.${this.BLIP_DOMAIN}`,
                method: 'set',
                type: 'application/vnd.iris.portal.guest-user+json',
                uri: '/auth-permissions',
                resource: user,
            });

            await this.refreshUser();
        } catch (e) {
            console.error(e);
        }
    }

    /**
    * Receive a message from BlipAccount informing that the user updated his account
    * @param message - Updated account message
    */
    private onReceiveUpdatedAccountMessage(message: Lime.Message) {
        this.setUpdatedBlipAccount(message.content)
            .then((updatedAccount) => {
                this.setTranslateCurrentLanguage(updatedAccount.culture);
                updatedAccount.id = updatedAccount.email;
                this.AuthenticationService.setAccount(updatedAccount);
                this.$rootScope.$emit(AccountUpdated);
            });
    }

    private async setUpdatedBlipAccount(account: BlipAccountUser) {

        const accountBlip = await this.me();
        const updatedAccount = {
            ...accountBlip,
            ...account,
        };

        await this.BlipService.sendCommand({
            method: 'set',
            type: 'application/vnd.lime.account+json',
            uri: '/account',
            resource: updatedAccount,
        });

        await this.refreshUser();

        return updatedAccount;
    }

    private setTranslateCurrentLanguage(culture: string) {
        if (culture) {
            //Update language
            let userLanguage;
            switch (culture.startsWith('pt')) {
                case true:
                    userLanguage = 'pt';
                    break;
                case false:
                    userLanguage = 'en';
                    break;
            }

            this.TranslateService.setCurrentLanguage(userLanguage);
        }
    }

    getEmailDomain = (email: string) => {
        const domainUrl = email.split('@')[1];
        return domainUrl.split('.')[0];
    }

    async fetchAndSetAccount() {
        try {
            let account;
            account = await this.me();
            this.AuthenticationService.setAccount(account);
        } catch (e) {
            console.error(e);
        }
    }

}
