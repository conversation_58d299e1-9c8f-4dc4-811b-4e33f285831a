import * as angular from 'angular';
import 'angular-base64';
import 'angular-modal-service';
import 'angular-ui-router';
import 'angular-resource';

//Style
import './Account.scss';

import { IStateProvider, IStateService } from 'angular-ui-router';
import { IRootScopeService } from 'angular';

import { AccountService2 } from './AccountService2';

import AuthenticationService from '../login/AuthenticationService';

import { AccountController } from './account/AccountController';
import AccountView from './account/AccountView.html';

import { RegisterController } from './register/RegisterController';
import RegisterView from './register/RegisterView.html';

import SubheaderPlaceholderView from '../navbar/subheader/SubheaderPlaceholderView.html';

import { WelcomeScreenController } from './welcome/WelcomeScreenController';
import WelcomeScreenView from './welcome/WelcomeScreenView.html';

import { UserAccountComponent } from './account/userAccount/userAccount.component';
import { PaymentAccountComponent } from './account/paymentAccount/paymentAccount.component';
import { PaymentAccountService } from './account/PaymentAccountService';
import { AccountFeatures } from './AccountFeatures';
import { ContextProcessorService, Contexts } from 'modules/core/ContextProcessorService';
import { TenantService } from 'modules/application/tenant/TenantService';
import { accountManagementUrl } from 'app.constants';

const fetchAccount = async (
    AccountService2: AccountService2,
    ngToast: any,
    $translate: any,
) => {
    'ngInject';

    try {
        const data = await AccountService2.me();
        return data;
    } catch (err) {
        const errorMsg8 = await $translate('utils.errorMsg.8');
        ngToast.danger(errorMsg8);
        throw err;
    }
};

// State names
export const registerStateName = 'anon.register';
export const accountStateName = 'auth.account';
export const welcomeScreenStateName = 'auth.welcomeScreen';

// Local storage
export const LSLastUrlTry = 'blip-portal:lastUrlTry'; // LS = local storage

//
export default angular
    .module('account', [
        'angularModalService',
        'base64',
        'ngResource',
        'ui.router',
    ])
    .service('AccountService2', AccountService2)
    .service('PaymentAccountService', PaymentAccountService)
    .service('TenantService', TenantService)
    .component('userAccount', UserAccountComponent)
    .component('paymentAccount', PaymentAccountComponent)
    .config(($stateProvider: IStateProvider) => {
        'ngInject';

        $stateProvider
            .state(registerStateName, {
                url: '/register',
                controllerAs: '$ctrl',
                params: { area: 'user-register' },
                templateProvider() {
                    return RegisterView;
                },
                controllerProvider() {
                    return RegisterController;
                },
            })
            .state(welcomeScreenStateName, {
                url: '/welcome?tenant-invitation',
                views: {
                    '@': {
                        controller: WelcomeScreenController,
                        controllerAs: '$ctrl',
                        template: WelcomeScreenView,
                    },
                },
            })
            .state(accountStateName, {
                url: '/account?activeTab',
                resolve: {
                    account: fetchAccount,
                },
                views: {
                    'subheader@auth': {
                        template: SubheaderPlaceholderView,
                    },
                    '@auth': {
                        template: AccountView,
                        controller: AccountController,
                        controllerAs: '$ctrl',
                    },
                },
            });
    })
    .run(
        (
            $rootScope: IRootScopeService,
            $state: IStateService,
            AuthenticationService: AuthenticationService,
            AccountService2: AccountService2,
            BLIP_ACCOUNT_URL,
            ContextProcessorService: ContextProcessorService,
            TenantService: TenantService
        ) => {
            'ngInject';

            // FIXME: the line below is needed so that an instance of AuthenticationService
            // is spawned by angular and eslint doesn't complain about unused function arguments.
            AuthenticationService; // tslint:disable-line

            const newBlipAccountMatches = {
                [accountStateName]: '/account',
            };

            //Set listener for updated account message comming from BLiP Account
            AccountService2.setUpdatedAccountListener();

            $rootScope.$on(
                '$stateChangeStart',
                (event, toState, toParams, fromState, fromParams) => {
                    const stateParamsToQueryString = (delimiter = '') => {
                        const paramsKeys = Object.keys(toParams);
                        if (paramsKeys.length > 0) {
                            const queryString = paramsKeys
                                .reduce((acc, curr, index) => toParams[curr]
                                    ? `${acc}${curr}=${toParams[curr]}${index + 1 < paramsKeys.length ? '&' : ''}`
                                    : acc, '');

                            return `${delimiter}${queryString}`;
                        }
                    };

                    const addQueryParams = (delimiter = '') => `${stateParamsToQueryString() && delimiter}${stateParamsToQueryString()}`;

                    if (
                        !Object.keys(newBlipAccountMatches).includes(
                            toState.name,
                        )
                    ) {
                        return;
                    }

                    const redirectToOriginalState = async () => {
                        await $state.go(toState.name, toParams, {
                            notify: false,
                        });
                        $rootScope.$broadcast(
                            '$stateChangeSuccess',
                            toState,
                            toParams,
                            fromState,
                            fromParams,
                        );
                    };

                    const processAccountStateRedirect = async () => {
                        const myAccountUrl = `${BLIP_ACCOUNT_URL}${newBlipAccountMatches[accountStateName]}`;
                        const storageOidcData = JSON.parse(localStorage.getItem('oidc.azureB2CData'));
                        const azureAdEnabled = !!storageOidcData && !!storageOidcData.isEnabled;
                        if (azureAdEnabled) {
                            window.location.href = `${accountManagementUrl}/account?portalUrl=${encodeURI(window.location.origin)}`;
                            return;
                        }

                        await ContextProcessorService.waitForInitialization();
                        const isOrganizationEnabled = await TenantService.isOrganizationEnabled();
                        if (!isOrganizationEnabled || ContextProcessorService.context === Contexts.Default) {
                            window.location.href = `${myAccountUrl}${addQueryParams('?')}`;
                            return;
                        }

                        if (ContextProcessorService.context === Contexts.Tenants) {
                            window.location.href = `${myAccountUrl}?portalUrl=${encodeURI(window.location.origin)}${addQueryParams('&')}`;
                            return;
                        }
                    };

                    event.preventDefault();
                    const isEnabled = AccountFeatures.isBlipAccountEnabled();

                    // Was avoided use async method in $stateChangeStart due to multiple unknowed digest cycle errors
                    if (isEnabled) {
                        switch (toState.name) {
                            case registerStateName:
                                window.location.href = `${BLIP_ACCOUNT_URL}${newBlipAccountMatches[registerStateName]}?${addQueryParams()}`;
                                return;
                            case accountStateName:
                                processAccountStateRedirect();
                                return;

                        }
                    }

                    redirectToOriginalState();
                },
            );

            $rootScope.$on('$stateChangeSuccess', async (e, toState) => {
                if (
                    toState.data &&
                    toState.data.restricted === true &&
                    toState.name === 'auth.application.list'
                ) {
                    e.preventDefault();
                }
            });
        },
    ).name;
