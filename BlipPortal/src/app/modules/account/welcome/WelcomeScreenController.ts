import './WelcomeScreen.scss';
import { TenantService } from 'modules/application/tenant/TenantService';
import { IStateService, IStateParamsService } from 'angular-ui-router';
import { AccountService2, UserBlipAccount } from '../AccountService2';
import { AccountFeatures } from '../AccountFeatures';
import { tenantNotFoundStateName } from 'modules/application/tenant';
import { BlipToastService } from 'modules/shared/BlipToastService';
import { existingTenantStateName, personalTenantCreationError } from 'modules/application/tenant';
import { translate } from 'angular';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { LoadingService } from 'modules/ui/LoadingService';
import { WELCOME_SCREEN_PAGE_LOADED, WELCOME_SCREEN_LETS_GO_BUTTON_CLICKED, } from './WelcomeScreenSegmentEvents';
import { Option } from 'blip-ds/dist/types/components/selects/select-interface';
import { dateToCompareTenant } from 'app.constants';
import moment from 'moment';

const USER_POSITION_SELECT_NAME = 'userPosition';
const USER_AREA_SELECT_NAME = 'userArea';
const OTHER_INTEREST = 'other';
const FORMAT_DATE = 'YYYY-MM-DD';

export class WelcomeScreenController {
    isOrganizationEnabled: boolean;
    isPersonalTenantEnabled: boolean;
    blipAccount: UserBlipAccount;
    tenantId: string;
    isLoading: boolean = false;
    userPositionOptions: Option[] = [];
    isUserPositionOptionsLoaded: boolean = false;
    userPosition: string;
    userAreaOptions: Option[] = [];
    isUserAreaOptionsLoaded: boolean = false;
    userArea: string;
    userInterestOptions: Option[] = [];
    isUserInterestOptionsLoaded: boolean = false;
    userInterest: string;
    userInterestOtherOption: string;
    waOptIn: boolean = false;
    phoneNumber: string;
    previousPhoneNumber: string;
    userHasOtherInterest: boolean = false;

    constructor(
        private TenantService: TenantService,
        private $state: IStateService,
        private AccountService2: AccountService2,
        private $stateParams: IStateParamsService,
        private BlipToastService: BlipToastService,
        private SegmentService: SegmentService,
        private $translate: translate.ITranslateService,
        private $window: Window,
        private LoadingService: LoadingService,
    ) {
        this.tenantId = this.$stateParams['tenant-invitation'];
        this.SegmentService.createOrganizationTrack(WELCOME_SCREEN_PAGE_LOADED);
    }

    async $onInit() {
        this.LoadingService.startLoading(false);

        await this.checkFeatures();

        this.blipAccount = await this.AccountService2.me();
        this.previousPhoneNumber = this.blipAccount.phoneNumber;
        this.phoneNumber = this.previousPhoneNumber;

        this.fillUserPositionOptions();
        this.fillUserAreaOptions();
        this.fillUserInterestOptions();

        this.LoadingService.stopLoading();
    }

    formatDate(dataString: string) {
        const dateToFormat = new Date(dataString);
        return moment(
            new Date(dateToFormat.getFullYear(), dateToFormat.getMonth(), dateToFormat.getDate()),
        ).format(FORMAT_DATE);
    }

    //set to not return to the welcome screen again
    async setIsOldUserAccount() {
        await this.bindBlipAccount();
        this.blipAccount = await this.AccountService2.me();
    }

    async goToTenantScreen() {
        try {
            this.isLoading = true;
            this.SegmentService.createOrganizationTrack(WELCOME_SCREEN_LETS_GO_BUTTON_CLICKED);

            if (this.isOrganizationEnabled) {
                await this.setIsOldUserAccount();

                //I have an invite for a tenantId
                if (this.tenantId) {
                    try {
                        this.$window.location.href = this.TenantService.getTenantUrl(this.tenantId, true);
                    } catch (e) {
                        console.log(e);
                        const error = JSON.parse(e);
                        if (error.reason.code == 67) {
                            this.$state.go(tenantNotFoundStateName);
                        } else {
                            console.error(e);
                        }
                    }
                } else {
                    //redirect to a contract list or a shared view
                    this.$state.go('auth.application.list');
                }
            } else {
                const tenantCorporate = await this.TenantService.TryGetDomainTenant();

                //this account is corporate
                if (tenantCorporate != undefined) {
                    const ownTenants = await this.TenantService.mine();
                    const alreadyInTenant = ownTenants.items.filter((ownTenant) => ownTenant.id === tenantCorporate.id);
                    if (alreadyInTenant.length == 0) {
                        this.$state.go(existingTenantStateName, { id: tenantCorporate.id, name: tenantCorporate.name });
                    } else {
                        this.$state.go('auth.application.list');
                        return;
                    }
                } else {
                    const dateAccountFormat = this.formatDate(this.blipAccount.creationDate);
                    const dateTenantFormat = this.formatDate(dateToCompareTenant);
                    //if creation date is greater than dateTimeFormat then create a contract
                    if (this.isPersonalTenantEnabled && dateAccountFormat > dateTenantFormat) {
                        try {
                            const tenantUrl = await this.TenantService.CreatePersonalTenant();
                            if (tenantUrl === '') {
                                this.$state.go(personalTenantCreationError);
                            }
                            await this.setIsOldUserAccount();
                            window.location.href = tenantUrl;
                        } catch (e) {
                            this.$state.go(personalTenantCreationError);
                        }
                    } else {
                        await this.setIsOldUserAccount();
                        this.$state.go('auth.application.list');
                        return;
                    }
                }
            }
        } catch (e) {
            console.error(e);
            const errorWhileRedirect = await this.$translate('welcomeScreen.errorWhileRedirect');
            this.BlipToastService.show('warning', {
                msg: errorWhileRedirect
            });
        } finally {
            this.isLoading = false;
        }
    }

    async checkFeatures() {
        try {
            this.isOrganizationEnabled = await this.TenantService.isOrganizationEnabled();
            this.isPersonalTenantEnabled = AccountFeatures.isPersonalTenantEnabled();
        } catch (e) {
            console.error(e);
        }
    }

    async bindBlipAccount() {
        await this.AccountService2.setExtras({
            ...this.blipAccount.extras,
            isOldUser: 'true',
        });
    }

    toggleWaOptIn = async (event: CustomEvent) => {
        this.waOptIn = event.detail.checked;
    }

    changePhoneNumber = (event: CustomEvent) => {
        const {
            detail: { value, code },
        } = event;

        this.phoneNumber = value === '' || isNaN(value)
            ? ''
            : code + value;
    }

    fillUserPositionOptions = () => {
        const userPositionKeys = ['analyst', 'assistant', 'developer', 'intern', 'student', 'coordinator', 'manager', 'director', 'founder'];

        userPositionKeys.forEach((c) => {
            const value = this.$translate.instant(
                `welcomeScreen.getToKnowUser.positionOptions.${c}`,
            );

            this.userPositionOptions.push({ label: value, value: c });
        });

        this.userPositionOptions.sort((a, b) => a.label.localeCompare(b.label));

        this.isUserPositionOptionsLoaded = true;
    }

    fillUserAreaOptions = () => {
        const userAreaKeys = ['customerService', 'data', 'finance', 'legal', 'marketing', 'product', 'hr', 'sales', 'it'];

        userAreaKeys.forEach((c) => {
            const value = this.$translate.instant(
                `welcomeScreen.getToKnowUser.areaOptions.${c}`,
            );

            this.userAreaOptions.push({ label: value, value: c });
        });

        this.userAreaOptions.sort((a, b) => a.label.localeCompare(b.label));

        this.isUserAreaOptionsLoaded = true;
    }

    fillUserInterestOptions = () => {
        const userInterestKeys = ['acquireOrFilterNewClients', 'automateClientCommunication', 'toolForServiceTeam', 'other'];

        userInterestKeys.forEach((c) => {
            const value = this.$translate.instant(
                `welcomeScreen.getToKnowUser.interestOptions.${c}`,
            );

            this.userInterestOptions.push({ label: value, value: c });
        });

        this.isUserInterestOptionsLoaded = true;
    }

    onChangeSelectInfo = async (event: CustomEvent) => {
        if (event.detail.name === USER_POSITION_SELECT_NAME) {
            this.userPosition = event.detail.value;
        } else if (event.detail.name === USER_AREA_SELECT_NAME) {
            this.userArea = event.detail.value;
        } else {
            this.userInterestOtherOption = undefined;
            this.userHasOtherInterest = event.detail.value === OTHER_INTEREST;
            this.userInterest = event.detail.value;
        }
    }

    onChangeUserInterestOtherOption = async (event: CustomEvent) => {
        this.userInterestOtherOption = event.detail.value;
    }
}
