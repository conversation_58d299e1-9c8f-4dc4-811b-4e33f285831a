@import '~assets/scss/main';

#welcome-screen {
    background: #fff;
    background-size: 40%;
    min-height: 100vh;
    top: 0;
    left: 0;
    z-index: $zindex-page;
    position: relative;
    min-width: 100vw;

    &::after {
        content: '';
        position: absolute;
        right: 0;
        background: url('~assets/img/soft-green-curve.png') no-repeat right
            bottom;
        min-height: 100vh;
        background-size: 100%;
        width: 100%;
        bottom: 0;
    }

    .container-content-page {
        max-width: 1366px;
        margin: 0 auto;
        display: flex;
        width: 100%;
    }

    @media (max-width: 1024px) {
        .container-content-page {
            > div {
                width: 50%;
            }

            .create-tenant-browser-canvas {
                bottom: 31%;
                right: 0%;
                margin-left: 52px;

                @media (min-height: 1024px) {
                    bottom: 40%;
                }
            }
        }
    }

    @media (max-width: 1286px) {
        .container-content-page {
            > div {
                width: 100%;
            }

            h1 {
                margin-left: auto;
                margin-right: auto;
            }

            > div:last-of-type {
                height: 30%;
                display: none;
            }

            .create-tenant-browser-canvas {
                width: 65%;
                left: 20%;
                top: auto;
                bottom: 0;
            }

            bds-select {
                width: 25vw;
            }
        }
    }

    @media (max-width: 750px) {
        min-width: 100%;
        position: absolute;
    }

    @media (max-width: 690px) {
        .completeness-registry {
            display: block;

            bds-select {
                width: 75vw;
            }
        }

        .user-area-container {
            margin: 8px 0px 8px 0px;
        }
    }

    @media (max-width: 350px) {
        margin-top: -130px;
    }

    @media (max-width: 320px) {
        margin-top: 0;
        position: fixed;
        .container-content-page {
            > div {
                width: 81vw;
            }
        }
    }

    @media (max-height: 667px) {
        &::after {
            display: none;
        }
    }

    .step-cards {
        margin-bottom: 15px;
        background-color: $color-neutral-light-whisper;
        padding: 20px;
    }

    .step-circle {
        background: $color-illustration-blue-brand;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        padding: 4px;
        display: flex;
        justify-content: center;
    }
}
