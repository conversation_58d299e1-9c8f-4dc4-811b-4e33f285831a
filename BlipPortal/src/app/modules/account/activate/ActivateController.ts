import { IStateParamsService, IStateService } from 'angular-ui-router';
import { AccountService2 } from 'modules/account/AccountService2';
import { SegmentService } from 'modules/application/misc/SegmentService';

export default class ActivateController {
    status: string;

    constructor(
        private AccountService2: AccountService2,
        private SegmentService: SegmentService,
        private $stateParams: IStateParamsService,
    ) {
        'ngInject';
        this.status = 'PENDING';
    }

    async activate() {
        const email = this.$stateParams.email;

        try {
            await this.AccountService2.save(email);
            this.SegmentService.setAccount({ email });
            this.SegmentService.createTrack('create-account-activated', {
                email
            });
            this.status = 'ACTIVATED';
        } catch (err) {
            this.status = 'FAILED';
            console.error(err);
            throw err;
        }
    }
}
