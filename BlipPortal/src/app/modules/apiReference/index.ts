import * as angular from 'angular';
import 'angular-ui-router';
import { IStateProvider } from 'angular-ui-router';

export default angular
    .module('apireference', ['ui.router'])
    .config(($stateProvider, $urlRouterProvider) => {
        'ngInject';

        // TODO Change to a cached call to overpass Github API rate limits (5000 / hour
        $stateProvider.state('apireference', {
            url: 'apiReference/:path',
            external: true,
        });
    })
    .run(($rootScope, $window) => {
        $rootScope.$on(
            '$stateChangeStart',
            (event, toState, toParams, fromState, fromParams) => {
                if (toState.external) {
                    event.preventDefault();
                    const docsUrl = 'https://hmg-docs.blip.ai/' + toParams.path;
                    $window.open(docsUrl, '_blank');
                }
            },
        );
    }).name;
