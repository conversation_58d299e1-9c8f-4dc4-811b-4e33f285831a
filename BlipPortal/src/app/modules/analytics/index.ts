// Misc
import * as angular from 'angular';

// 3rd party
import 'angular-drag-and-drop-lists';

// Modules
import messaginghub from 'modules/messaginghub';
import shared from 'modules/shared';
import statistics from 'modules/statistics';
import { analyticsComponents } from 'modules/analytics/components';

// Factory or Services
import { AnalyticsServiceFactory } from './services/AnalyticsServiceFactory';
import { UserAnalyticsService } from './services/UserAnalyticsService';
import { MessageAnalyticsService } from './services/MessageAnalyticsService';
import { EventTrackAnalyticsService } from './services/EventTrackAnalyticsService';
import { ChartService } from './services/ChartService';
import { SankeyService } from './services/SankeyService';

//
export default angular
    .module('analytics', ['dndLists', analyticsComponents, messaginghub, shared, statistics])
    .service('AnalyticsServiceFactory', AnalyticsServiceFactory)
    .service('UserAnalyticsService', UserAnalyticsService)
    .service('MessageAnalyticsService', MessageAnalyticsService)
    .service('EventTrackAnalyticsService', EventTrackAnalyticsService)
    .service('SankeyService', SankeyService)
    .service('ChartService', ChartService)
    .name;
