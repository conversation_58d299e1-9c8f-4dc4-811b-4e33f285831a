import * as moment from 'moment';
import { AnalyticsData } from 'modules/analytics/models';
import { getDatesInPeriod, formatDateFromString } from 'data/date';

/**
 *
 * Return date in formate (DD/MM)
 * @param d {Date} - javascript date
 *
 */
export const beautifyDate = (d: string, format: string, keepLocalTime: boolean = true) => moment(d).utc(keepLocalTime).format(format);

/**
 * Transpose matrix
 */
export const transposeMatrix = m => m[0].map((_, c) => m.map(r => r[c]));

/**
 * Concatenate day with count
 * @param array {Array} - Array to be joined
 * @param x {AnalyticsData}
 */
export const concatDayWithCount = (array: Array<any>, x: AnalyticsData, _, a) => {
    return array.concat([[beautifyDate((x.intervalStart || x.storageDate), 'MMM DD'), x.count]]);
};

/**
 * Receives an API data and gets a matrix with format [[day, count]]
 * @param data {AnalyticsData}
 */
export const GetMatrixFromData = ({ start, end }) => (data: AnalyticsData[]) => {
    const startDate = formatDateFromString(start);
    const endDate = formatDateFromString(end);
    const normalizeDate = d => moment(d).utc(false).format('DD/MM/YYYY');

    return getDatesInPeriod(startDate, endDate, 'days')
        .map(d => {
            const e = data.find(i => {
                const date = i.intervalStart || i.storageDate;
                return normalizeDate(date) == normalizeDate(d.utc(true));
            });

            return {
                intervalStart: d,
                storageDate: d,
                count: e ? e.count : 0,
            };
        })
        .reduce(concatDayWithCount, []);
};

/**
 * Function that returns another function to reduce method. Receives a parent array and parent index to
 * join count on its respective day on matrix
 * @param parent {Array}
 * @param pIndex {Number}
 */
const addCountOnDay = <T>(parent: Array<T>[], pIndex: number) => (
    acc,
    curr,
    index,
) => {
    if (pIndex == 0) {
        return acc.concat([curr]);
    }

    parent[index] = parent[index].concat(curr[1]);
    return acc;
};

/**
 * Receives an array of api data and gets an matrix
 * @param dataAnalysis {Array}
 */
export const GenerateMatrixFromApiData = <T>(dataAnalysis: AnalyticsData[][], { start, end }): Array<T>[] =>
    dataAnalysis
        .map(GetMatrixFromData({ start, end }))
        .reduce(
            (acc1: Array<any>, curr1, i1) =>
                acc1.concat(curr1.reduce(addCountOnDay(acc1, i1), [])),
            [],
        );
