import { cookies } from 'angular';
import * as moment from 'moment';
import { AnalyticsData, ViewReport } from './models';
import { ChartType } from './models/Chart';

const DATE_FORMAT = 'DD/MM/YYYY';
const DATE_TIME_FORMAT = 'DD/MM/YYYY HH:mm';

type CookiePeriod = {
    start: Date,
    end: Date
};

type PeriodConstants = {
    defaultStartFromToday: number,
    defaultEndFromToday: number,
    validStartFromToday: number,
    validEndFromToday: number,
    maximumDaysToSelect?: number
};

/**
 * Convert an instance of google.visualization.DataTable to CSV
 * @param {google.visualization.DataTable} dataTable_arg DataTable to convert
 * @return {String} Converted CSV String
 */
export const dataTableCSV = (dataTable) => {
    const dtCols = dataTable.getNumberOfColumns();
    const dtRows = dataTable.getNumberOfRows();

    const csvCols = [];
    let csvOut;

    // Iterate columns
    for (let i = 0; i < dtCols; i++) {
        const label = dataTable.getColumnLabel(i);

        // Replace any commas in column labels
        if (label != '') {
            csvCols.push(label.replace(/,/g, ''));
        }
    }

    // Create column row of CSV
    csvOut = csvCols.join(',') + '\r\n';

    // Iterate rows
    for (let i = 0; i < dtRows; i++) {
        const rawCol = [];
        for (let j = 0; j < dtCols; j++) {
            let formattedValue = dataTable.getFormattedValue(i, j);

            //add quotes to the value when they have a comma
            if (formattedValue.includes(',')) {
                formattedValue = `"${formattedValue}"`;
            }

            // Is a color attribute
            if (formattedValue.startsWith('#') && j == dtCols - 1) {
                continue;
            }
            // Replace any commas and line breaks in row values
            rawCol.push(formattedValue.replace(/(\r\n|\n|\r)/gm, ' '));
        }
        // Add row to CSV text
        csvOut += rawCol.join(',') + '\r\n';
    }

    return csvOut;
};

/**
 * Gets an action (or action list) and returns it translated if possible
 *
 * @param  $translate - Translate service
 * @param actions - Actions that will be changed
 */
export const getTranslatedActions = ($translate, actions: any, chartType = undefined, dimension = undefined) => {
    const keyPrefix = getTranslationPath(chartType, dimension);

    const getTranslationByKey = (path) => {
        const cleanPath = nomalizeEventTrackString(path);
        const cleanKey = `${keyPrefix}.${cleanPath}`;
        const translated = $translate.instant(cleanKey);
        return translated.startsWith(keyPrefix) ? cleanPath : translated;
    };

    switch (typeof actions) {
        case 'object':
            return actions.map(getTranslationByKey);
        case 'string':
            return getTranslationByKey(actions);
    }
};

/**
 * Translate the chart text
 * @param $translate Translation service instance
 * @param category Chart category
 * @param type Chart type
 * @param dimension Chart dimension
 * @returns Translated text
 */
export const getChartTranslation = ($translate, category, type: string, dimension: string) => {
    const keyPrefix = getTranslationPath(type, dimension);

    const cleanKey = `${keyPrefix}.${category}`;
    const translated = $translate.instant(cleanKey);
    return translated.startsWith(keyPrefix) ? category : translated;
};

/**
 * Replacing the first occourence of single or double open curly brackets or the last occourence of single or double close
 * curly brackets to avoid chart breakes if they exists on those positions. Trimming the string before return it.
 *
 * @param value - Event Track value
 */
export const nomalizeEventTrackString = (value: string) => {
    if (value !== undefined) {
        value = value.replace(/{{|}}/g, '');
        return value.trim();
    } else {
        return value;
    }
};

/**
 * Returns period object, that contais start date and end date
 *
 * @param initialInterval - Period interval
 */
export const setupIntervalPeriod = (initialInterval = 30, hasTime) => {
    const intervalFormat = hasTime ? DATE_TIME_FORMAT : DATE_FORMAT;

    return {
        start: moment()
            .subtract(initialInterval, 'days')
            .format(intervalFormat),
        end: moment()
            .format(intervalFormat),
    };
};

/**
 * Returns DaterangerPickerPeriod object, that contais selected period and valid period
 *
 * @param periodConstants - Object with period configuration
 * @param periodConstants.defaultStartFromToday - Number of days that will be subtracted from today and set as default selected start date on picker.
 * @param periodConstants.defaultEndFromToday - Number of days that will be added from today and set as default selected end date on picker.
 * @param periodConstants.validStartFromToday - Number of days that will be subtracted from today and set as the lowest date available to be selected.
 * @param periodConstants.validEndFromToday - Number of days that will be added from today and set as the highest date available to be selected.
 * @param periodConstants.maximumDaysToSelect - Number of days that will be allowed to be selected in available date range.
 * @param periodFromCookies - Object with startDate and endDate saved on cookies.
 * @param $cookies - Cookies service
 * @param cookiesPeriodKey - Daterangepicker saved cookie key
 */
export const setupDaterangePickerPeriod = (periodConstants: PeriodConstants, $cookies: cookies.ICookiesService, cookiesPeriodKey: string, hasTime: boolean) => {

    const { defaultStartFromToday, defaultEndFromToday, validStartFromToday, validEndFromToday, maximumDaysToSelect } = periodConstants;

    let startDate;
    let endDate;

    const periodFromCookies = simplePeriodFromCookies($cookies, cookiesPeriodKey);
    if (periodFromCookies) {
        startDate = moment(periodFromCookies.start, DATE_TIME_FORMAT).toDate();
        endDate = moment(periodFromCookies.end, DATE_TIME_FORMAT).toDate();
    } else {
        startDate = moment().add(defaultStartFromToday, 'days').toDate();
        endDate = moment().add(defaultEndFromToday, 'days').toDate();
    }

    if (!hasTime || !periodFromCookies) {
        startDate = moment(startDate).startOf('day').toDate();
        endDate = moment(endDate).endOf('day').toDate();
    }

    return {
        selectedPeriod: {
            startDate,
            endDate,
        },
        validPeriod: {
            startDate: moment().add(validStartFromToday, 'days').toDate(),
            endDate: moment().add(validEndFromToday, 'days').toDate(),
        },
        maxRangeInDays: maximumDaysToSelect || validStartFromToday * -1,
    };
};

export const simplePeriodFromCookies = ($cookies, key): CookiePeriod => {
    return $cookies.getObject(key);
};

/**
 * If cookie has key passed as param and returns a period object, use it.
 * Otherwise, setup period based on initialInterval
 *
 * @param $cookies - $cookies service
 * @param key - Cookie key
 * @param initialInterval - Initial interval in case of undefined cookie
 */
type CookiePeriodSetup = {
    $cookies: any,
    key: string,
    initialInterval: number,
    period?: {
        start: any,
        end: any
    },
    hasTime?: boolean
};

export const maybePeriodFromCookies = ({ $cookies, key, initialInterval, period, hasTime = false }: CookiePeriodSetup) => {
    if ($cookies.getObject(key)) {
        return $cookies.getObject(key);
    }

    const intervalPeriod = period || setupIntervalPeriod(initialInterval, hasTime);

    $cookies.putObject(key, intervalPeriod, {
        expires: new Date(
            moment(new Date())
                .add(1, 'days')
                .toDate(),
        ),
    });

    return $cookies.getObject(key);
};

/**
 * Setup period on cookies
 * @param $cookies - $cookies service
 * @param period - { start: Date, end: Date }
 */
export const setupPeriodToCookies = ({ $cookies, period, key }) =>
    $cookies.putObject(key, period, {
        expires: new Date(
            moment(new Date())
                .add(1, 'days')
                .toDate(),
        ),
    });

/**
 * Verify if is all events empty
 * @param e - Array of events
 */
export const isEmptyEvents = (e: AnalyticsData[]) =>
    e.length == 0 || e.reduce((counter, e) => counter + e.count, 0) == 0;

/**
 * @param {Array | Number} data
 */
export const getEmptyDataByType = (data) => (typeof data == 'number' ? 0 : []);

/**
 */
export const sortByStorageDate = (x: AnalyticsData, y: AnalyticsData) => {
    if (!x.storageDate || !y.storageDate) {
        return;
    }

    const d1 = moment(x.storageDate);
    const d2 = moment(y.storageDate);

    if (d1 > d2) {
        return 1;
    }
    if (d1 < d2) {
        return -1;
    }

    return 0;
};

/**
 * Returns one element for every kind dictated by one function fn()
 *
 * @param arr: Array to be reduced
 * @param fn: function for comparing elements
 */
export const uniqueElementsBy = (arr: Array<any>, fn: Function) => {
    return arr.reduce((acc, v) => {
        if (!acc.some(x => fn(v, x))) {
            acc.push(v);
        }
        return acc;
    }, []);
};

/**
 */
export const sortByModifiedAt = (x: ViewReport, y: ViewReport) => {
    if (x.modifiedAt < y.modifiedAt || x.modifiedAt == undefined) {
        return 1;
    }
    if (x.modifiedAt > y.modifiedAt || y.modifiedAt == undefined) {
        return -1;
    }

    return 0;
};

const getTranslationPath = (type: string, dimension: string) => {
    return type != ChartType.Line && dimension == 'users'
        ? 'reports.categories.period'
        : 'reports.categories';
};
