<div class="{{$ctrl.styles.container}} dimension-card-item relative">
    <button
        ng-if="$ctrl.data.filters.length == 0"
        ng-click="$ctrl.handleOnAddItem(true)"
        class="bp-btn bp-btn--bot bp-btn--dashed justify-center h3-1 w-90 ml3 mb4">
        + {{'modules.analytics.userDimension.addFilters' | translate}}
    </button>
    
    <div ng-if="$ctrl.data.filters.length > 0" class="card--accordion bp-bg-offwhite br3 ph3 pt3">
        <div class="w-100 relative">
            <div ng-repeat="filter in $ctrl.data.filters track by filter.id" class="w-100 relative">
                <hr ng-if="$index != 0" class="dashed mt0 mb4"></hr>
                <div class="relative pb4 add-remove-container z-hover">
                    <dimension-filter data="filter" condition-disabled="$ctrl.conditionDisabled" on-changes="$ctrl.handleOnChanges($event)"></dimension-filter>
                    <add-remove class="z-1 w-100 centered-add-remove centered-add-remove--bottom db tc absolute"
                    on-add="$ctrl.handleOnAddItem(true)"
                    on-remove="$ctrl.handleOnRemoveItem($index)"
                    show-add="$index == ($ctrl.data.filters.length - 1)"></add-remove>
                </div>
            </div>
            <i ng-if="$ctrl.onRemoveDimension && $ctrl.data.filters.length > 0" ng-click="$ctrl.$onRemoveDimension()" class="icon-delete color-gray-light-5 bp-lh-simple"></i>
        </div>
    </div>
</div>
