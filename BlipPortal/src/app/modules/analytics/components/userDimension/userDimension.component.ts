import template from './UserDimensionView.html';
import * as styles from './userDimension.module.scss';
import {
    UserDimension,
    DimensionFilter,
} from 'modules/analytics/models/dimensions';
import { EventEmitter } from 'modules/shared/EventEmitter';
import { ODataOperator, ODataFunction } from 'types/OData';
import { UserFeatures } from 'modules/application/detail/users/UserFeatures';
import { IToggleable } from 'feature-toggle-client';

/**
 * Component to encapsulate users dimension rules and fields.
 * Dependencies: dimensionFilter.component
 */
class UserDimensionController implements IToggleable {
    /**
     * Disable type select
     */
    disableType: boolean;

    /**
     * Styles object
     */
    styles: any;

    //
    data: UserDimension;
    private isUsingStartsWithOnContactsSearch: boolean;

    /**
     * Callback when add new item to dimension
     */
    onAddItem: (init) => void;

    /**
     * Callback when remove item from dimension
     */
    onRemoveItem: (index) => void;

    /**
     * Callback called when filter changes occur
     */
    onChanges: ($event) => void;

    /**
     * Callback when remove this dimension from metric
     */
    onRemoveDimension: ($event) => void;

    constructor(private $element, private $timeout) {
        this.disableType = false;
        this.styles = styles;
        this.checkFeatures();
    }

    async checkFeatures(): Promise<any> {
        this.isUsingStartsWithOnContactsSearch = await UserFeatures.isUsingStartsWithOnContactsSearch();
    }

    $onRemoveDimension() {
        if (this.onRemoveDimension) {
            this.onRemoveDimension(EventEmitter({ dimension: this.data }));
        }
    }

    handleOnAddItem(startWithStartsWith = false) {
        if (this.onAddItem) {
            this.onAddItem(
                EventEmitter({
                    init: {
                        condition: startWithStartsWith
                            ? (this.isUsingStartsWithOnContactsSearch
                                ? ODataFunction.StartsWith
                                : ODataFunction.Contains)
                            : ODataOperator.Equals,
                    },
                }),
            );
        }
    }

    handleOnRemoveItem(index = undefined) {

        if (this.onRemoveItem) {
            this.onRemoveItem(EventEmitter({ index }));
        }

        this.handleOnChanges({ changes: this.data });
    }

    handleOnChanges({ changes }: { changes: DimensionFilter | UserDimension }) {
        if (this.onChanges) {
            this.onChanges(EventEmitter({ changes }));
        }
    }
}

export const UserDimensionComponent = {
    template,
    controller: UserDimensionController,
    controllerAs: '$ctrl',
    bindings: {
        data: '<',
        onAddItem: '&?',
        onRemoveItem: '&?',
        onRemoveDimension: '&?',
        onChanges: '&?',
        disableType: '<?',
        conditionDisabled: '<?',
    },
};
