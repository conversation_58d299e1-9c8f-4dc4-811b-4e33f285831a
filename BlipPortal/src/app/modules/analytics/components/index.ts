import * as angular from 'angular';
import { chart } from 'modules/analytics/components/chart';
import { AnalyticsChartComponent } from 'modules/analytics/components/analyticsChart/analyticsChart.component';
import { DimensionFilterComponent } from 'modules/analytics/components/dimensionFilter/dimensionFilter.component';
import { UserDimensionComponent } from 'modules/analytics/components/userDimension/userDimension.component';
import DimensionFilterReact from './dimensionFilter/dimensionFilterReact/dimensionFilterReact';
import { react2angular } from 'react2angular';
import TranslateService from 'modules/translate/TranslateService';

export const analyticsComponents = angular
    .module('analyticsComponents', [ chart ])
    .service('translateService', TranslateService)
    .component('analyticsChart', AnalyticsChartComponent)
    .component('dimensionFilter', DimensionFilterComponent)
    .component('userDimension', UserDimensionComponent)    
    .component('dimensionFilterReact', react2angular(DimensionFilterReact, ['filter', 'conditionDisabled', 'onChanges'], ['translateService']))
    .name;
