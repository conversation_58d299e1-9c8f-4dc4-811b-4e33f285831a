@import '~assets/scss/main';
@import '~blip-ds/dist/collection/styles/colors';

analytics-chart {
    margin-top: 4 * $u;
    display: block;
    width: 100%;

    .icon-info {
        opacity: 0;
        padding-bottom: 5px;
    }

    &:hover {
        .card-handleable-content {
            visibility: visible;
        }

        .icon-info {
            opacity: 1;
            color: $color-content-default;
        }
    }

    .card-handleable-content {
        visibility: hidden;
    }

    button.drag-handle {
        position: absolute;
        top: 10px;
        right: 0;
        color: $color-content-disable;
        padding: 1 * $u;

        &:focus {
            padding: 1 * $u;
        }

        &:hover {
            cursor: move;
        }
    }

    select {
        width: 100%;
    }

    .controls {
        color: $color-content-disable;
        width: 100%;
    }

    .card-header {
        @include flex-box;
        padding-bottom: 0;
        color: $color-content-default;
    }

    card .card-content {
        padding-top: 0;
    }

    .card-title {
        width: 90%;
        padding-top: 0;
        color: $color-content-default !important;
    }

    div.google-visualization-tooltip {
        pointer-events: none;
    }

    svg > g > g:last-child {
        pointer-events: none;
    }
    .google-visualization-table-table * {
        .google-visualization-table-tr-even .tl {
             background-color: $color-surface-1;
        }

        .google-visualization-table-tr-odd .tl {
             background-color: $color-surface-1;
        }
        background-color: $color-surface-1;
    }
}

.analytics-counter {
    font-size: 100px;
    padding-bottom: 2rem;
}

.chart-types {
    margin-top: 20px;

    .chart-type-item {
        display: inline-block;
        border: 1px solid $color-surface-2;
        padding: 7px 14px;
        border-radius: 4px;

        &:focus {
            outline: 0;
        }
    }

    .chart-type-item--active {
        background: $color-primary;
        border: 1px solid $color-primary;
        color: $color-surface-1;
    }
}

.chart-type-item--with-error {
    color: $color-error !important;
}

$base: $color-surface-2;
$stripe: $color-surface-3;

.analytics-chart-placeholder {
    height: 290px;

    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: placeHolderShimmer;
    animation-timing-function: linear;

    background-image: linear-gradient(
        to left,
        $base 0%,
        $stripe 20%,
        $base 40%,
        $base 100%
    );
    background-size: 980px 898px;

    span {
        flex-grow: 1;
        background-color: $color-surface-1;
    }
}

@keyframes placeHolderShimmer {
    0% {
        background-position: -490px 0;
    }
    100% {
        background-position: 490px 0;
    }
}
