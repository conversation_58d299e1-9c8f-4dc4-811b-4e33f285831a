import './analyticsChart.scss';
import AnalyticsChartController from './AnalyticsChartController';
import AnalyticsChartView from './AnalyticsChartView.html';
import { ViewChart } from 'modules/analytics/models';

export class ChartAction {
    chart: ViewChart;

    constructor(init?: Partial<ChartAction>) {
        Object.assign(this, init);
    }
}

/**
 * These classes are used to dispatch a typed action to parent controller that's using this component
 */
export class Change extends ChartAction {
    constructor(props) {
        super(props);
    }
}
export class Delete extends ChartAction {
    constructor(props) {
        super(props);
    }
}
export class Edit extends ChartAction {
    constructor(props) {
        super(props);
    }
}

/**
 * Component configurations
 */
export const AnalyticsChartComponent = {
    template: AnalyticsChartView,
    controller: AnalyticsChartController,
    controllerAs: '$ctrl',
    bindings: {
        chart: '<',
        categories: '<',
        reportOwner: '<',
        onAction: '&?',
        editMode: '@?',
        isLoading: '<?',
        info: '@?',
    },
};
