<button dnd-handle class="drag-handle no-style" ng-auth-write="disallow" ng-if="$ctrl.isOwner()">
    <i class="icon icon-s icon-material" title="Mover">&#xE25D;</i>
</button>
<!-- create & edit -->
<div ng-if="$ctrl.currentState == $ctrl.STATES.CREATE || $ctrl.currentState == $ctrl.STATES.EDIT">
    <select ng-init="$view.category = $ctrl.chart.category" ng-model="$view.category" ng-options="category for category in $ctrl.categories">
        <option value="">{{'modules.analytics.analyticsChart.selectContext' | translate}}</option>
    </select>

    <div class="chart-types">
        <p ng-class="{'chart-type-item--with-error': $ctrl.hasNoType}"><b translate>modules.analytics.analyticsChart.displayType</b></p>
        <div class="chart-type-item" ng-class="{'chart-type-item--active': $ctrl.chartType == 'list'}" ng-click="$ctrl.changeType('list')">
            <icon-dpr>&#xE896;</icon-dpr> {{'utils.misc.list' | translate}}
        </div>
        <div class="chart-type-item" ng-class="{'chart-type-item--active': $ctrl.chartType == 'line'}" ng-click="$ctrl.changeType('line')">
            <icon-dpr>&#xE922;</icon-dpr> {{'utils.misc.line' | translate}}
        </div>
        <div class="chart-type-item" ng-class="{'chart-type-item--active': $ctrl.chartType == 'pie'}" ng-click="$ctrl.changeType('pie')">
            <icon-dpr>&#xE6C4;</icon-dpr> {{'utils.misc.pie' | translate}}
        </div>
        <div class="chart-type-item" ng-class="{'chart-type-item--active': $ctrl.chartType == 'bar'}" ng-click="$ctrl.changeType('bar')">
            <icon-dpr>&#xE01D;</icon-dpr> {{'utils.misc.bars' | translate}}
        </div>
        <div class="chart-type-item" ng-class="{'chart-type-item--active': $ctrl.chartType == 'column'}" ng-click="$ctrl.changeType('column')">
            <icon-dpr>&#xE01D;</icon-dpr> {{'utils.misc.column' | translate}}
        </div>
    </div>

    <div class="card-actions">
        <button ng-click="$ctrl.cancel()" translate>utils.forms.cancel</button>
        <button class="bp-btn bp-btn--bot bp-btn--small" ng-click="$ctrl.view({ category: $view.category, chartType: $ctrl.chart.chartType })" translate>utils.forms.confirm</button>
    </div>
</div>

<!-- view -->
<div ng-switch="$ctrl.chart.chartType">
    <div ng-switch-when="list" ng-if="$ctrl.currentState == $ctrl.STATES.VIEW">
        <div class="overflow-scroll" ng-if="$ctrl.chart.data[0].length > 0">
            <table class="u-full-width table-stripped table-padded">
                <tr ng-repeat="data in $ctrl.chart.data[0] track by $index">
                    <td>{{$ctrl.chart.labels[$index]}}</td>
                    <td>{{$ctrl.getPercentage($ctrl.chart.data[0], data)}}%</td>
                    <td>{{data}}</td>
                </tr>
            </table>
        </div>
        <h5 ng-if="$ctrl.chart.data === null || $ctrl.chart.data[0].length === 0" class="bp-c-rooftop tc" translate>
            utils.errorMsg.35
        </h5>
    </div>
    <div ng-switch-when="counter">
        <h2 class="bp-c-bot tc ma0" ng-bind="$ctrl.bindCounter($ctrl.chart.data[0])"></h2>
    </div>
    <div ng-switch-when="pie" ng-if="$ctrl.currentState == $ctrl.STATES.VIEW">
        <chart height="200" type="{{$ctrl.chart.chartType}}" data="$ctrl.chart.data.length > 0 ? $ctrl.chart.data[0] : null" labels="$ctrl.chart.labels"
            options="$ctrl.getChartOptions()"></chart>
    </div>
    <div ng-switch-default ng-if="$ctrl.currentState == $ctrl.STATES.VIEW">
        <chart height="200" ng-click="$ctrl.test($ctrl.chart)" type="{{$ctrl.chart.chartType}}" data="$ctrl.chart.data.length > 0 ? $ctrl.chart.data : null"
            labels="$ctrl.chart.labels" series="$ctrl.chart.series" options="$ctrl.getChartOptions()"></chart>
    </div>
</div>
