import { Change, Delete, Edit } from './analyticsChart.component';
import { IRootScopeService } from 'angular';
import { AccountService2 } from 'modules/account/AccountService2';
import { SegmentService } from 'modules/application/misc/SegmentService';
import * as uuid from 'uuid';
import { ViewChart } from 'modules/analytics/models';
import { ChartService } from 'modules/analytics/services/ChartService';
import { dataTableCSV, getChartTranslation } from 'modules/analytics/Utils';
import CSVParser from 'assets/js/export-csv';
import { dataURItoBlob } from 'data/blob';
import { ChartType } from 'modules/analytics/models/Chart';

enum States {
    CREATE = 'CREATE',
    VIEW = 'VIEW',
    EDIT = 'EDIT',
}

export default class AnalyticsChartController {
    chartImg: any;
    dataTableMatrix: any;
    chartDataBuffer: any;
    chartOptions: { legend: { position: string; }; } | { legend?: undefined; };
    info: string;
    chartTypeBuffer: string;
    chartContainerId: any;
    editMode: string;
    reportOwner: any;
    hasNoType: boolean;
    chartType: ChartType | string;
    currentState: string;
    chart: ViewChart;
    editMsg: string;
    newChart: string;
    onAction(action: any): any { }
    me: any;

    constructor(
        private $rootScope: IRootScopeService,
        private $translate,
        private AccountService2: AccountService2,
        private SegmentService: SegmentService,
        private $timeout,
        private ChartService: ChartService,
        private FileSaver, // External service
    ) {
        'ngInject';

        this.AccountService2.me().then((me) => {
            this.me = me;
        });

        this.handleTranslations();
    }

    $onInit() {
        this.setUpChartSettings();
    }

    private setUpChartSettings() {
        this.chartContainerId = this.chart.id || uuid.v4();
        this.currentState = this.chart && this.chart.isNew ? States.CREATE : States.VIEW;
        this.chartTypeBuffer = this.chart.chartType;
        this.chartOptions = (this.chart.chartType != ChartType.Line && this.chart.chartType != ChartType.Pie)
            ? { legend: { position: 'none' } }
            : {};
    }

    private handleTranslations() {
        this.$translate([
            'modules.analytics.analyticsChart.newChart',
            'utils.forms.edit',
        ]).then((translations) => {
            this.newChart =
                translations['modules.analytics.analyticsChart.newChart'];
            this.editMsg = translations['utils.forms.edit'];
        });
    }

    get chartTitle() {
        if (this.chart.name) {
            return this.chart.name;
        }

        switch (this.currentState) {
            case States.CREATE:
                return this.newChart;
            case States.EDIT:
                return this.editMsg;
            case States.VIEW:
                return getChartTranslation(
                    this.$translate,
                    this.chart.category?.trim(),
                    this.chart.chartType,
                    this.chart.dimension);
        }
    }

    get isChangeble() {
        switch (this.chartTypeBuffer) {
            case ChartType.List:
            case ChartType.Table:
            case ChartType.Counter:
                return false;
            default:
                return true;
        }
    }

    onChartReady($elementData, $chartImg) {
        this.dataTableMatrix = $elementData;
        this.chartImg = $chartImg;
    }

    downloadAsCsv($event) {
        this.downloadEvent();
        const csvData = dataTableCSV(this.dataTableMatrix);
        const file = CSVParser.CSVStringToBlob(csvData);
        this.FileSaver.saveAs(file, `${this.chartTitle}.csv`);

        $event.stopPropagation();
        $event.preventDefault();
    }

    downloadAsPng($event) {
        this.downloadEvent();
        const file = dataURItoBlob(this.chartImg);
        this.FileSaver.saveAs(file, `${this.chartTitle}.png`);

        $event.stopPropagation();
        $event.preventDefault();
    }

    downloadEvent() {
        this.SegmentService.createTrack('analytics-custom-reports-exported');
    }

    private updateChartVisualization(chartType: ChartType) {
        this.SegmentService.createTrack('analytics-custom-reports-chart-view-changed');

        if (!this.chartDataBuffer || this.chartDataBuffer.length == 0 || this.chart.data.length == 0) {
            this.chartDataBuffer = this.chart.data;
        }

        let chartData = this.chart.data;

        if (this.chartTypeBuffer == 'bar' || this.chartTypeBuffer == 'column') {
            if (chartType == ChartType.Table) {
                chartData = this.chart.data.map(v => v.slice(0, -1)); // Remove column that have colors properties
            } else {
                chartData = this.chartDataBuffer; // Bring back column with color properties
            }
        }

        this.chart = new ViewChart({
            ...this.chart,
            data: chartData,
            chartType,
        });

        if (this.chart.data.length == 0) {
            return;
        }

        this.$timeout(() => {
            this.ChartService.updateChartProps(this.$rootScope, { containerId: this.chartContainerId });
        });
    }

    $dispatch($action) {
        this.onAction({ $action });
    }

    changeType(chartType) {
        this.chartType = chartType;
        this.hasNoType = false;
    }

    bindCounter(data) {
        if (data) {
            return typeof data == 'number'
                ? data
                : data.reduce((n, c) => n + c, 0);
        }
    }

    view(chart) {
        if (!chart.category) {
            return;
        }

        if (!this.chartType) {
            this.hasNoType = true;

            return;
        }

        if (this.currentState !== States.VIEW) {
            const chartType = this.chartType;
            this.$dispatch(new Change({
                chart: {
                    ...this.chart,
                    ...chart,
                    chartType
                }}));
            this.chartType = undefined;
        }
        this.currentState = States.VIEW;
    }

    isOwner() {
        return this.me && this.reportOwner && this.reportOwner.email == this.me.email;
    }

    edit() {
        if (this.editMode == 'modal') {
            this.$dispatch(new Edit({ chart: this.chart }));
        } else {
            this.currentState = States.EDIT;
            this.chartType = this.chart.chartType;
        }
    }

    cancel() {
        switch (this.currentState) {
            case States.CREATE:
                this.$dispatch(new Delete({ chart: this.chart }));
                break;
            case States.EDIT:
            default:
                this.currentState = States.VIEW;
                break;
        }

        this.chartType = undefined;
    }

    delete() {
        this.$dispatch(new Delete({ chart: this.chart }));
        this.chartType = undefined;
    }

    getPercentage(array, value) {
        let total = 0;
        array.map((n) => (total += n));
        const percentage =
            value == 0 && total == 0 ? 0 : (value / total * 100).toFixed(2);

        return percentage;
    }
}
