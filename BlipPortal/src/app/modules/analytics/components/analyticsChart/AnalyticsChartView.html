<card ng-if="$ctrl.isOwner()"
    class="u-full-width analytics-chart-card card--with-header-options tc"
    card-info="{{$ctrl.info}}"
    item-title="{{$ctrl.chartTitle}}"
    on-edit="$ctrl.edit()"
    on-exclude="$ctrl.delete()"
    show-options="$ctrl.chart.chartType != 'counter'">
    <card-options>
        <div ng-if="$ctrl.isChangeble">
            <div class="mb3 f3" translate>utils.misc.visualization</div>
            <ul class="list ma0 f4">
                <li ng-click="$ctrl.updateChartVisualization($ctrl.chartTypeBuffer)" translate>utils.misc.chart</li>
                <li ng-click="$ctrl.updateChartVisualization('table')" translate>utils.misc.table</li>
            </ul>
            <hr>
        </div>
        <div class="mb3 f3 tl" translate>utils.forms.export</div>
        <ul class="list ma0 f4">
            <li ng-click="$ctrl.downloadAsCsv($event)">CSV</li>
            <li ng-if="$ctrl.chart.chartType != 'table' && $ctrl.chart.chartType != 'list'" ng-click="$ctrl.downloadAsPng($event)" translate>utils.misc.image</li>
        </ul>
    </card-options>
    <div class="flex items-center justify-center" ng-if="$ctrl.isLoading">
        <div class="analytics-chart-placeholder">
            <div class="flex flex-column items-center justify-center h-100">
                <span class="w-100"></span>
                <img src="/assets/img/placeholders/charts/List.svg" ng-if="$ctrl.chart.chartType == 'list'"></img>
                <img src="/assets/img/placeholders/charts/Line.svg" ng-if="$ctrl.chart.chartType == 'line'"></img>
                <img src="/assets/img/placeholders/charts/Pie.svg" ng-if="$ctrl.chart.chartType == 'pie'"></img>
                <img src="/assets/img/placeholders/charts/Bar.svg" ng-if="$ctrl.chart.chartType == 'bar'"></img>
                <img src="/assets/img/placeholders/charts/Column.svg" ng-if="$ctrl.chart.chartType == 'column'"></img>
                <img src="/assets/img/placeholders/charts/Counter.svg" ng-if="$ctrl.chart.chartType == 'counter'"></img>
                <span class="w-100"></span>
            </div>
        </div>
    </div>
    <div class="pa4">
        <chart options="$ctrl.chartOptions"
            container-id="{{$ctrl.chartContainerId}}"
            data="$ctrl.chart.data"
            type="$ctrl.chart.chartType"
            on-chart-ready="$ctrl.onChartReady($elementData, $chartImg)"
            ng-if="!$ctrl.isLoading"
            ></chart>
    </div>
</card>

<card ng-if="!$ctrl.isOwner()"
    card-info="{{$ctrl.info}}"
    class="u-full-width analytics-chart-card card--with-header-options tc"
    item-title="{{$ctrl.chartTitle}}"
    show-options="$ctrl.chart.chartType != 'counter'">
    <card-options>
        <div ng-if="$ctrl.isChangeble">
            <div class="mb3 f3" translate>utils.misc.visualization</div>
            <ul class="list ma0 f4">
                <li ng-click="$ctrl.updateChartVisualization($ctrl.chartTypeBuffer)" translate>utils.misc.chart</li>
                <li ng-click="$ctrl.updateChartVisualization('table')" translate>utils.misc.table</li>
            </ul>
            <hr>
        </div>
        <div class="mb3 f3 tl" translate>utils.forms.export</div>
        <ul class="list ma0 f4">
            <li ng-click="$ctrl.downloadAsCsv($event)">CSV</li>
            <li ng-if="$ctrl.chart.chartType != 'table' && $ctrl.chart.chartType != 'list'" ng-click="$ctrl.downloadAsPng($event)" translate>utils.misc.image</li>
        </ul>
    </card-options>
    <div class="flex items-center justify-center" ng-if="$ctrl.isLoading">
        <div class="analytics-chart-placeholder">
            <div class="flex flex-column items-center justify-center h-100">
                <span class="w-100"></span>
                <img src="/assets/img/placeholders/charts/List.svg" ng-if="$ctrl.chart.chartType == 'list'"></img>
                <img src="/assets/img/placeholders/charts/Line.svg" ng-if="$ctrl.chart.chartType == 'line'"></img>
                <img src="/assets/img/placeholders/charts/Pie.svg" ng-if="$ctrl.chart.chartType == 'pie'"></img>
                <img src="/assets/img/placeholders/charts/Bar.svg" ng-if="$ctrl.chart.chartType == 'bar'"></img>
                <img src="/assets/img/placeholders/charts/Column.svg" ng-if="$ctrl.chart.chartType == 'column'"></img>
                <img src="/assets/img/placeholders/charts/Counter.svg" ng-if="$ctrl.chart.chartType == 'counter'"></img>
                <span class="w-100"></span>
            </div>
        </div>
    </div>
    <div class="ph4 pb4">
        <chart options="$ctrl.chartOptions"
            container-id="{{$ctrl.chartContainerId}}"
            data="$ctrl.chart.data"
            type="$ctrl.chart.chartType"
            on-chart-ready="$ctrl.onChartReady($elementData, $chartImg)"
            ng-if="!$ctrl.isLoading"
            ></chart>
    </div>
</card>
