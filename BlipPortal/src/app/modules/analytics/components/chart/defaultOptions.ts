import { COLORS_CHART, COLOR_CONTENT_DEFAULT, COLOR_SECONDARY, COLOR_SURFACE_1 } from 'modules/application/detail/analytics/Colors';
import { FontsStyle } from 'modules/application/detail/analytics/FontStyles';

export const colorsArray = Object.keys(COLORS_CHART.colors).map((key) => {
    return COLORS_CHART.colors[key];
});

export const defaultOptions = (arrayGraphRange, graphHeight) => ({
    colors: colorsArray,
    curveType: 'line',
    fontSize: 12,
    fontName: FontsStyle.style.CARBONA,
    lineWidth: 2.5,
    backgroundColor: COLOR_SURFACE_1,
    animation: {
        startup: true,
        duration: 1000,
        easing: 'out',
    },
    hAxis: { //eixo y
        viewWindow: {
            min: 0,
        },
        ticks: arrayGraphRange,
        format: '#',
    },
    vAxis: { // eixo x
        viewWindow: {
            min: 0,
        },
        ticks: arrayGraphRange,
        format: '#',
        gridlines: {
            color: COLOR_SURFACE_1,
        },
        baseline: {
            color: COLOR_SECONDARY,
        },
    },
    legend: {
        position: 'bottom',
        pointSize: '14',
        textStyle: {
            color: COLOR_CONTENT_DEFAULT,
            fontName: FontsStyle.style.CARBONA,
            fontSize: 14,
        },
    },
    pointShape: 'circle',
    pointSize: '0',
    height: graphHeight,
    chartArea: {  width: '85%'},
});

export const defaultTableOptions = {
    width: '100%',
    height: '100%',
    cssClassNames: {
        tableCell: 'tl',
        headerCell: 'th',
    },
};
