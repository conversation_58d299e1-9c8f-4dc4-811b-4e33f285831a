import * as angular from 'angular';
import 'angular-chart.js';
import * as colors from 'modules/application/detail/analytics/Colors';

import ChartComponent from './chart.component';
import { FontsStyle } from 'modules/application/detail/analytics/FontStyles';

export const chart = angular
    .module('chart', ['chart.js'])
    .component('chart', ChartComponent)
    .config((ChartJsProvider) => {
        'ngInject';

        ChartJsProvider.setOptions({
            responsive: true,
            maintainAspectRatio: false,
            animationSteps: 66,
            animationEasing: 'easeInOutQuad',
            backgroundColor: colors.COLOR_SURFACE_1,
            elements: {
                arc: {
                    borderColor: colors.COLOR_BRAND,
                    hoverBorderColor: colors.COLOR_BRAND,
                },
                point: {
                    radius: 2,
                    borderWidth: 2,
                },
            },
            tooltips: {
                bodyFontFamily: FontsStyle.style.CARBONA_BOLD,
                borderColor: colors.COLOR_CONTENT_DEFAULT,
                backgroundColor: colors.COLOR_CONTENT_DEFAULT,
                bodyFontSize: 24,
            },
        });

        ChartJsProvider.setOptions('doughnut', {
            cutoutPercentage: 81,
        });

        ChartJsProvider.setOptions('line', {
            tooltipTemplate: '<%= value %>',
            multiTooltipTemplate: '<%= value %>',
        });
    }).name;
