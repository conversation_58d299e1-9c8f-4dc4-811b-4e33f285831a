<div ng-if="$ctrl.type != 'counter'">
    <div ng-if="$ctrl.data.length > 0" class="chart-table-container" ng-class="{ 'bp-table-chart bp-table-chart--sort': $ctrl.isTableType }" id="{{$ctrl.containerId}}"></div>
    <div ng-if="$ctrl.data.length == 0" class="chart-no-data mv4 bp-c-time" translate>modules.application.detail.dashboard.general.noEnoughData</div>
</div>
<div ng-if="$ctrl.type == 'counter'" id="{{$ctrl.containerId}}" class="analytics-counter">
    {{ $ctrl.data }}
</div>
