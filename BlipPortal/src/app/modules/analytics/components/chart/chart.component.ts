import 'data/string';
import 'assets/scss/main.scss';
import * as uuid from 'uuid';
import template from './ChartView.html';
import { defaultOptions, defaultTableOptions } from './defaultOptions';
import { IRootScopeService } from 'angular';
import { immutableSplice, generateArrayOfNumbers } from 'data/array';
import { IChartComponentController } from 'modules/analytics/models/Chart';

/**
 *
 * IMPORTANT: needs to use require due typescript automatically type imports,
 * so its cause another issues with this specific module
 *
 */
const { GoogleCharts } = require('assets/js/google-charts'); //tslint:disable-line

export const ElementChangeEvent = 'ElementChangeEvent';

export const GetChartConstructor = (ChartType) => {
    const Constructor = GoogleCharts.api.visualization[`${ChartType}Chart`];

    if (Constructor == undefined) {
        throw new Error('Invalid chart type');
    }

    return Constructor;
};

const requiredPackages = ['corechart', 'table'];
class Chart implements IChartComponentController {
    onChartReady: (args: any) => void;
    type: any;
    data: Array<any>[];
    options: {};
    containerId: string;
    labels: any;
    loading: any;

    constructor(private $rootScope: IRootScopeService) {
        this.loading = this.loading || false;
        this.containerId = this.containerId || `chart-${uuid.v4()}`;
    }

    get isTableType() {
        return this.type == 'table' || this.type == 'list';
    }

    $onInit() {
        window.addEventListener('resize', this.onResizeWindow);
        this.subscribeEvents();
        this.initOrUpdateGC();
    }

    $onDestroy() {
        window.removeEventListener('resize', this.onResizeWindow);
    }

    /**
     * Chart change event method
     * @param {Object} - Object with properties that has changes
     */
    onChartChange({ containerId }) {
        if (this.containerId == containerId) {
            this.initOrUpdateGC();
        }
    }

    /**
     * On resize window function
     * IMPORTANT: needs to be an arrow function because of 'this' scope
     */
    onResizeWindow = () => {
        this.drawElement();
    }

    /**
     * Subscribe all $watch or $on events
     */
    subscribeEvents() {
        this.$rootScope.$on(ElementChangeEvent, ($e, containerId) => {
            this.onChartChange({ containerId });
        });
    }

    /**
     * Calls GoogleCharts load method with required packages
     */
    private initOrUpdateGC() {
        if (typeof this.data != 'object') {
            return;
        }
        GoogleCharts.load(this.drawElement.bind(this), requiredPackages);
    }

    /**
     * Get the maximum integer value in a DataTable object
     * @param data - DataTable object from GoogleCharts API
     */
    private getMaxValueInData( data: any ) {
        if (this.type == 'line') {
            const numberOfColums = data.getNumberOfColumns();
            const maxPerColumn = immutableSplice(generateArrayOfNumbers(numberOfColums).map((_, i) => data.getColumnRange(i).max), 0, 1);

            return maxPerColumn ? Math.max(...maxPerColumn) : 0;
        } else if (this.type != 'list') {
            const numberOfRows = data.getNumberOfRows();
            const maxPerRow = generateArrayOfNumbers(numberOfRows).map((i) => data.getValue(i, 1));

            return maxPerRow ? Math.max(...maxPerRow) : 0;
        } else {
            return 0;
        }
    }

    /**
     * Generate a array number of 6 integers (starting from 0)
     * where the given number is closest to the end of the array,
     * but within it's range. This method is necessary to avoid decimal
     * numbers in GoogleCharts graph axes.
     * @param n - Number that has to be within array range
     */
    private generateTicksArray( n: number ) {
        const arrayMaxF = x => x % 4 != 0 ? arrayMaxF(x + 1) : x;
        const arrayMax = arrayMaxF(n + 1);

        const ticksArray = [
            0,
            arrayMax / 4,
            arrayMax / 2,
            arrayMax * 3 / 4 ,
            arrayMax
        ];

       return ticksArray;
    }

    /**
     * Calculate the height that the pie and bar charts must have
     * depending on the number of series that the DataTable has.
     * Between 0 and 5 series it will have 250px;
     * Between 6 and 10 series it will have 300px;
     * More than 10 series it will have 500px;
     * @param data - DataTable object from GoogleCharts API
     */
    private calculateGraphHeight( data: any) {
        if (this.type != 'bar' && this.type != 'pie') {
            return 250;
        }
        const numberOfRows = data.getNumberOfRows();

        return (numberOfRows >= 0 && numberOfRows <= 5) ? 250
             : (numberOfRows > 5 && numberOfRows <= 10) ? 300
             : 500;
    }

    /**
     * Draw chart (or not) elements
     */
    private drawElement() {
        if (this.data && this.data.length == 0) {
            return;
        }

        if (!this.type) {
            throw new Error('Element type is not defined');
        }

        const elementData = GoogleCharts.api.visualization.arrayToDataTable(
            this.data,
        );

        const arrayGraphRange = this.generateTicksArray(this.getMaxValueInData(elementData));
        const graphHeight = this.calculateGraphHeight(elementData);

        let elementOptions;
        let ElementInstance;

        switch (this.type) {
            case 'table':
            case 'list':
                ElementInstance = GoogleCharts.api.visualization.Table;
                elementOptions = {
                    ...defaultTableOptions,
                    ...this.options,
                };
                break;
            case 'counter':
                break;
            default:
                ElementInstance = GetChartConstructor(this.type.capitalize());
                elementOptions = {
                    ...defaultOptions(arrayGraphRange, graphHeight),
                    ...this.options,
                };
                break;
        }

        const element = new ElementInstance(
            document.getElementById(this.containerId),
        );

        try {
            element.draw(elementData, elementOptions);

            GoogleCharts.api.visualization.events.addListener(element, 'ready', () => {
                // Element data table is used on AnalyticsChart
                this.handleOnChartReady({ $elementData: elementData, $chartImg: element.getImageURI() });
            });

            if (this.type == 'table' || this.type == 'list') {
                // Element data table is used on AnalyticsChart
                this.handleOnChartReady({ $elementData: elementData, $chartImg: undefined });
            }
        } catch (e) {
            throw new Error('Error while drawing chart: ' + e);
        }
    }

    handleOnChartReady({ $elementData, $chartImg }) {
        if (this.onChartReady) {
            this.onChartReady({ $elementData, $chartImg });
        }
    }
}

const ChartComponent = {
    template,
    controller: Chart,
    controllerAs: '$ctrl',
    bindings: {
        type: '<',
        data: '<',
        options: '<?',
        containerId: '@?',
        onChartReady: '&?',
    },
};

export default ChartComponent;
