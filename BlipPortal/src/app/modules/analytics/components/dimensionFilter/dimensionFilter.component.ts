import template from './DimensionFilterView.html';
import { DimensionFilter } from 'modules/analytics/models/dimensions';
import { EventEmitter } from 'modules/shared/EventEmitter';
import TranslateService from 'modules/translate/TranslateService';
import { UserProperties } from 'modules/application/detail/users/models/UserProperties';
import './dimensionFilter.scss';
import {
    ODataOperator,
    ODataFunction,
    ODataModifier,
} from 'types/OData';
import { IToggleable } from 'feature-toggle-client';
import { UserFeatures } from 'modules/application/detail/users/UserFeatures';

class DimensionFilterController implements IToggleable {
    styles: any;
    userProperties: { key: string, name: string }[];
    data: DimensionFilter;
    isReactOnDimensionFilterEnabled: boolean;
    private props: string[];
    private isUsingStartsWithOnContactsSearch: boolean;
    private isContactTaxDocumentEnabled: boolean = false;
    
    /**
     * Callback called when changes occur
     */
    onChanges: ($event) => void;
    comparisons: { value: ODataModifier; label: any; }[];

    constructor(private $translate, private TranslateService: TranslateService) {
        this.onInit();
    }

    async onInit() {
        await this.checkFeatures();
        this.setProperties();
        this.mapUserProperties();
        await this.loadComparisons();
    }

    setProperties() {
        this.props = this.isContactTaxDocumentEnabled
            ? UserProperties.getPropsWithDocument()
            : UserProperties.getProps();
    }

    mapUserProperties() {
        this.userProperties = this.props.map(key => ({
            key,
            name: this.TranslateService.instantTranslate(`utils.forms.${key}`)
        }));
    }

    async checkFeatures(): Promise<any> {
        this.isUsingStartsWithOnContactsSearch = await UserFeatures.isUsingStartsWithOnContactsSearch();
        this.isContactTaxDocumentEnabled = await UserFeatures.isContactTaxDocumentEnabled();
        this.isReactOnDimensionFilterEnabled = await UserFeatures.isReactOnContactsPageEnabled();
    }

    get isNotValidValue() {
        return this.data.property && !this.data.value;
    }

    get isNotValidProperty() {
        return !this.data.property && this.data.value;
    }

    async loadComparisons() {
        this.comparisons = this.isUsingStartsWithOnContactsSearch
            ? [
                  {
                      value: ODataFunction.StartsWith,
                      label: await this.$translate(
                          'utils.misc.odata.startsWith',
                      ),
                  },
              ]
            : [
                  {
                      value: ODataFunction.Contains,
                      label: await this.$translate('utils.misc.odata.contains'),
                  },
                  {
                      value: ODataFunction.NotContains,
                      label: await this.$translate(
                          'utils.misc.odata.notContain',
                      ),
                  },
              ];

        const propertyIsOnList =  this.props.indexOf(this.data.prop) !== -1;

        if (propertyIsOnList) {

            this.comparisons = this.comparisons.concat({
                value: ODataOperator.Equals,
                label: await this.$translate(
                    'utils.misc.odata.equals',
                ),
            });
            this.comparisons = this.comparisons.concat({
                value: ODataOperator.NotEquals,
                label: await this.$translate(
                    'utils.misc.odata.notEqual',
                ),
            });
        }
    }

    handleChanges(changes: DimensionFilter) {
        this.loadComparisons();
        if (this.onChanges) {
            this.onChanges(EventEmitter({ changes }));
        }
    }
}

export const DimensionFilterComponent = {
    template,
    controller: DimensionFilterController,
    controllerAs: '$ctrl',
    bindings: {
        data: '<',
        onChanges: '&?',
        conditionDisabled: '<?',
    },
};
