<div ng-if="!$ctrl.isReactOnDimensionFilterEnabled" class="w-100 flex flex-column justify-between">
    <div class="w-100 flex flex-row">
        <autocomplete items="$ctrl.userProperties"
        search-for="name"
        show-text="name"
        search-length="0"
        max-length="150"
        show-not-found-text="false"
        initial-value="$ctrl.data.property"
        ng-model="$ctrl.data.property"
        ng-change="$ctrl.handleChanges($ctrl.data)"
        label="{{'utils.misc.property' | translate}}"
        class="w-50 mr3 autocomplete-theme-1"></autocomplete>

        <custom-select label="{{'modules.analytics.dimensionFilter.condition' | translate}}"
        class="w-50 custom-select-theme-1"
        no-wrap="true"
        ng-model="$ctrl.data.condition"
        ng-change="$ctrl.handleChanges($ctrl.data)"
        disabled="$ctrl.conditionDisabled">
            <select-item ng-repeat="comparison in $ctrl.comparisons" value="{{comparison.value}}" label="{{comparison.label}}"></select-item>
        </custom-select>
    </div>

    <div class="mt3 labeled-input" ng-class="{'labeled-input--error': $ctrl.isNotValidValue}">
        <label translate>modules.analytics.dimensionFilter.value</label>
        <input ng-model="$ctrl.data.value" ng-change="$ctrl.handleChanges($ctrl.data)" type="text">
    </div>
</div>
<dimension-filter-react ng-if="$ctrl.isReactOnDimensionFilterEnabled" 
    filter="$ctrl.data"
    condition-disabled="$ctrl.conditionDisabled",
    on-changes="$ctrl.onChanges">
</dimension-filter-react>