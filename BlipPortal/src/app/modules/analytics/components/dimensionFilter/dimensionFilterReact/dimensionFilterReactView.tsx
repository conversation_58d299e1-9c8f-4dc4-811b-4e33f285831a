import { ODataModifier } from "types/OData";
import DimensionFilterReactTranslations from "./dimensionFilterReactTranslations";
import { useEffect, useState } from "react";
import { BdsAutocomplete, BdsInput, BdsLoadingSpinner, BdsSelect, BdsSelectOption } from 'blip-ds/dist/blip-ds-react/components';

interface DimensionFilterReactViewProps {
    translations: DimensionFilterReactTranslations;
    userProperties: { key: string, name: string }[];
    comparisons: { value: ODataModifier; label: any; }[];
    conditionDisabled: boolean;
    handleOnSelectedProperty: (propertyKey: string) => void;
    handleFilterChange: (propertyKey: string, comparison: string, value: string) => void;
    isNotValidValue: (selectedProperty: string, selectedValue: string) => boolean;
    isLoadingData: boolean;
};

const DimensionFilterReactView = ({
    translations,
    userProperties,
    comparisons,
    conditionDisabled,
    handleOnSelectedProperty,
    handleFilterChange,
    isNotValidValue,
    isLoadingData
}: DimensionFilterReactViewProps) => {    

    const [selectedProperty, setSelectedProperty] = useState<string | null>();
    const [selectedComparison, setSelectedComparison] = useState<ODataModifier | null>();
    const [selectedValue, setSelectedValue] = useState<string | null>();

    useEffect(() => {        
        handleFilterChange(selectedProperty, selectedComparison, selectedValue);
    }, [selectedProperty, selectedComparison, selectedValue]);

    return isLoadingData ?
    (
        <BdsLoadingSpinner size="small"></BdsLoadingSpinner>
    )
    :
    (
        <div className="w-100 flex flex-column justify-between">
            <div className="w-100 flex flex-row">
                <BdsAutocomplete
                    label={translations.property}
                    options-position="bottom"
                    className="w-50 mr3 autocomplete-theme-1"
                    onBdsChange={(e) => {handleOnSelectedProperty(e.target.value); setSelectedProperty(e.target.value);}}>
                    {userProperties.map((property, index) => (
                        <BdsSelectOption key={index} value={property.key}>{property.name}</BdsSelectOption>
                    ))}
                </BdsAutocomplete>
                <BdsSelect
                    id="comparison-select-id"
                    value={selectedComparison}
                    label={translations.condition}
                    className="w-50 custom-select-theme-1"
                    disabled={conditionDisabled}
                    onBdsChange={(e) => {setSelectedComparison(e.target.value);}}>
                    {comparisons.map((comparison, index) => (
                        <BdsSelectOption key={index} value={comparison.value}>{comparison.label}</BdsSelectOption>
                    ))}
                </BdsSelect>           
            </div>
            <BdsInput
                className="mt3"
                label={translations.value}
                danger={isNotValidValue(selectedProperty, selectedValue)}
                onBdsChange={(e) => setSelectedValue((e.target as any).value)}>                   
            </BdsInput>
        </div>
    );
}

export default DimensionFilterReactView;