import { DimensionFilter } from "modules/analytics/models/dimensions";
import DimensionFilterReactView from "./dimensionFilterReactView";
import { ODataFunction, ODataModifier, ODataOperator } from "types/OData";
import { EventEmitter } from "modules/shared/EventEmitter";
import { UserProperties } from "modules/application/detail/users/models/UserProperties";
import TranslateService from "modules/translate/TranslateService";
import DimensionFilterReactTranslations from "./dimensionFilterReactTranslations";
import { useEffect, useState } from "react";
import { UserFeatures } from "modules/application/detail/users/UserFeatures";
import React from "react";

interface DimensionFilterReactProps {
    filter: DimensionFilter;
    translateService: TranslateService;
    conditionDisabled: boolean;
    onChanges: (event: any) => void;
};
  
const DimensionFilterReact = ({
  filter,
  translateService,
  conditionDisabled,
  onChanges,
}: DimensionFilterReactProps) => {

  const [comparisons, setComparisons] = useState<{ value: ODataModifier; label: any; }[]>([
    {
        value: ODataFunction.StartsWith,
        label: translateService.instantTranslate('utils.misc.odata.startsWith'),
    },
  ]);
  
  const [isUsingStartsWithOnContactsSearch, setIsUsingStartsWithOnContactsSearch] = useState<boolean | null | undefined>(null);
  const [isContactTaxDocumentEnabled, setIsContactTaxDocumentEnabled] = useState<boolean | null | undefined>(null);

  useEffect(() => {
    checkFeatures();
  }, []);

  const checkFeatures = async (): Promise<any> => {
    setIsUsingStartsWithOnContactsSearch(await UserFeatures.isUsingStartsWithOnContactsSearch());
    setIsContactTaxDocumentEnabled(await UserFeatures.isContactTaxDocumentEnabled());
  }

  const getProperties = (): string[] => {
    return isContactTaxDocumentEnabled
      ? UserProperties.getPropsWithDocument()
      : UserProperties.getProps();
  }

  const mapUserProperties = () : { key: string, name: string }[] => {
    return (getProperties().map(key => ({
        key,
        name: translateService.instantTranslate(`utils.forms.${key}`)
    })));
  }

  const loadTranslations = (): DimensionFilterReactTranslations => {
    let translations = new DimensionFilterReactTranslations();
    translations.property = translateService.instantTranslate('utils.misc.property');
    translations.condition = translateService.instantTranslate('modules.analytics.dimensionFilter.condition');
    translations.value = translateService.instantTranslate('modules.analytics.dimensionFilter.value');

    return translations;
  };

  const loadComparisons = (selectedPropertyKey: string) => {
    let comparisons: { value: ODataModifier; label: any; }[] =
      isUsingStartsWithOnContactsSearch ?
      [
        {
            value: ODataFunction.StartsWith,
            label: translateService.instantTranslate('utils.misc.odata.startsWith'),
        },
      ]
      :
      [
        {
            value: ODataFunction.Contains,
            label: translateService.instantTranslate('utils.misc.odata.contains'),
        },
        {
            value: ODataFunction.NotContains,
            label: translateService.instantTranslate('utils.misc.odata.notContain'),
        },
      ];

    const propertyIsOnList =  getProperties().indexOf(selectedPropertyKey) !== -1;

    if (propertyIsOnList) {

      comparisons = comparisons.concat({
            value: ODataOperator.Equals,
            label: translateService.instantTranslate('utils.misc.odata.equals'),
        });
      comparisons = comparisons.concat({
            value: ODataOperator.NotEquals,
            label: translateService.instantTranslate('utils.misc.odata.notEqual'),
        });
    }

    return comparisons;
  };

  const handleOnSelectedProperty = (propertyKey: string) => {
    setComparisons(loadComparisons(propertyKey));
  };

  const handleFilterChange = (propertyKey: string, comparison: ODataModifier, value: string) => {   
    filter.condition = comparison || null;
    filter.value = value || null;
    
    filter.property = propertyKey 
      ? { key: propertyKey, name: translateService.instantTranslate(`utils.forms.${propertyKey}`) }
      : null;
  
    onChanges(EventEmitter({ changes: filter }));
  };

  const isNotValidValue = (selectedProperty: string, selectedValue: string): boolean => {
    return selectedProperty && !selectedValue;
  }

  const isLoadingData = (): boolean => {
    return (isUsingStartsWithOnContactsSearch === null ||
      isUsingStartsWithOnContactsSearch === undefined ||
      isContactTaxDocumentEnabled === null ||
      isContactTaxDocumentEnabled === undefined);
  };

  return (
    <DimensionFilterReactView
      translations={ loadTranslations() }
      userProperties={ mapUserProperties() }
      comparisons={ comparisons }
      conditionDisabled={ conditionDisabled }
      handleOnSelectedProperty={ handleOnSelectedProperty }
      handleFilterChange={ handleFilterChange }
      isNotValidValue={ isNotValidValue }
      isLoadingData={ isLoadingData() }
    />
  );
}

export default DimensionFilterReact;