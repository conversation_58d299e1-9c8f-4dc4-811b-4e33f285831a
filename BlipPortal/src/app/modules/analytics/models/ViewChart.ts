import {
    UsersCategoryType,
    MessagesCategoryType,
} from 'modules/analytics/models';
import { ChartType } from './Chart';

export class ViewChart {
    dimension?: string;
    id?: string;
    isNew?: boolean;
    category?: MessagesCategoryType | UsersCategoryType;
    chartType: ChartType | string;
    type?: any;
    name?: string;
    data?: any; // Chart Matrix
    isLoading?: boolean = true;
    order?: number;

    constructor(init?: Partial<ViewChart>) {
        Object.assign(this, init);
    }
}
