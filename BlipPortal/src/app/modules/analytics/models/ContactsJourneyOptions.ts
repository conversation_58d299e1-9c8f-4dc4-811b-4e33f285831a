export interface ContactsJourneyOptions {
    tooltip: { isHtml: boolean, trigger: string };
    legend: {position: string};
    width: number;
    height: number;
    sankey: {
        node: {
            colors: string[],
            colorMode: string,
            label: {
                fontName: string,
                fontSize: number,
                color: string,
                bold: boolean,
                position: string
            },
            interactivity: boolean,
            nodePadding: number,
            width: number
        },
        iterations: number,
        link: {
            color: {
                fill: string,
                fillOpacity: number,
                stroke: string,
                strokeWidth: number,
            }
        }
    };
}
