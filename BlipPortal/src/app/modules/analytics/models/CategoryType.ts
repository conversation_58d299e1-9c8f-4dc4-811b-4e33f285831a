export enum MessagesCategoryType {
    SentMessages = 'sentMessages',
    ReceivedMessages = 'receivedMessages',
    TotalMessages = 'totalMessages',
    ActiveMessages = 'activeMessages',
    ActiveMessagesPerDomain = 'activeMessagesPerDomain'
}

export enum UsersCategoryType {
    UniqueUsers = 'uniqueUsers',
    ActiveUsers = 'activeUsers',
    EngagedUsers = 'engagedUsers',
    AverageInteractionUsers = 'averageInteractionUsers',
    TotalUsers = 'totalUsers',
}
