/**
 * Chart Type
 */

export enum ChartType {
    List = 'list',
    Table = 'table',
    Line = 'line',
    Bar = 'bar',
    Column = 'column',
    Pie = 'pie',
    Counter = 'counter'
}

/**
 * Chart component type
 */

interface IChartComponentBindings {
    type: ChartType;
    data: Array<any>[];
    options: {};
    containerId: string;
}

export interface IChartComponentController extends IChartComponentBindings {
    $onInit: () => void;
    $onDestroy: () => void;
    onChartChange: (props: any) => void;
    onResizeWindow: () => void;
    subscribeEvents: () => void;
}
