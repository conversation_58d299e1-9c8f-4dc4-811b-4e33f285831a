export enum GroupingType {
    Total = 'total',
    Action = 'action',
    EventExtra = 'eventExtra',
    UserExtra = 'userExtra',
    UserProperty = 'userProperty',
}

export class Grouping {
    /**
     * Grouping type
     */
    type: GroupingType;

    /**
     * Property to be grouped
     */
    property: string;

    constructor(init?: Partial<Grouping>) {
        Object.assign(this, init);
    }
}
