
import buildQuery from 'odata-query';

//
import { UserDimension } from './users/UserDimension';
import { EventDimension } from './events/EventDimension';
import { Grouping, GroupingType } from './Grouping';
import { FilterableDimension } from './FilterableDimension';
import { ChartType } from '../Chart';
import { ViewChart, ServiceType } from '..';
import { switchcase } from 'data/function';
import * as uuid from 'uuid';

/**
 *
 * Class to handle metric and command requests.
 * All properties started with '$' means 'view properties', not used to make command calls on MessagingHub
 *
 */
export class Metric {
    /**
     * Metric id (used on Bucket)
     */
    $id: string;

    /**
     * Show metric by unique users or total of events
     */
    $showBy: 'events' | 'users/unique';

    /**
     * Metric name
     */
    $name: string;

    /**
     * Chart type to be displayed. Required.
     */
    $chartType: ChartType;

    /**
     * Indicates if metric is still loading
     */
    $isLoading?: boolean;

    /**
     * Used to pass data to Chart library (current GoogleCharts)
     */
    $chartData?: any;

    /**
     * Users dimension filters
     */
    users?: UserDimension;

    /**
     * Events dimension filters
     */
    events?: EventDimension[] = [];

    /**
     * Grouping mode
     */
    grouping?: Grouping;

    constructor(init?: Partial<Metric>) {
        // Defaults
        this.grouping = new Grouping({
            type: GroupingType.Total,
        });
        this.$chartType = ChartType.Line;
        this.$isLoading = true;
        this.$showBy = 'events';
        this.$id = uuid.v4(),

        Object.assign(this, init);
    }

    addUserDimension() {
        if (!this.users) {
            this.users = new UserDimension();
        }
    }

    addEventDimension() {
        if (this.events.length == 0) { //Temporaly supports only one eventDimension
            this.events = this.events.concat(new EventDimension());
        }
    }

    removeDimension(dimension: FilterableDimension) {
        switch (dimension.constructor) {
            case UserDimension:
                this.users = undefined;
                break;
            case EventDimension:
                this.events = [];
                break;
        }
    }

    toOdataFilter({ beginDate, endDate }) {
        const usersDimension = 'user';
        const eventsDimension = 'events';
        let usersFilter;
        let eventsFilter;

        if (this.users) {
            usersFilter = {
                [`${eventsDimension}.${usersDimension}.type`]: this.users.type,
                ...this.users.filters.reduce((props, f) => ({
                    ...props,
                    [`${eventsDimension}.${usersDimension}.extras.${f.prop}`]: { [f.condition]: f.value },
                }), {}),
            };
        }

        if (this.events) {
            eventsFilter = {
                ...this.events.reduce((props, e) => ({
                    ...props,
                    [`${eventsDimension}.category`]: e.category,
                    [e.action ? `${eventsDimension}.action` : '']: e.action,
                    ...e.filters.reduce((filters, f) => ({
                        ...filters,
                        [`${eventsDimension}.extras.${f.prop}`]: { [f.condition]: f.value },
                    }), {}),
                }), {}),
            };
        }

        return buildQuery({
            filter: {
                beginDate,
                endDate,
                ...usersFilter,
                ...eventsFilter,
            },
        });
    }

    static convertChartToMetric(chart: ViewChart): Metric {
        const dimensionType = switchcase({
            'events': {
                events: [new EventDimension({
                    category: chart.category,
                })],
            },
        })({
            events: [new EventDimension({
                category: chart.dimension,
            })],
        })(chart.dimension);

        return new Metric({
            $id: chart.id,
            $name: chart.name,
            $chartType: <ChartType>chart.chartType,
            $chartData: chart.data,
            ...dimensionType,
        });
    }
}
