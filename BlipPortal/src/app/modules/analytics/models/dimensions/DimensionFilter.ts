import { ODataOperator, ODataModifier } from 'types/OData';
import * as uuid from 'uuid';

export class DimensionFilter {
    /**
     * Property to be filtered
     */

    id: string;

    property: {
        key: string,
        name: string
    };

    /**
     * Condition
     */
    condition: ODataModifier;

    /**
     * property value to be compared
     */
    value: any;

    constructor(init?: Partial<DimensionFilter>) {
        Object.assign(this, {
            id: uuid.v4(),
            condition: ODataOperator.Equals,
            property: { key: '', name: '' },
            ...init,
        });
    }

    get prop(): string {
        if (!this.property || (!this.property.key && !this.property.name))  {
            return '';
        }
        return this.property.key || this.property.name;
    }
}
