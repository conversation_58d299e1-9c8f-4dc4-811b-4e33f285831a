import { DimensionFilter } from '../DimensionFilter';
import { immutableSplice } from 'data/array';
import { FilterableDimension } from 'modules/analytics/models/dimensions/FilterableDimension';

export enum UserDimensionCategory {
    All = 'all',
    Active = 'active',
    Recurrents = 'recurrents',
}

export class UserDimension extends FilterableDimension {
    /**
     * Dimension filter type
     */
    type: UserDimensionCategory;

    constructor(init?: Partial<UserDimension>) {
        super();
        Object.assign(this, {
            type: UserDimensionCategory.All,
            ...init
        });
    }
}
