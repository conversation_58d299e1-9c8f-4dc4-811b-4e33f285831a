import { DimensionFilter } from 'modules/analytics/models/dimensions';
import { immutableSplice } from 'data/array';

export class FilterableDimension {
    /**
     * List of 'generic' dimension filter
     */
    filters: DimensionFilter[] = [];

    addFilter({ init } = { init: undefined }) {
        this.filters = this.filters.concat(new DimensionFilter(init));
    }

    popFilter({ index }) {
        if (index === undefined) {
            index = this.filters.length - 1;
        }

        this.filters = immutableSplice(this.filters, index, 1);
    }
}
