export interface ContactsJourneyHeader {
    type: EdgeTypes;
    step: number;
}

export class ContactsJourneyEdge {
    from: string;
    to: string;
    fromStateId: string;
    toStateId: string;
    type: EdgeTypes;
    count: number;
    step: number;
    order: number;
    name: string;
}

export enum EdgeTypes {
    Regular = 'regular',
    Other = 'other',
    End = 'exit'
}

export enum EdgeOrder {
    Top = 1,
    Middle = 2,
    Bottom = 3
}
