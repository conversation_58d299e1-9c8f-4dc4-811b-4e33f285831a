import { IAnalyticsService } from './IAnalyticsService';
import { AnalyticsFeatures } from 'modules/application/detail/analytics/AnalyticsFeatures';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';

const DEFAULT_DATA_SOURCE = 'db';
const LAKE_DATA_SOURCE = 'lake';
const GET_ACTIONS_FOR_CATEGORIES_TAKE_SIZE = 5000;
const PENDING_STATUS_CODE = 7390;
const MAX_RETRY_COUNT = 10;

export class EventTrackAnalyticsService implements IAnalyticsService {

    constructor(
        private MessagingHubService: MessagingHubService,
        private DEFAULT_PAGE_SIZE
    ) {}

    public async getCategories(
            filter: any = undefined,
            pagination = { take: this.DEFAULT_PAGE_SIZE}) {
        
        const originDataSource = await this.getDataSourceName();
        const { take } = pagination;

        const queryUri = filter
            ? `/event-track?categoryFilter=${filter}&dataSource=${originDataSource}&$take=${take}`
            : `/event-track?dataSource=${originDataSource}&$take=${take}`;

        const { items } = await this.getData(
            {
                method: 'get',
                uri: queryUri,
            }
        );

        return items.map((item) => ({
            key: item.category,
            value: item.category,
        }));
    }

    public async getActionsForCategory({
        category,
        endDate,
        startDate
    }) {
        const originDataSource = await this.getDataSourceName();
        let canLoadMoreData = true;
        let actionsForCategory = [];
        let skipData = 0;

        while (canLoadMoreData) {
            const { items } = await this.getData(
                {
                    method: 'get',
                    uri: `/event-track/${encodeURIComponent(
                        category,
                    )}?startDate=${encodeURIComponent(
                        startDate,
                    )}&endDate=${encodeURIComponent(endDate)}&dataSource=${originDataSource}&$take=${GET_ACTIONS_FOR_CATEGORIES_TAKE_SIZE}&$skip=${skipData}`,
                }
            );

            canLoadMoreData = items.length == GET_ACTIONS_FOR_CATEGORIES_TAKE_SIZE;
            actionsForCategory = actionsForCategory.concat(items);
            skipData += GET_ACTIONS_FOR_CATEGORIES_TAKE_SIZE;
        }
        return actionsForCategory;
    }

    public async getEventTrackCategoryCount({
        category,
        endDate,
        startDate,
    }) {
        const { count } = await this.getData(
            {
                method: 'get',
                uri: `/event-track-count/${encodeURIComponent(
                    category,
                )}?startDate=${encodeURIComponent(
                    startDate,
                )}&endDate=${encodeURIComponent(endDate)}`,
            }
        );
        return count;
    }

    async getData(commandBody: Lime.Command, retryCount = 0) {
        try {
            return await this.MessagingHubService.sendCommand(commandBody);
        } catch (error) {
            if (error.code === PENDING_STATUS_CODE && retryCount < MAX_RETRY_COUNT) {
                retryCount++;
                return new Promise((resolve) => {
                    setTimeout(async () => resolve(await this.getData(commandBody, retryCount)) , 1000);
                });
            }
            console.error(error);
        }
    }

    private async getDataSourceName(): Promise<String> {
        const enableLakeDatasource = await AnalyticsFeatures.enableLakeDatasource();
        if (enableLakeDatasource) {
            return LAKE_DATA_SOURCE;
        }

        return DEFAULT_DATA_SOURCE;
    }
}
