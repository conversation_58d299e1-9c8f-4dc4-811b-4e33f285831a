import { ElementChangeEvent } from 'modules/analytics/components/chart/chart.component';
export class ChartService {

    /**
     * Update chart based on containerId
     * @param  {IScope | IRootScopeService} $context - Scope that will broadcast event
     * @param args - Properties that will be updated
     */
    updateChartProps($context, { containerId }) {
        $context.$broadcast(ElementChangeEvent, containerId);
    }
}
