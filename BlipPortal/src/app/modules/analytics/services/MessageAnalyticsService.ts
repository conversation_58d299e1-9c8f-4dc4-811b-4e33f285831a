import { IAnalyticsService } from './IAnalyticsService';

import * as dimensions from '../dimensions.json';
import { MessagesCategoryType } from '../models';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { MetricsService } from 'modules/metrics/MetricsService';
import { StatisticsService2 } from 'modules/statistics/StatisticsService2';
import { MetricsFeatures } from 'modules/metrics/MetricsFeatures';

type ActionsPayload = {
    category: MessagesCategoryType,
    [name: string]: any
};

export class MessageAnalyticsService implements IAnalyticsService {
    constructor(
        private MetricsService: MetricsService,
        private StatisticsService2: StatisticsService2,
        private $translate: any,
        private MessagingHubService: MessagingHubService) {
    }

    public getCategories(): any {
        const categoryKeys = Object.keys(dimensions.messages);

        const categories = [];

        categoryKeys.forEach((c) => {
            const value = this.$translate.instant(
                `reports.categories.${c}`,
            );
            categories.push({ key: c, value: value });
        });

        return categories;
    }

    public async getActionsForCategory({
        category,
        endDate,
        startDate,
        application,
    }: ActionsPayload) {
        let result;

        switch (category) {
            case MessagesCategoryType.SentMessages:
                result = await this.getActionsSentMessages({
                    shortName: application.shortName,
                    startDate,
                    endDate,
                });
                break;

            case MessagesCategoryType.ReceivedMessages:
                result = await this.getActionsReceivedMessages({
                    shortName: application.shortName,
                    startDate,
                    endDate,
                });
                break;

            case MessagesCategoryType.TotalMessages:
                result = await this.getActionsTotalMessages({
                    shortName: application.shortName,
                    startDate,
                    endDate,
                });
                break;

            case MessagesCategoryType.ActiveMessages:
                result = await this.getActiveMessages({
                    startDate,
                    endDate,
                    application
                });
                break;

            case MessagesCategoryType.ActiveMessagesPerDomain:
                result = await this.getActiveMessagesPerDomain({
                    startDate,
                    endDate,
                    application
                });
                break;
        }

        return result.map((element) => ({
            category,
            action: category,
            ...element,
        }));
    }

    private async getActionsSentMessages(payload) {
        const { startDate, endDate } = payload;
        
        const enableFT = await MetricsFeatures.isEnableMetricsToStatistics();

        if (enableFT) {
            const { items } = await this.MetricsService.getSentMessagesStatistics(
                this.StatisticsService2.INTERVAL_DAILY,
                startDate,
                endDate
            );

            return this.returnItems(items);

        } else {
            const { items } = await this.StatisticsService2.getMessagesStatistics(
                this.MetricsService.INTERVAL_DAILY,
                this.StatisticsService2.AGGREGATION_TYPE_SENT_BY,
                startDate,
                endDate
            );

            return this.returnItems(items);
        }
    }

    private returnItems(items) {
        return items.map((day) => ({
            ...day,
            count: day.count,
            storageDate: new Date(day.intervalStart).toJSON(),
        }));
    }

    private async getActionsReceivedMessages(payload) {
        const { startDate, endDate } = payload;

        const enableFT = await MetricsFeatures.isEnableMetricsToStatistics();

        if (enableFT) {
            const { items } = await this.MetricsService.getReceivedMessagesStatistics(
                this.MetricsService.INTERVAL_DAILY,
                startDate,
                endDate
            );

            return this.returnItems(items);

        } else {
            const { items } = await this.StatisticsService2.getMessagesStatistics(
                this.StatisticsService2.INTERVAL_DAILY,
                this.StatisticsService2.AGGREGATION_TYPE_RECEIVED_BY,
                startDate,
                endDate
            );

            return this.returnItems(items);
        }
    }

    private async getActionsTotalMessages(payload) {
        const [received, sent] = await Promise.all([
            this.getActionsReceivedMessages(payload),
            this.getActionsSentMessages(payload),
        ]);

        const receivedEvents = received.map((day) => ({
            action: 'receivedMessages',
            count: day.count,
            storageDate: day.storageDate,
        }));
        const sentEvents = sent.map((day) => ({
            action: 'sentMessages',
            count: day.count,
            storageDate: day.storageDate,
        }));

        return receivedEvents.concat(sentEvents);
    }

    private async getActiveMessages(payload) {
        const { startDate, endDate } = payload;

        const { items } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/active-messages/NI?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`,
            }
        );

        return [
            {
                count: items[0].count,
                action: MessagesCategoryType.ActiveMessages,
            },
        ];
    }

    private async getActiveMessagesPerDomain(payload) {
        const { startDate, endDate } = payload;

        const { resource: { items } } = await this.MessagingHubService.processCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/active-messages-per-domain/NI?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`,
            }
        );

        return [
            {
                action: MessagesCategoryType.ActiveMessagesPerDomain,
                itemsPerDomain: items
            },
        ];
    }
}
