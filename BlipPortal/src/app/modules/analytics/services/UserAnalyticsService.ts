import { IAnalyticsService } from './IAnalyticsService';

import * as dimensions from '../dimensions.json';
import * as moment from 'moment';
import { UsersCategoryType } from '../models';
import MessagingHubService from 'modules/messaginghub/MessagingHubService';
import { StatisticsService2 } from 'modules/statistics/StatisticsService2';
import { getChartTranslation } from '../Utils';
import { MetricsService } from 'modules/metrics/MetricsService';
import { MetricsFeatures } from 'modules/metrics/MetricsFeatures';

const GET_ACTIVE_USERS_BY_DAY_TIMEOUT = 90000;

type ActionsPayload = {
    category: UsersCategoryType;
    [name: string]: any;
};

export class UserAnalyticsService implements IAnalyticsService {
    constructor(
        private StatisticsService2: StatisticsService2,
        private MetricsService: MetricsService,
        private $translate: any,
        private MessagingHubService: MessagingHubService,
    ) { }

    public getCategories(chartType: string) {
        const dimension = dimensions.usersMau;
        const categoryKeys = Object.keys(dimension);
        const categories = [];

        categoryKeys.forEach((category) => {
            const translation = getChartTranslation(this.$translate, category, chartType, 'users');
            const value = this.$translate.instant(translation);

            categories.push({ key: category, value: value });
        });

        return categories;
    }

    public async getActionsForCategory({
        chartType,
        category,
        startDate,
        endDate,
        application,
    }: ActionsPayload) {
        //TODO: refactor to single chart component calls
        let result;

        switch (category) {
            case UsersCategoryType.UniqueUsers:
                result =
                    chartType == 'line'
                        ? await this.getActionsUniqueUsersByDay({
                            shortName: application.shortName,
                            startDate,
                            endDate,
                        }) //Get active users by day even they are the same
                        : await this.getActionsUniqueUsers({
                            shortName: application.shortName,
                            startDate,
                            endDate,
                        }); //Get unique active users
                break;

            case UsersCategoryType.ActiveUsers:
                result =
                    chartType == 'line'
                        ? await this.getActiveUsersByDay({
                            startDate,
                            endDate,
                            application,
                        }) //Get unique active users by day
                        : await this.getActiveUsers({
                            startDate,
                            endDate,
                            application,
                        }); //Get unique active users
                break;

            case UsersCategoryType.EngagedUsers:
                result =
                    chartType == 'line'
                        ? await this.getEngagedUsersByDay({
                            startDate,
                            endDate,
                            application,
                        }) //Get unique engaged users by day
                        : await this.getEngagedUsers({
                            startDate,
                            endDate,
                            application,
                        }); //Get unique engaged users
                break;

            case UsersCategoryType.AverageInteractionUsers:
                result = await this.getActionsAverageInteractionByDay({
                    shortName: application.shortName,
                    startDate,
                    endDate,
                });
                break;
            case UsersCategoryType.TotalUsers:
                result = await this.getActionsTotalUsers({
                    shortName: application.shortName,
                    startDate,
                    endDate,
                });
                break;
        }

        return result.map((element) => ({
            category: category,
            action: category,
            ...element,
        }));
    }

    public async getTotalActiveUsers({ startDate, endDate }) {
        const { count } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/active-identity-quantity?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`,
            }, 5 * 60 * 1000);

        return count;
    }

    public async getTotalEngagedUsers({ startDate, endDate }) {
        const { count } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/engaged-identity-quantity?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`,
            }, 5 * 60 * 1000);

        return count;
    }

    private async getActionUsersData(payload) {

        const { startDate, endDate, interval } = payload;

        const enableFT = await MetricsFeatures.isEnableMetricsToStatistics();

        const service = enableFT ? this.MetricsService : this.StatisticsService2;

        const { items } = await service.getUserStatistics(
            interval,
            startDate,
            endDate
        );

        return items;
    }

    private async getActionsUniqueUsers(payload) {
        const data = await this.getActionUsersData({ interval: this.MetricsService.INTERVAL_NO_INERVAL, ...payload });

        return data.map((data) => ({
            count: data.count,
            action: UsersCategoryType.UniqueUsers,
        }));
    }

    private async getActionsUniqueUsersByDay(payload) {
        const data = await this.getActionUsersData({ interval: this.MetricsService.INTERVAL_DAILY, ...payload });

        return data.map((day) => ({
            ...day,
            count: day.count,
            storageDate: moment(day.intervalStart).toJSON(),
        }));
    }

    private async getActionsAverageInteractionByDay(payload) {
        //get user by day
        const usersByDay = await this.getActionUsersData({
            interval: this.MetricsService.INTERVAL_DAILY,
            ...payload
        });

        //get received messages by day
        const { startDate, endDate } = payload;
        const enableFT = await MetricsFeatures.isEnableMetricsToStatistics();

        if (enableFT) {
            const { items: messagesByDay } = await this.MetricsService.getReceivedMessagesStatistics(
                this.MetricsService.INTERVAL_DAILY,
                startDate,
                endDate
            );

            return this.averageMessagePerUser(messagesByDay, usersByDay);
        } else {
            const { items: messagesByDay } = await this.StatisticsService2.getMessagesStatistics(
                this.StatisticsService2.INTERVAL_DAILY,
                this.StatisticsService2.AGGREGATION_TYPE_RECEIVED_BY,
                startDate,
                endDate
            );

            return this.averageMessagePerUser(messagesByDay, usersByDay);
        }

    }

    private averageMessagePerUser(messagesByDay, usersByDay) {
        const averageMessagesPerUser = messagesByDay.map((m, i) => {
            if (m.count > 0) {
                return usersByDay[i].count == 0
                    ? m
                    : {
                        ...m,
                        count: Math.round(m.count / usersByDay[i].count),
                    };
            } else {
                return m;
            }
        });

        return averageMessagesPerUser.map((am) => ({
            count: am.count,
            storageDate: moment(am.intervalStart).toJSON(),
        }));
    }

    private async getActiveUsers(payload) {
        const { startDate, endDate } = payload;

        const { items } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/active-identity/NI?startDate=${encodeURIComponent(
                    startDate,
                )}&endDate=${encodeURIComponent(endDate)}`,
            },
        );

        return [
            {
                count: items[0].count,
                action: UsersCategoryType.ActiveUsers,
            },
        ];
    }

    private async getActiveUsersByDay(payload) {
        const { startDate, endDate } = payload;

        const { items } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/active-identity/D?startDate=${encodeURIComponent(
                    startDate,
                )}&endDate=${encodeURIComponent(endDate)}`,
            },
            GET_ACTIVE_USERS_BY_DAY_TIMEOUT // This command can take more than the default timeout to return a large number of active users
        );

        return items.map((day) => ({
            ...day,
            count: day.count,
            storageDate: moment(day.intervalStart).toJSON(),
        }));
    }

    private async getEngagedUsers(payload) {
        const { startDate, endDate } = payload;

        const { items } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/engaged-identity/NI?startDate=${encodeURIComponent(
                    startDate,
                )}&endDate=${encodeURIComponent(endDate)}`,
            },
            GET_ACTIVE_USERS_BY_DAY_TIMEOUT // This command can take more than the default timeout to return a large number of active users
        );

        return [
            {
                count: items[0].count,
                action: UsersCategoryType.EngagedUsers,
            },
        ];
    }

    private async getEngagedUsersByDay(payload) {
        const { startDate, endDate } = payload;

        const { items } = await this.MessagingHubService.sendCommand(
            {
                method: 'get',
                to: '<EMAIL>',
                uri: `/metrics/engaged-identity/D?startDate=${encodeURIComponent(
                    startDate,
                )}&endDate=${encodeURIComponent(endDate)}`,
            },
        );

        return items.map((day) => ({
            ...day,
            count: day.count,
            storageDate: moment(day.intervalStart).toJSON(),
        }));
    }

    private async getActionsTotalUsers(payload) {
        const [active, engaged] = await Promise.all([
            this.getActiveUsersByDay(payload),
            this.getEngagedUsersByDay(payload),
        ]);

        const activeUserEvents = active.map((day) => ({
            action: UsersCategoryType.ActiveUsers,
            count: day.count,
            storageDate: day.storageDate,
        }));
        const engagedUserEvents = engaged.map((day) => ({
            action: UsersCategoryType.EngagedUsers,
            count: day.count,
            storageDate: day.storageDate,
        }));

        return activeUserEvents.concat(engagedUserEvents);
    }
}
