import { ContactsJourneyEdge, EdgeTypes } from '../models/ContactsJourneyEdge';

export interface ISankeyService {
    sumCounters(edges: Array<ContactsJourneyEdge>, ignoreExitType: boolean): number;
    getEdgesDisplayValue(displayedEdges: Array<ContactsJourneyEdge>): Array<ContactsJourneyEdge>;
    createCustomTooltipContent(currentEdge: ContactsJourneyEdge, displayedEdges: Array<ContactsJourneyEdge>): string;
}
