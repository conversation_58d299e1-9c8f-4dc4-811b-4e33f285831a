import { IAnalyticsService } from './IAnalyticsService';
import { UserAnalyticsService } from './UserAnalyticsService';
import { MessageAnalyticsService } from './MessageAnalyticsService';
import { EventTrackAnalyticsService } from './EventTrackAnalyticsService';
import { ServiceType } from '../models';

export class AnalyticsServiceFactory {
    constructor(
        private UserAnalyticsService: UserAnalyticsService,
        private MessageAnalyticsService: MessageAnalyticsService,
        private EventTrackAnalyticsService: EventTrackAnalyticsService,
    ) {}

    public createService(dimension: ServiceType): IAnalyticsService {
        switch (dimension) {
            case ServiceType.Users:
                return this.UserAnalyticsService;
            case ServiceType.Messages:
                return this.MessageAnalyticsService;
            case ServiceType.Events:
                return this.EventTrackAnalyticsService;
        }
    }
}
