import {removeSufix} from 'data/string';
import { ContactsJourneyEdge, EdgeTypes } from 'modules/analytics/models/ContactsJourneyEdge';
import { ISankeyService } from './ISankeyService';
import TranslateService from 'modules/translate/TranslateService';

export class SankeyService implements ISankeyService {

    constructor(
        private TranslateService: TranslateService,
    ) {}

    private getLabelSufix(nodeName: string, step: number, type: EdgeTypes, displayedEdges: Array<ContactsJourneyEdge>): string | number {
        let nodeValue = this.sumCounters(displayedEdges.filter((x) => x.to == nodeName), true);
        let total = this.sumCounters(displayedEdges.filter((x) => x.step == step), true);
        if (!nodeValue) {
            nodeValue = this.sumCounters(displayedEdges.filter((x) => x.from == nodeName), true);
            total = this.sumCounters(displayedEdges.filter((x) => x.step == step + 1), true);
        }

        return step < 0 || total <= 0 ? total :  (nodeValue / total * 100).toFixed(2) + '%';
    }

    private getTooltipSufix(currentEdge: ContactsJourneyEdge, displayedEdges: Array<ContactsJourneyEdge>): string {
        const total = this.sumCounters(displayedEdges.filter((x) => x.from == currentEdge.from), false);
        return total ? `${(currentEdge.count / total * 100).toFixed(2)}%` : '';
    }

    public sumCounters(edges: Array<ContactsJourneyEdge>, ignoreExitType: boolean = false): number {
        const sum = ignoreExitType ?
            edges
                    .map((x) => x.count)
                    .reduce((accumulator, currentValue) => accumulator + currentValue, 0)
        :
            edges.map((x) => x.count)
                .reduce((accumulator, currentValue) => accumulator + currentValue, 0);
        return sum;
    }

    public getEdgesDisplayValue(displayedEdges: Array<ContactsJourneyEdge>): Array<ContactsJourneyEdge> {
        const edgesSufixed = new Array<ContactsJourneyEdge>();
        displayedEdges.forEach((e) => {
            const clonedEdge = new ContactsJourneyEdge();
            clonedEdge.from = e.from + ': ' + this.getLabelSufix(e.from, e.step - 1, undefined, displayedEdges);
            clonedEdge.to = e.to + ': ' + this.getLabelSufix(e.to, e.step, e.type, displayedEdges);
            clonedEdge.step = e.step;
            clonedEdge.count = e.count;
            clonedEdge.type = e.type;
            edgesSufixed.push(clonedEdge);
        });

        return edgesSufixed;
    }

    public createCustomTooltipContent(tooltipEdge: ContactsJourneyEdge, displayedEdges: Array<ContactsJourneyEdge>): string {
        const edgePercent = this.getTooltipSufix(tooltipEdge, displayedEdges);
        const contactsText = this.TranslateService.instantTranslate('contactsJourney.contacts');
        return (
            `<div class="flex flex-column">
                <bds-typo class="tooltip-node-names-text" tag="span" variant="fs-10" bold="bold">
                    ${removeSufix(tooltipEdge.from, ':')}
                </bds-typo>
                <bds-icon size="small" name="arrow-down" role="img" class="bds-icon hydrated"></bds-icon>
                <bds-typo class="tooltip-node-names-text" tag="span" variant="fs-10" bold="bold">
                     ${removeSufix(tooltipEdge.to, ':')}
                </bds-typo>
                <div>
                    <bds-typo class="tooltip-node-values-text" tag="span" variant="fs-10" bold="regular">
                        ${tooltipEdge.count} ${contactsText}:
                    </bds-typo>
                    <bds-typo class="tooltip-node-values-text" tag="span" variant="fs-10" bold="bold">
                         ${edgePercent}
                    </bds-typo>
                </div>
            </div>`
        );
    }
}
