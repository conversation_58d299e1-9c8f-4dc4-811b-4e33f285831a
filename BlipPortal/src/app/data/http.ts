export const isLocalhost = (url: string | URL): boolean => ['127.0.0.1', 'localhost', 'localdev.me']
    .some(h => (typeof url === 'string' ? new URL(url) : url).hostname.endsWith(h));

// Tried to copy the same logic used on chromium repo:
// https://github.com/chromium/chromium/blob/0ab91ac72067efd34a9f75a9a9e4039df307f2fa/third_party/blink/renderer/platform/network/http_parsers.cc#L268
export const isValidHeaderKey = (key: string): boolean => {
    if (typeof key !== 'string' || key === '') {
        return false;
    }

    // Copied from: https://chromium.googlesource.com/chromium/src/net/+/master/http/http_util.cc#506
    const isTokenChar = (c: string): boolean => {
        const charCode = c.charCodeAt(0);
        return !(charCode >= 0x7F || charCode <= 0x20 || [
            '(', ')', '<', '>', '@', ',', ';', ':', '\\', '"', '/', '[', ']', '?', '=', '{', '}'
        ].includes(c));
    };

    for (let i = 0; i < key.length; i++) {
        if (key.charCodeAt(i) > 0x7F || !isTokenChar(key.charAt(i))) {
            return false;
        }
    }

    return true;
};

// Tried to copy the same logic used on chromium repo:
// https://github.com/chromium/chromium/blob/0ab91ac72067efd34a9f75a9a9e4039df307f2fa/third_party/blink/renderer/platform/network/http_parsers.cc#L259
export const isValidHeaderValue = (value: string): boolean => {
    if (typeof value !== 'string') {
        return false;
    }

    // Check if we have non latin-1 chars
    if (/[^\u0020-\u007e\u00a0-\u00ff]/g.test(value)) {
        return false;
    }

    if (value.includes('\r') || value.includes('\n') || value.includes('\0')) {
        return false;
    }

    return true;
};

export const sanitizeHeaderValue = (value: string): string => {
    return value.replace(/[^\u0020-\u007e\u00a0-\u00ff]|\r|\n|\0/g, '');
};

export const isValidHeaders = (headers: HeadersInit | { [key: string]: string }): boolean => {
    for (const keyField in headers) {
        if (!isValidHeaderKey(keyField) || !isValidHeaderValue(headers[keyField])) {
            return false;
        }
    }

    return true;
};
