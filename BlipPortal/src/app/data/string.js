String.prototype.capitalize = function () {
    return this.replace(/(?:^|\s)\S/g, (a) => a.toUpperCase());
};

String.prototype.replaceAll = function (search, value) {
    return this.split('').map(c => c == search ? value : c).join('');
};

/**
 * Capitalize a string
 * @param {string} s - string to be capitalized
 */
export const capitalizeF = s => s.replace(/(?:^|\s)\S/g, (a) => a.toUpperCase());

export const getHashCode = (s) => {
    let hash = 0;
    if (s.length == 0) return hash;
    for (let i = 0; i < s.length; i++) {
        let character = s.charCodeAt(i);
        hash = ((hash << 5) - hash) + character;
        hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
};

export const toCamelCase = (s) =>
    s.replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) => {
        if (+match === 0) return '';
        return index === 0 ? match.toLowerCase() : match.toUpperCase();
    });

export const isEmpty = (value) => {
    return value.length === 0 && value === '';
};

export const isValidEmail = (s) => {
    return s.match(/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/gi);
};

export const removeSufix = (s, separator) => {
    let splitArray = s.split(separator);

    if (splitArray.length > 1) {
        splitArray = splitArray.slice(0, splitArray.length - 1);
    }

    return splitArray
        .reduce((previousValue, currentValue) => currentValue = previousValue + separator + currentValue);
};

export const splitString = (str, separator) => {
    if (!str) {
        return [];
    }
    const i = str.indexOf(separator);
    if (i > 0) {
        return [str.substring(0, i), str.substring(i + 1)];
    } else {
        return [str, ''];
    }
};

export const isNullOrWhiteSpace = (str) => {
    return !str || !str.trim();
};

export const isPrintableASCII = string => /^[\x20-\x7F]*$/.test(string);
