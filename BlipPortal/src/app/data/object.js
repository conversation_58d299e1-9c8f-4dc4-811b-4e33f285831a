import { toCamelCase } from 'data/string';

Object.defineProperty(Object.prototype, 'getProp', {
    value: function (...args) {
        return args.reduce(
            (pos, curr) => (pos && pos[curr] ? pos[curr] : undefined),
            this,
        );
    },
});

export const toLowerCaseObjectKeys = (object) => {
    const lowercaseObjectKeys = {};
    Object.keys(object).forEach(
        key => (lowercaseObjectKeys[key.toLocaleLowerCase()] = object[key]),
    );
    return lowercaseObjectKeys;
};

export const toCamelObjectKeys = (object) => {
    const camelCaseObjectKeys = {};
    Object.keys(object).forEach(
        key => (camelCaseObjectKeys[toCamelCase(key)] = object[key]),
    );
    return camelCaseObjectKeys;
};

export const equals = (a, b) => {
    if (a === b) { return true; }
    if (a instanceof Date && b instanceof Date) {
        return a.getTime() === b.getTime();
    }
    if (!a || !b || (typeof a !== 'object' && typeof b !== 'object')) {
        return a === b;
    }
    if (a.prototype !== b.prototype) { return false; }
    const keys = Object.keys(a);
    if (keys.length !== Object.keys(b).length) { return false; }
    return keys.every(k => this.equals(a[k], b[k]));
};
