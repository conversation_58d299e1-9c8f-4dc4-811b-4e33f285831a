import moment from 'moment';

export const SECONDS_IN_MINUTE = 60;
export const MINUTES_IN_HOUR = 60;
export const SECONDS_IN_HOUR = 3600;
export const MILLISSECONDS_IN_SECOND = 1000;

export const getDatesInPeriod = (start, end, key, arr = [moment(start).startOf(key)]) => {
    let endDate = moment(end);
    let startDate = moment(start);
    if (startDate.isAfter(endDate)) throw new Error('start must precede end');

    const next = startDate
        .add(1, key)
        .startOf(key);

    if (next.isAfter(endDate, key)) return arr;

    return getDatesInPeriod(next, endDate, key, arr.concat(next));
};

/**
 * Receives an string date and returns javascript Date
 * @param {String} d - Date in format (DD/MM/YYYY)
 */
export const formatDateFromString = d => {
    let [day, month, year] = d.split('/');

    return new Date(
        Number(year),
        Number(month) - 1,
        Number(day),
    );
};

export const getUsFormatDate = (date) => {
    let [day, month, year] = date.split('/');

    return `${month}/${day}/${year}`;
};

export const getBrFormatDate = (date) => {
    let [day, month, year] = date.split('/');

    return new Date(`${month}/${day}/${year}`);
};

export const convertHoursFormatToMinutes = (hourFormatString) => {
    const hourFormatArray = hourFormatString.split(':');
    const hours = Number(hourFormatArray[0]);
    const minutes = Number(hourFormatArray[1]);
    return ((hours * 60) + minutes);
};

export const convertMinutesToHoursFormat = (minutesFormatString) => {
    const hours = Math.trunc((Number(minutesFormatString) / 60));
    const minutes = (Number(minutesFormatString) % 60);
    return (hours + ':' + minutes);
};

export const getFormatDate = (dateValue, locale) => {
    return new Date(dateValue).toLocaleString(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
};

export const getFormatDateTime = (dateValue, locale) => {
    return new Date(dateValue).toLocaleString(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
    });
};

export const toUTCISOString = (date, hours = 0, minutes = 0, seconds = 0, milliseconds = 0) => {
    return moment(date)
        .utc(true)
        .hours(hours)
        .minutes(minutes)
        .seconds(seconds)
        .milliseconds(milliseconds)
        .toISOString();
};

export const getUTCISODate = (date) => {
    return date.setDate(date.getUTCDate());
};

export const datesDiffInDays = (date1, date2) => {
    const MILLISECONDS_PER_DAY = 1000 * 60 * 60 * 24;

    const utc1 = Date.UTC(date1.getFullYear(), date1.getMonth(), date1.getDate());
    const utc2 = Date.UTC(date2.getFullYear(), date2.getMonth(), date2.getDate());

    return Math.floor((utc2 - utc1) / MILLISECONDS_PER_DAY);
};

export const getFormattedDateTimeOnTimer = (dateTimeInput, dateTimeNow) => {
    if (dateTimeInput && dateTimeNow) {
        const dateTime = new Date(dateTimeInput);
        const nowMilliseconds = dateTimeNow.getTime();
        const timeInSeconds = (nowMilliseconds - new Date(dateTime).getTime()) / MILLISSECONDS_IN_SECOND;
        if (timeInSeconds > 0) {
            const seconds = padLeadingZeros((timeInSeconds % SECONDS_IN_MINUTE), 2);
            const minutes = padLeadingZeros((timeInSeconds / SECONDS_IN_MINUTE) % MINUTES_IN_HOUR, 2);
            const hours = padLeadingZeros((timeInSeconds / SECONDS_IN_HOUR), 2);
            return `${hours}:${minutes}:${seconds}`;
        }
    }
};

const padLeadingZeros = (num, size) => {
    let s = parseInt(num) + '';
    while (s.length < size) {
        s = '0' + s;
    }
    return s;
};