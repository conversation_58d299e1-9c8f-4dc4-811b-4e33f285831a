<?xml version="1.0" encoding="UTF-8"?>
<svg fill="none" viewBox="0 0 433 302" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#ah)">
<ellipse cx="131.08" cy="77.095" rx="54.306" ry="54.51" fill="#F5F5F5"/>
<path d="m248.24 78.843c-55.81 0-101.05 45.414-101.05 101.44s45.243 101.44 101.05 101.44c55.811 0 101.05-45.414 101.05-101.44s-45.243-101.44-101.05-101.44z" fill="#C5D9FB"/>
<g filter="url(#af)" opacity=".1">
<path d="m328.96 278.57c0 5.398-29.936 9.774-66.864 9.774s-66.864-4.376-66.864-9.774 29.936-9.774 66.864-9.774 66.864 4.376 66.864 9.774z" fill="#0A0F1A"/>
</g>
<path d="m201.93 123.47h-5.365c-1.073 0-1.916-0.847-1.916-1.925v-8.431c0-1.078 0.843-1.925 1.916-1.925h5.365c1.073 0 1.916 0.847 1.916 1.925v8.431c0 1.078-0.843 1.925-1.916 1.925z" fill="#0096FA"/>
<path d="m201.93 149.65h-5.365c-1.073 0-1.916-0.847-1.916-1.925v-17.209c0-1.078 0.843-1.925 1.916-1.925h5.365c1.073 0 1.916 0.847 1.916 1.925v17.209c0 1.078-0.843 1.925-1.916 1.925z" fill="#0096FA"/>
<path d="m296.36 161.16h-5.365c-1.073 0-1.916-0.847-1.916-1.925v-17.209c0-1.077 0.843-1.924 1.916-1.924h5.365c1.073 0 1.916 0.847 1.916 1.924v17.209c0 1.078-0.881 1.925-1.916 1.925z" fill="#0096FA"/>
<path d="m279.38 281.7h-65.914c-7.089 0-12.837-5.775-12.837-12.897v-172.7c0-7.1221 5.748-12.897 12.837-12.897h65.914c7.089 0 12.837 5.7748 12.837 12.897v172.7c-0.038 7.122-5.748 12.897-12.837 12.897z" fill="url(#y)"/>
<path d="m199.47 92.546c0-5.1685 3.908-9.3585 8.728-9.3585h78.188c4.82 0 8.728 4.19 8.728 9.3585v3.2394h-95.644v-3.2394z" fill="url(#n)"/>
<path d="m287.08 100.94 1.394-1.4032c0.034-0.0432 0.051-0.0977 0.048-0.153s-0.027-0.1074-0.066-0.1464c-0.039-0.0389-0.091-0.062-0.146-0.0648-0.055-0.0027-0.109 0.0151-0.152 0.05l-1.394 1.3994-1.394-1.3994c-0.02-0.0247-0.045-0.0449-0.072-0.0594-0.028-0.0145-0.059-0.0229-0.09-0.0247-0.031-0.0019-0.063 0.0029-0.092 0.0141-0.029 0.0111-0.056 0.0284-0.078 0.0506-0.023 0.0221-0.04 0.0488-0.051 0.0782-0.012 0.0294-0.017 0.0608-0.015 0.0923 1e-3 0.0315 9e-3 0.0623 0.024 0.0904 0.014 0.0281 0.034 0.0529 0.058 0.0727l1.393 1.4012-1.393 1.403c-0.031 0.031-0.052 0.071-0.061 0.115-8e-3 0.043-4e-3 0.088 0.013 0.129s0.046 0.076 0.082 0.101c0.037 0.024 0.08 0.037 0.124 0.037 0.029 1e-3 0.058-5e-3 0.085-0.016 0.028-0.012 0.052-0.028 0.073-0.049l1.394-1.401 1.394 1.401c0.021 0.021 0.045 0.037 0.072 0.049 0.027 0.011 0.056 0.017 0.085 0.016 0.044 0 0.088-0.013 0.124-0.037 0.037-0.025 0.065-0.06 0.082-0.101s0.022-0.086 0.013-0.129c-8e-3 -0.044-0.029-0.084-0.06-0.115l-1.394-1.401z" fill="#fff"/>
<path d="m206.18 102.35c-0.023 0-0.045-5e-3 -0.065-0.015-0.02-9e-3 -0.038-0.023-0.053-0.041l-1.076-1.288c-0.048-0.057-0.074-0.128-0.074-0.203 0-0.074 0.026-0.145 0.074-0.202l1.076-1.2884c0.027-0.0304 0.065-0.049 0.106-0.0519 0.04-0.0029 0.08 0.0101 0.111 0.0363s0.051 0.0635 0.055 0.1041c5e-3 0.0406-7e-3 0.0812-0.032 0.1133l-1.077 1.2886 1.075 1.29c0.019 0.022 0.032 0.05 0.036 0.08s-1e-3 0.06-0.014 0.087c-0.012 0.027-0.032 0.05-0.058 0.066-0.025 0.016-0.054 0.024-0.084 0.024z" fill="#fff"/>
<path d="m292.56 94.916h-91.7v12.93h91.7v-12.93z" fill="#0096FA"/>
<path d="m213.25 105.26c0.795 0 1.573-0.238 2.234-0.682s1.176-1.075 1.48-1.814c0.304-0.738 0.384-1.55 0.229-2.334-0.156-0.7836-0.539-1.5034-1.101-2.0684-0.563-0.565-1.279-0.9498-2.059-1.1058s-1.589-0.0762-2.324 0.2293-1.363 0.823-1.805 1.4872c-0.442 0.6641-0.679 1.4447-0.679 2.2437 2e-3 1.072 0.427 2.1 1.181 2.857 0.755 0.758 1.777 1.185 2.844 1.187z" clip-rule="evenodd" fill="#2C2A46" fill-rule="evenodd"/>
<g clip-path="url(#ag)">
<path d="m214.35 103.12c-0.311 0.038-0.664 0.05-1.057 0.05-0.394 0-0.746-0.012-1.057-0.05h-1e-3l-0.535 0.778c-0.031 0.043-0.077 0.065-0.124 0.065-0.046 0-0.093-0.02-0.122-0.062-0.287-0.398-0.577-0.913-0.766-1.535-0.072-0.235-0.129-0.486-0.167-0.75-0.032-0.202-0.049-0.431-0.049-0.688 0-2.0211 1.148-2.669 2.821-2.669 1.67 0 2.817 0.6461 2.82 2.661v8e-3 0.021c-1e-3 0.234-0.015 0.444-0.043 0.633-0.131 0.979-0.539 1.764-0.938 2.319-0.061 0.085-0.187 0.083-0.247-3e-3l-0.535-0.778z" clip-rule="evenodd" fill="#0096FA" fill-rule="evenodd"/>
<path d="m210.73 102.5c0.199 0.6 0.494 1.084 0.725 1.405 0.06 0.085 0.187 0.083 0.246-3e-3l0.535-0.778c-0.734-0.065-1.282-0.279-1.506-0.624z" fill="url(#h)"/>
<path d="m215.86 102.5c-0.198 0.6-0.494 1.084-0.724 1.405-0.061 0.085-0.187 0.083-0.247-3e-3l-0.535-0.778c0.734-0.065 1.282-0.279 1.506-0.624z" fill="url(#g)"/>
<path d="m213.98 101.93h0.287c-1e-3 0.092-0.065 0.166-0.144 0.166-0.078 0-0.142-0.074-0.143-0.166z" clip-rule="evenodd" fill="#000" fill-rule="evenodd"/>
<path d="m212.64 101.93h-0.287c2e-3 0.092 0.065 0.166 0.144 0.166 0.078 0 0.142-0.074 0.143-0.166z" clip-rule="evenodd" fill="#000" fill-rule="evenodd"/>
<path d="m211.56 101.47c-0.051 0-0.092 0.042-0.092 0.094s0.041 0.094 0.092 0.094h3.429c0.051 0 0.092-0.042 0.092-0.094s-0.041-0.094-0.092-0.094h-3.429zm2.045 6.075-0.159-1.232 3.74-0.099-0.517 1.504c-0.164 0.475-0.618 0.787-1.12 0.768l-0.95-0.035c-0.508-0.019-0.928-0.402-0.994-0.906zm7.237 0 0.159-1.232-3.74-0.099 0.517 1.504c0.164 0.475 0.618 0.787 1.12 0.768l0.95-0.035c0.508-0.019 0.929-0.402 0.994-0.906z" clip-rule="evenodd" fill="#0035B5" fill-rule="evenodd"/>
<path d="m214.4 102.06 0.028-0.406 0.224 0.015-0.028 0.406-0.224-0.015z" clip-rule="evenodd" fill="#0065FF" fill-rule="evenodd"/>
</g>
<path d="m217.07 99.79c0.622 0 1.126-0.5066 1.126-1.1314s-0.504-1.1313-1.126-1.1313-1.126 0.5065-1.126 1.1313 0.504 1.1314 1.126 1.1314z" fill="#90E6BC"/>
<path d="m217.07 97.673c-0.541 0-0.981 0.4413-0.981 0.9856s0.44 0.9855 0.981 0.9855c0.542 0 0.981-0.4412 0.981-0.9855s-0.439-0.9856-0.981-0.9856zm-1.271 0.9856c0-0.7053 0.569-1.2771 1.271-1.2771 0.703 0 1.272 0.5718 1.272 1.2771s-0.569 1.2771-1.272 1.2771c-0.702 0-1.271-0.5718-1.271-1.2771z" clip-rule="evenodd" fill="#2CC3D5" fill-rule="evenodd"/>
<rect x="220.6" y="101.22" width="22.033" height="1.6182" rx=".80912" fill="#fff"/>
<path d="m245.53 91.056h-10.802c-1.417 0-2.555-0.9369-2.555-2.1032 0-1.1664 1.138-2.1033 2.555-2.1033h10.802c1.416 0 2.555 0.9369 2.555 2.1033 0 1.1663-1.139 2.1032-2.555 2.1032z" fill="#0096FA"/>
<path d="m253.06 90.328c0.741 0 1.342-0.6032 1.342-1.3474s-0.601-1.3474-1.342-1.3474c-0.74 0-1.341 0.6032-1.341 1.3474s0.601 1.3474 1.341 1.3474z" fill="#F5F5F5"/>
<path d="m212.91 285.67h67.063c8.507 0 15.367-6.891 15.328-15.4v-175.59c0-8.5081-6.859-15.399-15.328-15.399h-67.063c-8.469 0-15.329 6.8911-15.329 15.399v175.59c0 8.509 6.86 15.4 15.329 15.4zm0.614-3.965h65.913c7.089 0 12.799-5.775 12.838-12.897v-172.7c0-7.1221-5.749-12.897-12.838-12.897h-65.913c-7.09 0-12.838 5.7748-12.838 12.897v172.7c0 7.122 5.748 12.897 12.838 12.897z" clip-rule="evenodd" fill="url(#f)" fill-rule="evenodd"/>
<path d="m251.14 203.58c0-1.978-1.596-3.581-3.564-3.581h-32.614c-1.968 0-3.564 1.603-3.564 3.581v4.832c0 0.659 0.532 1.193 1.188 1.193h34.99c1.968 0 3.564-1.603 3.564-3.581v-2.444z" fill="#fff"/>
<path d="m244.85 216.77c0-1.978 1.596-3.581 3.565-3.581h36.199c1.968 0 3.564 1.603 3.564 3.581v4.831c0 0.659-0.532 1.194-1.188 1.194h-38.575c-1.969 0-3.565-1.603-3.565-3.581v-2.444z" fill="#C5D9FB"/>
<path d="m244.85 248.85c0-1.978 1.596-3.581 3.565-3.581h36.199c1.968 0 3.564 1.603 3.564 3.581v4.832c0 0.659-0.532 1.193-1.188 1.193h-38.575c-1.969 0-3.565-1.603-3.565-3.581v-2.444z" fill="#C5D9FB"/>
<path d="m265.48 229.96c0-1.977-1.595-3.58-3.564-3.58h-46.956c-1.969 0-3.565 1.603-3.565 3.58v10.536c0 0.659 0.532 1.193 1.189 1.193h49.332c1.969 0 3.564-1.603 3.564-3.581v-8.148z" fill="#fff"/>
<g filter="url(#ae)">
<path d="m258.91 128.6c0-2.23-1.802-4.038-4.024-4.038h-86.362c-2.222 0-4.023 1.808-4.023 4.038v17.769c0 0.446 0.36 0.807 0.805 0.807h89.58c2.222 0 4.024-1.808 4.024-4.038v-14.538z" fill="#fff"/>
</g>
<path d="m168.82 129.32c0 6.765 5.463 12.249 12.203 12.249s12.203-5.484 12.203-12.249c0-6.766-5.463-12.25-12.203-12.25s-12.203 5.484-12.203 12.25z" fill="#FCCCCC"/>
<path d="m203.48 133.07c0-0.698 0.564-1.263 1.259-1.263h45.192c0.695 0 1.258 0.565 1.258 1.263 0 0.697-0.563 1.263-1.258 1.263h-45.192c-0.695 0-1.259-0.566-1.259-1.263z" clip-rule="evenodd" fill="#D2DFE6" fill-rule="evenodd"/>
<path d="m203.48 138.72c0-0.697 0.564-1.263 1.259-1.263h30.505c0.695 0 1.258 0.566 1.258 1.263 0 0.698-0.563 1.263-1.258 1.263h-30.505c-0.695 0-1.259-0.565-1.259-1.263z" clip-rule="evenodd" fill="#D2DFE6" fill-rule="evenodd"/>
<path d="m180.81 139.95c6.695 0 12.122-5.452 12.122-12.178 0-6.725-5.427-12.178-12.122-12.178s-12.122 5.453-12.122 12.178c0 6.726 5.427 12.178 12.122 12.178z" fill="#C5D9FB"/>
<path d="m168.94 130.14c0.993 5.021 5.075 9.044 10.399 9.699 5.277 0.655 10.182-2.23 12.37-6.783l-22.769-2.916z" fill="url(#e)"/>
<path d="m187.46 128.86v-1.123l-1.584-4.444 0.451-0.405 1.396 1.808 0.373-0.233 1.148-4.132 0.497 0.062-0.186 2.885 1.117-3.556 0.637 0.094-0.574 3.602 1.35-2.666 0.543 0.311-0.776 2.885 1.335-1.933 0.388 0.187-1.77 5.566v1.014l-4.345 0.078z" fill="url(#d)"/>
<path d="m186.29 138.61c3.182-1.622 5.557-4.615 6.349-8.217v-1.123c0-0.359-0.295-0.655-0.652-0.655h-5.029c-0.357 0-0.652 0.296-0.652 0.655v9.34h-0.016z" fill="url(#c)"/>
<path d="m177.15 130.55c4.535 0 8.211-3.568 8.211-7.968 0-4.401-3.676-7.968-8.211-7.968-4.534 0-8.21 3.567-8.21 7.968 0 4.4 3.676 7.968 8.21 7.968z" fill="#000"/>
<path d="m176.36 126.18 0.015-3.228 4.439-1.481 2.841 4.397-1.009 0.717-0.062 3.197-1.646-0.094v1.996l-4.578-0.593v-2.557h-0.575c-0.372 0-0.667-0.296-0.667-0.67v-1.7h1.242v0.016z" fill="#6E3A0C"/>
<path d="m180.95 129.69-2.08-0.171 2.064 1.715 0.016-1.544z" fill="url(#b)"/>
<g filter="url(#ad)">
<path d="m237.64 167.98c0-2.23 1.802-4.038 4.023-4.038h86.363c2.222 0 4.023 1.808 4.023 4.038v17.769c0 0.446-0.36 0.807-0.805 0.807h-89.581c-2.221 0-4.023-1.808-4.023-4.038v-14.538z" fill="#F6F6F6"/>
</g>
<path d="m293.07 172.45c0-0.698-0.563-1.263-1.258-1.263h-45.193c-0.695 0-1.258 0.565-1.258 1.263 0 0.697 0.563 1.263 1.258 1.263h45.193c0.695 0 1.258-0.566 1.258-1.263z" clip-rule="evenodd" fill="#1968F0" fill-rule="evenodd"/>
<path d="m293.07 178.1c0-0.697-0.563-1.262-1.258-1.262h-30.505c-0.695 0-1.259 0.565-1.259 1.262 0 0.698 0.564 1.263 1.259 1.263h30.505c0.695 0 1.258-0.565 1.258-1.263z" clip-rule="evenodd" fill="#1968F0" fill-rule="evenodd"/>
<mask id="z" x="298" y="158" width="26" height="25" style="mask-type:alpha" maskUnits="userSpaceOnUse">
<path d="m311.03 182.72c6.695 0 12.122-5.452 12.122-12.177 0-6.726-5.427-12.178-12.122-12.178s-12.122 5.452-12.122 12.178c0 6.725 5.427 12.177 12.122 12.177z" fill="#FDE99B"/>
</mask>
<g mask="url(#z)">
<path d="m309.21 185.52c8.355 1.126 16.036-4.765 17.157-13.158 1.12-8.393-4.744-16.11-13.098-17.235-8.355-1.126-16.036 4.765-17.157 13.158-1.12 8.393 4.744 16.109 13.098 17.235z" fill="#1156CF"/>
<path d="m314.08 176.26 5.041-5.754-0.641-6.156-12.575-2.668-1.182 3.006 2.679 1.617 6.678 9.955z" fill="#F06305"/>
<path d="m307.68 164.8-1.65 2.603-0.117 1.659-1.546 3.192 0.914 0.385 0.03 4.086 2.641-0.061 0.377 3.243 6.521 0.175-0.991-6.466 1.308-1.21-1.224-1.971-0.852 0.722-0.578-3.689-4.833-2.668z" fill="#FCAA73"/>
<path d="m308.21 178.82 2.749-2.244-3.01 0.09 0.261 2.154z" fill="url(#a)"/>
<path d="m312.49 175.6 1.959 1.939-0.599-3.927-1.36 1.988z" fill="url(#x)"/>
<path d="m305.28 174.42 1.015-1.354-1.013-0.425-2e-3 1.779z" fill="url(#w)"/>
</g>
<g filter="url(#ac)">
<path d="m271.6 197.47c0-2.082 1.681-3.769 3.755-3.769h13.142c2.074 0 3.755 1.687 3.755 3.769v5.115c0 0.669-0.54 1.211-1.207 1.211h-15.69c-2.074 0-3.755-1.687-3.755-3.769v-2.557z" fill="#fff"/>
</g>
<path d="m284.9 199.6c0.43-1.65-0.555-3.338-2.199-3.769s-3.325 0.557-3.754 2.207c-0.43 1.65 0.554 3.338 2.198 3.769s3.325-0.557 3.755-2.207z" fill="#F6A721"/>
<path d="m280.93 198.25c0 0.209-0.142 0.379-0.316 0.379s-0.316-0.17-0.316-0.379 0.143-0.379 0.316-0.379 0.316 0.17 0.316 0.379z" fill="#2C2A46"/>
<path d="m283.45 198.63c-0.027 1e-3 -0.052-9e-3 -0.071-0.027-0.207-0.19-0.51-0.041-0.514-0.039-0.019 9e-3 -0.041 0.012-0.062 8e-3 -0.021-5e-3 -0.04-0.016-0.055-0.032-0.014-0.015-0.023-0.036-0.025-0.057-2e-3 -0.022 3e-3 -0.043 0.014-0.062 0.077-0.119 0.185-0.215 0.312-0.278 0.127-0.064 0.268-0.093 0.41-0.084 0.013 1e-3 0.026 4e-3 0.038 0.01 0.011 6e-3 0.022 0.014 0.031 0.024 8e-3 0.01 0.015 0.022 0.019 0.035 4e-3 0.012 6e-3 0.025 5e-3 0.039-1e-3 0.013-4e-3 0.026-0.01 0.038-6e-3 0.011-0.014 0.022-0.024 0.031-0.01 8e-3 -0.022 0.015-0.034 0.019-0.013 4e-3 -0.026 6e-3 -0.039 5e-3 -0.094-6e-3 -0.188 0.011-0.274 0.049 0.128 8e-3 0.249 0.061 0.343 0.148 0.01 9e-3 0.018 0.02 0.024 0.032 5e-3 0.012 8e-3 0.025 9e-3 0.038 0 0.013-2e-3 0.027-6e-3 0.039-5e-3 0.013-0.012 0.024-0.021 0.034-9e-3 9e-3 -0.02 0.017-0.032 0.022s-0.025 8e-3 -0.038 8e-3z" fill="#2C2A46"/>
<path d="m282.14 200.82c-0.335 0-0.66-0.112-0.924-0.317-0.03-0.026-0.048-0.061-0.052-0.1-4e-3 -0.038 7e-3 -0.077 0.03-0.108 0.024-0.03 0.059-0.051 0.097-0.057s0.077 3e-3 0.109 0.026c0.181 0.14 0.398 0.225 0.625 0.247 0.228 0.021 0.457-0.023 0.66-0.127 0.204-0.104 0.374-0.264 0.491-0.461 0.117-0.198 0.176-0.424 0.169-0.653-1e-3 -0.041 0.014-0.08 0.041-0.109 0.028-0.029 0.066-0.046 0.105-0.047 0.04-2e-3 0.079 0.013 0.108 0.041 0.03 0.027 0.047 0.065 0.048 0.105 0.011 0.401-0.136 0.791-0.41 1.083s-0.652 0.464-1.052 0.476l-0.045 1e-3z" fill="#2C2A46"/>
<g filter="url(#ab)">
<path d="m230.16 153.86c0-2.081-1.681-3.769-3.755-3.769h-13.142c-2.074 0-3.755 1.688-3.755 3.769v5.116c0 0.669 0.54 1.211 1.207 1.211h15.69c2.074 0 3.755-1.687 3.755-3.769v-2.558z" fill="#fff"/>
</g>
<path d="m220.35 158.45c1.788-0.287 3.005-1.975 2.719-3.769-0.286-1.795-1.967-3.017-3.754-2.73-1.788 0.288-3.006 1.975-2.72 3.769 0.286 1.795 1.968 3.017 3.755 2.73z" fill="#F6A721"/>
<path d="m221.74 154.78c7e-3 4e-3 0.016 6e-3 0.025 6e-3 8e-3 0 0.017-3e-3 0.025-7e-3 7e-3 -4e-3 0.013-0.01 0.018-0.018 4e-3 -7e-3 7e-3 -0.016 7e-3 -0.025 0-0.115-0.045-0.226-0.126-0.307-0.082-0.082-0.192-0.127-0.307-0.127s-0.225 0.045-0.306 0.127c-0.081 0.081-0.127 0.192-0.127 0.307 0 0.01 3e-3 0.019 8e-3 0.026 4e-3 8e-3 0.011 0.015 0.019 0.019 8e-3 5e-3 0.017 7e-3 0.027 6e-3 9e-3 0 0.018-2e-3 0.025-7e-3 0.107-0.066 0.229-0.101 0.355-0.101 0.125 0 0.247 0.035 0.354 0.101h3e-3z" fill="#2C2A46"/>
<path d="m218.64 154.78c8e-3 4e-3 0.016 6e-3 0.025 6e-3s0.017-3e-3 0.025-7e-3c7e-3 -4e-3 0.014-0.01 0.018-0.018 5e-3 -7e-3 7e-3 -0.016 8e-3 -0.025 0-0.115-0.046-0.226-0.127-0.307-0.081-0.082-0.192-0.127-0.307-0.127-0.114 0-0.225 0.045-0.306 0.127-0.081 0.081-0.127 0.192-0.127 0.307 1e-3 9e-3 3e-3 0.018 8e-3 0.025 4e-3 8e-3 0.011 0.014 0.018 0.018 8e-3 4e-3 0.016 7e-3 0.025 7e-3s0.017-2e-3 0.025-6e-3c0.107-0.066 0.229-0.101 0.354-0.101 0.126 0 0.248 0.035 0.355 0.101h6e-3z" fill="#2C2A46"/>
<path d="m219.85 156.7c-0.553-1e-3 -1.085-0.209-1.492-0.585-0.028-0.029-0.043-0.069-0.044-0.11 0-0.041 0.016-0.08 0.043-0.11 0.028-0.03 0.066-0.048 0.107-0.051s0.081 0.01 0.112 0.036c0.341 0.315 0.786 0.492 1.249 0.497 0.463 6e-3 0.912-0.161 1.26-0.468 0.016-0.014 0.034-0.024 0.054-0.031s0.041-0.01 0.062-9e-3c0.021 2e-3 0.042 7e-3 0.06 0.017 0.019 9e-3 0.036 0.022 0.05 0.038 0.014 0.015 0.025 0.034 0.032 0.054 6e-3 0.02 9e-3 0.041 8e-3 0.062s-7e-3 0.042-0.016 0.061-0.022 0.035-0.038 0.049c-0.399 0.355-0.914 0.55-1.447 0.55z" fill="#2C2A46"/>
<path d="m310.64 121.79v-10.958h6.476v10.958h-6.476z" fill="#8C3A03"/>
<path d="m309.01 110.86 8.683 10.878c-0.545 0.676-1.017 4.458 0.868 7.16 3.573 5.156-0.331 5.063-0.331 5.063l-14.305-17.923 5.085-5.178z" fill="url(#v)"/>
<path d="m271.5 66.819h34.295c4.911 0 8.897 4.0039 8.897 8.936v0.0796l6.495 38.529h-17.794l-5.148-30.574h-22.916c-1.162 0.5569-19.174 0.875-20.547 0.875-4.91 0 7.795-4.004 7.795-8.9361 0.026-4.9056 4.013-8.9095 8.923-8.9095z" fill="url(#u)"/>
<path d="m301.59 102.56 13.228-4.2401c0.434 0.7528 3.782 2.5501 6.996 1.7431 6.076-1.5014 4.591 2.124 4.591 2.124l-21.795 6.986-3.02-6.613z" fill="#072F73"/>
<path d="m317.24 97.54-16.982 5.4434-17.688-34.826 16.983-5.4434 17.687 34.826z" fill="#0096FA"/>
<path d="m293.74 73.882c-4.69 1.5031-9.706-1.0944-11.203-5.8016-1.498-4.7073 1.09-9.7418 5.78-11.245 4.689-1.5032 9.705 1.0942 11.202 5.8015 1.498 4.7072-1.09 9.7418-5.779 11.245z" fill="#0096FA"/>
<path d="m259.64 83.879c-6.989 2.2401-14.463-1.6308-16.695-8.646-2.231-7.0152 1.625-14.518 8.614-16.758 6.988-2.2401 14.463 1.6309 16.695 8.6461 2.231 7.0151-1.625 14.518-8.614 16.758z" fill="#0096FA"/>
<path d="m254.35 66.769 33.969-9.9337 5.149 16.187-32.73 10.491-6.388-16.744z" fill="#0096FA"/>
<path d="m242.79 74.662-2.029-18.754h18.132l-16.103 18.754z" fill="#0096FA"/>
<path d="m270.11 25.793h-18.991c-1.165 0-2.12-0.9582-2.12-2.1278v-14.409c0-1.1696 0.955-2.1277 2.12-2.1277h18.991c1.165 0 2.12 0.9581 2.12 2.1277v14.409c0 1.1696-0.955 2.1278-2.12 2.1278z" fill="#141414"/>
<path d="m273.83 29.675h-14.057c-1.165 0-2.12-0.9581-2.12-2.1277v-10.44c0-1.1696 0.955-2.1277 2.12-2.1277h14.057c1.166 0 2.12 0.9581 2.12 2.1277v10.44c0 1.1821-0.942 2.1277-2.12 2.1277z" fill="#141414"/>
<path d="m260.02 30.994h-14.058c-1.165 0-2.119-0.9581-2.119-2.1277v-10.44c0-1.1696 0.954-2.1277 2.119-2.1277h14.058c1.165 0 2.119 0.9581 2.119 2.1277v10.44c0 1.182-0.942 2.1277-2.119 2.1277z" fill="#141414"/>
<path d="m267.69 3.3459h-11.839l-0.905 0.90833v22.36h8.752v-3.5836h3.992c0.347-0.3484 0.545-0.5475 0.905-0.9083v-17.868l-0.905-0.90833z" fill="#8C3A03"/>
<path d="m255.85 3.3459-0.905 0.90833-0.062 10.24 0.075 0.0498 9.024-9.1082 4.612 5.5744v-6.7565l-0.905-0.90833h-11.839z" fill="#141414"/>
<path d="m253.87 18.116h2.479v-3.8947h-3.359v3.0237c0 0.4852 0.397 0.871 0.88 0.871z" fill="#8C3A03"/>
<path d="m256.98 22.869h-2.033v-4.7532h2.033v4.7532z" fill="url(#t)"/>
<path d="m260.61 23.018h3.087v1.2443h-3.087v-1.2443z" fill="url(#s)"/>
<path d="m265.02 13.029c-0.122 0.0534-0.178 0.1958-0.124 0.318l1.848 4.2407h-1.826c-0.133 0-0.241 0.1081-0.241 0.2415s0.108 0.2415 0.241 0.2415h2.194c0.081 0 0.157-0.0411 0.201-0.1093 0.045-0.0682 0.052-0.1543 0.019-0.229l-1.996-4.579c-0.053-0.1222-0.195-0.1779-0.316-0.1244z" clip-rule="evenodd" fill="#141414" fill-rule="evenodd"/>
<path d="m237.68 38.014c0.593-3.3614 2.979-6.1151 6.204-7.1694l11.074-3.6592v-2.8529h8.713v2.8529l1.409 0.4714 9.664 3.1878c3.226 1.0667 5.611 3.8204 6.205 7.1694l-1.531 19.284v2.6929l-37.374 10.215-6.568-20.058 2.204-12.134z" fill="#35DE90"/>
<path d="m247.07 58.319h-13.011l1.631-9.2859h12.986l-1.606 9.2859z" fill="#8C3A03"/>
<path d="m241.49 66.862c-4.138 0-7.493-3.4091-7.493-7.6145s3.355-7.6145 7.493-7.6145c4.139 0 7.494 3.4091 7.494 7.6145s-3.355 7.6145-7.494 7.6145z" fill="#8C3A03"/>
<path d="m250.11 41.604-3.098 16.715h14.802l-11.704-16.715z" fill="url(#r)"/>
<path d="m238.5 58.319 8.925-6.0607 8.862-6.0111 6.266 9.0972-17.918 10.961-6.135-7.986z" fill="#8C3A03"/>
<path d="m248.68 49.776-0.359 1.9601-9.666 6.583h-4.592l0.533-3.0696 14.084-5.4735z" fill="url(#q)"/>
<path d="m256.27 46.291 13.686-1.5299 6.245 1.9617 3.752 2.7884-1.074 1.0487-2.382-1.024 2.629 2.5416-1.407 0.95-5.85-3.7261 3.678 4.1826-1.111 0.8143-4.443-2.9241 2.716 2.9858-0.963 0.7897-4.369-2.8254-4.949 3.0228-6.158-9.0561z" fill="#8C3A03"/>
<path d="m263.66 56.976h29.539c0.608 0 1.066 0.56 0.955 1.1572l-0.186 0.9581c-0.087 0.4604-0.483 0.7839-0.955 0.7839h-29.539c-0.608 0-1.066-0.5599-0.955-1.1572l0.186-0.9581c0.087-0.4603 0.483-0.7839 0.955-0.7839z" fill="#072F73"/>
<path d="m312.22 38.075h-32.552c-0.458 0-0.855 0.3235-0.954 0.7839l-3.843 19.859c-0.112 0.5972 0.347 1.1572 0.954 1.1572h32.553c0.458 0 0.855-0.3236 0.954-0.7839l3.843-19.859c0.111-0.5973-0.347-1.1572-0.955-1.1572z" fill="#0C4EC0"/>
<path d="m114.76 248.65c-9.033 3.548-16.156-0.632-18.722-3.385l3.1815-2.987c1.6463 1.766 6.9543 5.049 13.953 2.3l1.587 4.072z" clip-rule="evenodd" fill="#0188FB" fill-rule="evenodd"/>
<path d="m164.4 247.22c9.694-0.355 14.571-7.036 15.832-10.587l-4.103-1.469c-0.809 2.278-4.376 7.411-11.887 7.686l0.158 4.37z" clip-rule="evenodd" fill="#0188FB" fill-rule="evenodd"/>
<path d="m121.81 246.18c0 3.019-2.438 5.466-5.445 5.466-3.008 0-5.446-2.447-5.446-5.466s2.438-5.467 5.446-5.467c3.007 0 5.445 2.448 5.445 5.467z" fill="#072F73"/>
<path d="m156.66 246.18c0 3.019 2.439 5.466 5.446 5.466 3.008 0 5.446-2.447 5.446-5.466s-2.438-5.467-5.446-5.467c-3.007 0-5.446 2.448-5.446 5.467z" fill="#072F73"/>
<path d="m130.09 269.57c1.684 0 3.05-1.371 3.05-3.062 0-1.69-1.366-3.061-3.05-3.061s-3.05 1.371-3.05 3.061c0 1.691 1.366 3.062 3.05 3.062zm0 2.623c3.128 0 5.664-2.545 5.664-5.685 0-3.139-2.536-5.685-5.664-5.685s-5.664 2.546-5.664 5.685c0 3.14 2.536 5.685 5.664 5.685z" clip-rule="evenodd" fill="#2C2A46" fill-rule="evenodd"/>
<path d="m148.82 269.57c1.684 0 3.049-1.371 3.049-3.062 0-1.69-1.365-3.061-3.049-3.061s-3.05 1.371-3.05 3.061c0 1.691 1.366 3.062 3.05 3.062zm0 2.623c3.128 0 5.663-2.545 5.663-5.685 0-3.139-2.535-5.685-5.663-5.685s-5.664 2.546-5.664 5.685c0 3.14 2.536 5.685 5.664 5.685z" clip-rule="evenodd" fill="#2C2A46" fill-rule="evenodd"/>
<path d="m129.65 261.7h19.17v11.807c0 5.314-4.292 9.621-9.585 9.621s-9.585-4.307-9.585-9.621v-11.807z" fill="#072F73"/>
<path d="m133.79 270.45c0-0.483 0.39-0.874 0.872-0.874h9.802c0.481 0 0.871 0.391 0.871 0.874s-0.39 0.875-0.871 0.875h-9.802c-0.482 0-0.872-0.392-0.872-0.875z" clip-rule="evenodd" fill="#F4F5F6" fill-rule="evenodd"/>
<path d="m133.79 274.82c0-0.483 0.39-0.874 0.872-0.874h9.802c0.481 0 0.871 0.391 0.871 0.874s-0.39 0.875-0.871 0.875h-9.802c-0.482 0-0.872-0.392-0.872-0.875z" clip-rule="evenodd" fill="#F4F5F6" fill-rule="evenodd"/>
<path d="m91.965 235.02c0-0.966 0.7802-1.749 1.7426-1.749 3.9701 0 7.188 3.23 7.188 7.215 0 3.986-3.2179 7.216-7.188 7.216s-7.1885-3.23-7.1885-7.216c0-0.966 0.7802-1.749 1.7427-1.749 0.9624 0 1.7427 0.783 1.7427 1.749 0 2.053 1.6579 3.718 3.7031 3.718s3.7032-1.665 3.7032-3.718c0-2.052-1.658-3.717-3.7032-3.717-0.9624 0-1.7426-0.783-1.7426-1.749z" clip-rule="evenodd" fill="#0188FB" fill-rule="evenodd"/>
<path d="m178.17 224.99c-0.568-0.78-1.658-0.952-2.435-0.382-3.208 2.348-3.912 6.862-1.572 10.081 2.339 3.22 6.836 3.927 10.044 1.578 3.207-2.348 3.911-6.862 1.571-10.081-0.567-0.781-1.657-0.952-2.434-0.383-0.778 0.569-0.949 1.664-0.381 2.444 1.205 1.659 0.842 3.984-0.81 5.194-1.653 1.209-3.969 0.846-5.174-0.813s-0.843-3.984 0.809-5.194c0.778-0.569 0.949-1.663 0.382-2.444z" clip-rule="evenodd" fill="#0188FB" fill-rule="evenodd"/>
<path d="m138.36 267.68c13.615 0 20.507-2.007 20.507-16.194 0-14.617-8.346-19.3-20.507-19.3-12.16 0-20.507 4.683-20.507 19.3 0 14.187 6.893 16.194 20.507 16.194z" fill="url(#p)"/>
<path d="m133.57 250.33c0 1.932-1.56 3.498-3.485 3.498s-3.485-1.566-3.485-3.498 1.56-3.498 3.485-3.498 3.485 1.566 3.485 3.498z" fill="url(#o)"/>
<path d="m142.72 250.33c0 1.932-1.56 3.499-3.485 3.499s-3.485-1.567-3.485-3.499 1.56-3.498 3.485-3.498 3.485 1.566 3.485 3.498z" fill="url(#m)"/>
<path d="m151.87 250.33c0 1.932-1.56 3.499-3.485 3.499s-3.485-1.567-3.485-3.499 1.56-3.498 3.485-3.498 3.485 1.566 3.485 3.498z" fill="url(#l)"/>
<path d="m119.6 217.32c6.42 0.23 9.697-0.593 9.926-7.286 0.247-6.884-3.601-9.236-9.335-9.446-5.716-0.211-9.735 1.855-9.983 8.739-0.229 6.712 2.972 7.764 9.392 7.993z" fill="#072F73"/>
<path d="m157.51 217.3c-6.42 0.229-9.697-0.593-9.925-7.286-0.248-6.884 3.6-9.236 9.335-9.446 5.715-0.211 9.735 1.855 9.982 8.739 0.229 6.693-2.972 7.764-9.392 7.993z" fill="#072F73"/>
<path d="m138.37 174.87c0.505 0 0.914 0.41 0.914 0.917v21.035c0 0.506-0.409 0.917-0.914 0.917-0.504 0-0.913-0.411-0.913-0.917v-21.035c0-0.507 0.409-0.917 0.913-0.917z" clip-rule="evenodd" fill="#072F73" fill-rule="evenodd"/>
<path d="m138.56 197.66c4.61 0 6.934-0.689 6.934-5.488 0-4.953-2.819-6.54-6.934-6.54s-6.935 1.587-6.935 6.54c0 4.819 2.324 5.488 6.935 5.488z" fill="url(#k)"/>
<path d="m138.37 181.64c2.871 0 5.2-2.337 5.2-5.219 0-2.883-2.329-5.22-5.2-5.22-2.872 0-5.2 2.337-5.2 5.22 0 2.882 2.328 5.219 5.2 5.219z" fill="#0096FA"/>
<path d="m138.37 181.64c2.871 0 5.2-2.337 5.2-5.219 0-2.883-2.329-5.22-5.2-5.22-2.872 0-5.2 2.337-5.2 5.22 0 2.882 2.328 5.219 5.2 5.219z" fill="url(#j)"/>
<path d="m138.56 228.72c15.164 0 22.842-2.237 22.842-18.052 0-16.292-9.297-21.513-22.842-21.513s-22.842 5.221-22.842 21.513c0 15.815 7.677 18.052 22.842 18.052z" fill="url(#i)"/>
<path d="m148.04 196.54c-6.344-0.536-12.631-0.536-18.975 0-2.514 0.21-4.743 2.505-5.41 4.838-1.029 4.991-1.467 9.752-1.181 14.858 0.381 2.371 2.591 4.704 5.048 4.953 7.392 0.727 14.669 0.727 22.08 0 2.305-0.421 4.839-2.467 5.049-4.953 0.285-5.106-0.153-9.867-1.182-14.858-0.552-2.467-3.029-4.456-5.429-4.838z" fill="#072F73"/>
<path d="m130.69 211.32c1.381 0 2.501-1.124 2.501-2.51s-1.12-2.51-2.501-2.51-2.501 1.124-2.501 2.51 1.12 2.51 2.501 2.51z" fill="#35DE90"/>
<path d="m146.38 211.32c1.381 0 2.5-1.124 2.5-2.51s-1.119-2.51-2.5-2.51c-1.382 0-2.501 1.124-2.501 2.51s1.119 2.51 2.501 2.51z" fill="#35DE90"/>
<path d="m136.46 208.85c0.352 0 0.637 0.286 0.637 0.639 0 0.814 0.668 1.484 1.478 1.484s1.478-0.67 1.478-1.484c0-0.353 0.285-0.639 0.637-0.639 0.351 0 0.636 0.286 0.636 0.639 0 1.52-1.237 2.762-2.751 2.762s-2.751-1.242-2.751-2.762c0-0.353 0.285-0.639 0.636-0.639z" clip-rule="evenodd" fill="#35DE90" fill-rule="evenodd"/>
<path d="m132.23 230.4c0-0.452 0.39-0.818 0.872-0.818h10.236c0.481 0 0.871 0.366 0.871 0.818 0 0.451-0.39 0.817-0.871 0.817h-10.236c-0.482 0-0.872-0.366-0.872-0.817z" clip-rule="evenodd" fill="#0188FB" fill-rule="evenodd"/>
<g filter="url(#aa)" opacity=".1">
<path d="m155.11 285.8c0 2.771-7.026 5.018-15.694 5.018s-15.695-2.247-15.695-5.018c0-2.772 7.027-5.019 15.695-5.019s15.694 2.247 15.694 5.019z" fill="#0A0F1A"/>
</g>
</g>
<defs>
<filter id="af" x="179.23" y="252.8" width="165.73" height="51.548" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur result="effect1_foregroundBlur_1564_12386" stdDeviation="8"/>
</filter>
<filter id="ae" x="156.36" y="117.78" width="110.7" height="38.905" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1.35753"/>
<feGaussianBlur stdDeviation="4.07258"/>
<feColorMatrix values="0 0 0 0 0.374547 0 0 0 0 0.481553 0 0 0 0 0.601935 0 0 0 0.15 0"/>
<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1564_12386"/>
<feBlend in="SourceGraphic" in2="effect1_dropShadow_1564_12386" result="shape"/>
</filter>
<filter id="ad" x="229.5" y="157.16" width="110.7" height="38.905" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="1.35753"/>
<feGaussianBlur stdDeviation="4.07258"/>
<feColorMatrix values="0 0 0 0 0.374547 0 0 0 0 0.481553 0 0 0 0 0.601935 0 0 0 0.15 0"/>
<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1564_12386"/>
<feBlend in="SourceGraphic" in2="effect1_dropShadow_1564_12386" result="shape"/>
</filter>
<filter id="ac" x="267.88" y="190.6" width="28.089" height="17.532" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.619731"/>
<feGaussianBlur stdDeviation="1.85919"/>
<feColorMatrix values="0 0 0 0 0.374547 0 0 0 0 0.481553 0 0 0 0 0.601935 0 0 0 0.15 0"/>
<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1564_12386"/>
<feBlend in="SourceGraphic" in2="effect1_dropShadow_1564_12386" result="shape"/>
</filter>
<filter id="ab" x="205.79" y="146.99" width="28.089" height="17.532" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="0.619731"/>
<feGaussianBlur stdDeviation="1.85919"/>
<feColorMatrix values="0 0 0 0 0.374547 0 0 0 0 0.481553 0 0 0 0 0.601935 0 0 0 0.15 0"/>
<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_1564_12386"/>
<feBlend in="SourceGraphic" in2="effect1_dropShadow_1564_12386" result="shape"/>
</filter>
<filter id="aa" x="109.97" y="267.02" width="58.902" height="37.551" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur result="effect1_foregroundBlur_1564_12386" stdDeviation="6.87824"/>
</filter>
<linearGradient id="y" x1="167.27" x2="298.02" y1="22.778" y2="302.16" gradientUnits="userSpaceOnUse">
<stop stop-color="#fff" offset="0"/>
<stop stop-color="#F7FAFE" offset=".14772"/>
<stop stop-color="#E2ECFD" offset=".30404"/>
<stop stop-color="#FAFCFF" offset=".5162"/>
<stop stop-color="#C5D9FB" offset=".9704"/>
</linearGradient>
<linearGradient id="n" x1="255.73" x2="247.45" y1="97.523" y2="77.127" gradientUnits="userSpaceOnUse">
<stop stop-color="#0D4FC1" offset="0"/>
<stop stop-color="#135CD8" offset=".48267"/>
<stop stop-color="#1A69F1" offset="1"/>
</linearGradient>
<linearGradient id="h" x1="212.27" x2="210.8" y1="102.98" y2="103.31" gradientUnits="userSpaceOnUse">
<stop stop-color="#0012FF" stop-opacity=".2" offset=".010557"/>
<stop stop-color="#0012FF" stop-opacity="0" offset=".5203"/>
</linearGradient>
<linearGradient id="g" x1="214.32" x2="215.79" y1="102.98" y2="103.31" gradientUnits="userSpaceOnUse">
<stop stop-color="#0012FF" stop-opacity=".2" offset=".010557"/>
<stop stop-color="#0012FF" stop-opacity="0" offset=".5203"/>
</linearGradient>
<linearGradient id="f" x1="183.17" x2="332.67" y1="122.21" y2="263.23" gradientUnits="userSpaceOnUse">
<stop stop-color="#1968F0" offset="0"/>
<stop stop-color="#125BD8" offset=".2912"/>
<stop stop-color="#0E51C6" offset=".5885"/>
<stop stop-color="#0C4EC0" offset=".8048"/>
</linearGradient>
<linearGradient id="e" x1="172.34" x2="188.31" y1="137.25" y2="128.66" gradientUnits="userSpaceOnUse">
<stop stop-color="#0096FA" offset=".065031"/>
<stop stop-color="#0093FA" offset=".2889"/>
<stop stop-color="#0089FB" offset=".4695"/>
<stop stop-color="#0078FC" offset=".6351"/>
<stop stop-color="#0060FD" offset=".7917"/>
<stop stop-color="#0041FE" offset=".9406"/>
<stop stop-color="#03f" offset="1"/>
</linearGradient>
<linearGradient id="d" x1="189.72" x2="189.72" y1="123.58" y2="131.33" gradientUnits="userSpaceOnUse">
<stop stop-color="#6E3A0C" offset=".375"/>
<stop stop-color="#6A380C" offset=".5005"/>
<stop stop-color="#5F320A" offset=".6185"/>
<stop stop-color="#4C2808" offset=".7335"/>
<stop stop-color="#311A05" offset=".8467"/>
<stop stop-color="#0F0802" offset=".9576"/>
<stop offset="1"/>
</linearGradient>
<linearGradient id="c" x1="184.72" x2="195.22" y1="129.46" y2="138.98" gradientUnits="userSpaceOnUse">
<stop stop-color="#0096FA" offset=".2046"/>
<stop stop-color="#0092FA" offset=".3622"/>
<stop stop-color="#0086FB" offset=".5231"/>
<stop stop-color="#0071FC" offset=".6855"/>
<stop stop-color="#0055FD" offset=".8479"/>
<stop stop-color="#03f" offset="1"/>
</linearGradient>
<linearGradient id="b" x1="179.5" x2="181.49" y1="130.82" y2="128.64" gradientUnits="userSpaceOnUse">
<stop stop-color="#6E3A0C" offset=".2107"/>
<stop stop-color="#65350B" offset=".3223"/>
<stop stop-color="#4E2908" offset=".5021"/>
<stop stop-color="#271504" offset=".7264"/>
<stop offset=".9218"/>
</linearGradient>
<linearGradient id="a" x1="310.45" x2="308.04" y1="178.43" y2="175.94" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0" offset=".2944"/>
<stop stop-opacity=".5" offset="1"/>
</linearGradient>
<linearGradient id="x" x1="312.68" x2="315.62" y1="178.31" y2="173.12" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0" offset=".3675"/>
<stop stop-opacity=".5" offset=".8842"/>
</linearGradient>
<linearGradient id="w" x1="306.43" x2="305.39" y1="175.08" y2="172.07" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0" offset=".3675"/>
<stop stop-opacity=".5" offset=".9278"/>
</linearGradient>
<linearGradient id="v" x1="322.46" x2="300.77" y1="123.55" y2="120.78" gradientUnits="userSpaceOnUse">
<stop stop-color="#072F73" offset=".2603"/>
<stop stop-color="#072C6D" offset=".3664"/>
<stop stop-color="#06255B" offset=".5132"/>
<stop stop-color="#04193D" offset=".6836"/>
<stop stop-color="#010915" offset=".8706"/>
<stop offset=".9558"/>
</linearGradient>
<linearGradient id="u" x1="318.91" x2="270.63" y1="73.598" y2="107.52" gradientUnits="userSpaceOnUse">
<stop stop-color="#0096FA" offset=".026201"/>
<stop stop-color="#1968F0" offset=".641"/>
</linearGradient>
<linearGradient id="t" x1="257.87" x2="252.42" y1="20.961" y2="19.634" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C3A03" stop-opacity=".2" offset="0"/>
<stop stop-color="#763103" offset=".9375"/>
</linearGradient>
<linearGradient id="s" x1="260.85" x2="263.53" y1="25.555" y2="21.649" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C3A03" stop-opacity=".13" offset="0"/>
<stop stop-color="#642901" offset="1"/>
</linearGradient>
<linearGradient id="r" x1="258.42" x2="245.06" y1="47.82" y2="59.541" gradientUnits="userSpaceOnUse">
<stop stop-color="#35DE90" offset=".1453"/>
<stop stop-color="#1BA766" offset=".65254"/>
</linearGradient>
<linearGradient id="q" x1="239.05" x2="244.93" y1="48.824" y2="62.162" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8BC9A" stop-opacity="0" offset=".2814"/>
<stop stop-color="#8C3A03" offset=".297"/>
<stop stop-color="#732F02" offset=".7905"/>
</linearGradient>
<linearGradient id="p" x1="133.1" x2="144.87" y1="269.19" y2="229.79" gradientUnits="userSpaceOnUse">
<stop stop-color="#C5D9FB" offset="0"/>
<stop stop-color="#CEDEFA" offset=".046892"/>
<stop stop-color="#E0E8F8" offset=".1687"/>
<stop stop-color="#ECF0F6" offset=".317"/>
<stop stop-color="#F3F4F5" offset=".5176"/>
<stop stop-color="#F5F5F5" offset="1"/>
</linearGradient>
<linearGradient id="o" x1="130.09" x2="125.64" y1="246.83" y2="253.99" gradientUnits="userSpaceOnUse">
<stop stop-color="#0096FA" offset=".23876"/>
<stop stop-color="#072F73" offset="1"/>
</linearGradient>
<linearGradient id="m" x1="139.24" x2="134.79" y1="246.83" y2="253.99" gradientUnits="userSpaceOnUse">
<stop stop-color="#0096FA" offset=".23876"/>
<stop stop-color="#072F73" offset="1"/>
</linearGradient>
<linearGradient id="l" x1="148.38" x2="143.94" y1="246.83" y2="253.99" gradientUnits="userSpaceOnUse">
<stop stop-color="#0096FA" offset=".23876"/>
<stop stop-color="#072F73" offset="1"/>
</linearGradient>
<linearGradient id="k" x1="136.78" x2="140.76" y1="198.18" y2="184.84" gradientUnits="userSpaceOnUse">
<stop stop-color="#C5D9FB" offset="0"/>
<stop stop-color="#CEDEFA" offset=".046892"/>
<stop stop-color="#E0E8F8" offset=".1687"/>
<stop stop-color="#ECF0F6" offset=".317"/>
<stop stop-color="#F3F4F5" offset=".5176"/>
<stop stop-color="#F5F5F5" offset="1"/>
</linearGradient>
<linearGradient id="j" x1="141.77" x2="134.37" y1="170.7" y2="183.07" gradientUnits="userSpaceOnUse">
<stop stop-color="#03f" stop-opacity="0" offset="0"/>
<stop stop-color="#03f" stop-opacity=".5" offset="1"/>
</linearGradient>
<linearGradient id="i" x1="132.7" x2="145.83" y1="230.4" y2="186.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#C5D9FB" offset="0"/>
<stop stop-color="#CEDEFA" offset=".046892"/>
<stop stop-color="#E0E8F8" offset=".1687"/>
<stop stop-color="#ECF0F6" offset=".317"/>
<stop stop-color="#F3F4F5" offset=".5176"/>
<stop stop-color="#F5F5F5" offset="1"/>
</linearGradient>
<clipPath id="ah">
<rect transform="translate(.5)" width="432" height="301.13" fill="#fff"/>
</clipPath>
<clipPath id="ag">
<rect transform="translate(210.48 98.26)" width="5.6409" height="5.7046" fill="#fff"/>
</clipPath>
</defs>
</svg>

