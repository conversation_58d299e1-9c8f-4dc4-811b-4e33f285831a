<?xml version="1.0" encoding="UTF-8"?>
<svg width="217px" height="395px" viewBox="0 0 217 395" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 50 (54983) - http://www.bohemiancoding.com/sketch -->
    <title>Phone</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M43,14 L172,14 C190.777681,14 206,29.2223185 206,48 L206,349 C206,367.777681 190.777681,383 172,383 L43,383 C24.2223185,383 9,367.777681 9,349 L9,48 C9,29.2223185 24.2223185,14 43,14 Z" id="path-1"></path>
        <path d="M5.69923372,12.8892675 L0.120689655,6.8358887 C-0.0402298851,6.65871664 -0.0402298851,6.36342987 0.120689655,6.18625781 L5.69923372,0.132879046 C5.86015326,-0.0442930153 6.12835249,-0.0442930153 6.28927203,0.132879046 L6.87931034,0.782509938 C7.04022989,0.959681999 7.04022989,1.25496877 6.87931034,1.43214083 L2.45402299,6.18625781 C2.29310345,6.36342987 2.29310345,6.65871664 2.45402299,6.8358887 L6.8256705,11.5900057 C6.98659004,11.7671777 6.98659004,12.0624645 6.8256705,12.2396366 L6.23563218,12.8892675 C6.10153257,13.0369108 5.86015326,13.0369108 5.69923372,12.8892675 L5.69923372,12.8892675 Z" id="path-3"></path>
        <path d="M6.51340996,14.8722317 L0.137931034,7.88756388 C-0.0459770115,7.68313458 -0.0459770115,7.34241908 0.137931034,7.13798978 L6.51340996,0.153321976 C6.69731801,-0.0511073254 7.00383142,-0.0511073254 7.18773946,0.153321976 L7.86206897,0.902896082 C8.04597701,1.10732538 8.04597701,1.44804089 7.86206897,1.65247019 L2.8045977,7.13798978 C2.62068966,7.34241908 2.62068966,7.68313458 2.8045977,7.88756388 L7.80076628,13.3730835 C7.98467433,13.5775128 7.98467433,13.9182283 7.80076628,14.1226576 L7.12643678,14.8722317 C6.97318008,15.0425894 6.69731801,15.0425894 6.51340996,14.8722317 L6.51340996,14.8722317 Z" id="path-5"></path>
        <linearGradient x1="50%" y1="0%" x2="55.4183864%" y2="0%" id="linearGradient-7">
            <stop stop-color="#D4DFE5" offset="0%"></stop>
            <stop stop-color="#D8E6EE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Phone" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Group-6">
            <path d="M34,-4.26325641e-13 L183,-4.19220214e-13 C201.777681,-4.3934755e-13 217,15.2223185 217,34 L217,361 C217,379.777681 201.777681,395 183,395 L34,395 C15.2223185,395 2.29960275e-15,379.777681 0,361 L0,34 C-2.29960275e-15,15.2223185 15.2223185,-4.1577081e-13 34,-4.19220214e-13 Z" id="base" fill="#E7ECEE"></path>
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <use id="mask" fill="#FFFFFF" xlink:href="#path-1"></use>
            <path d="M56,10 L162,10 L162,10 C162,18.836556 154.836556,26 146,26 L72,26 C63.163444,26 56,18.836556 56,10 Z" id="bar" fill="#E7ECEE"></path>
        </g>
        <g id="icons/utility/chevronleft-copy" transform="translate(172.000000, 345.000000)">
            <mask id="mask-4" fill="white">
                <use xlink:href="#path-3"></use>
            </mask>
            <use id="Mask" fill="#EAF1F4" transform="translate(3.500000, 6.500000) scale(-1, 1) translate(-3.500000, -6.500000) " xlink:href="#path-3"></use>
        </g>
        <g id="icons/utility/chevronleft-copy-2" transform="translate(33.000000, 34.500000) scale(-1, 1) translate(-33.000000, -34.500000) translate(29.000000, 27.000000)">
            <mask id="mask-6" fill="white">
                <use xlink:href="#path-5"></use>
            </mask>
            <use id="Mask" fill="#EAF1F4" transform="translate(4.000000, 7.500000) scale(-1, 1) translate(-4.000000, -7.500000) " xlink:href="#path-5"></use>
        </g>
        <path d="M205.5,174.333333 L9.00793329,174.333333" id="Line-Copy-5" stroke="url(#linearGradient-7)" stroke-linecap="square"></path>
        <path d="M205.73387,278.333333 L9.00758669,278.333333" id="Line-Copy-8" stroke="url(#linearGradient-7)" stroke-linecap="square"></path>
        <path d="M205.73387,325.333333 L9.00758669,325.333333" id="Line-Copy-9" stroke="url(#linearGradient-7)" stroke-linecap="square"></path>
        <rect id="Rectangle-7" fill="#E7ECEE" opacity="0.45" x="9" y="51" width="197" height="70"></rect>
        <rect id="Rectangle-7-Copy" fill="#E7ECEE" opacity="0.45" x="2" y="216" width="209" height="22"></rect>
    </g>
</svg>