<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M36.0584 65.6619C52.5362 65.6619 65.8942 52.3039 65.8942 35.8261C65.8942 19.3483 52.5362 5.99036 36.0584 5.99036C19.5806 5.99036 6.22266 19.3483 6.22266 35.8261C6.22266 52.3039 19.5806 65.6619 36.0584 65.6619Z" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M27.1191 37.7997L30.1024 46.3232C30.1447 46.4442 30.2772 46.508 30.3982 46.4656C30.4738 46.4392 30.5304 46.3757 30.548 46.2976L31.995 39.8893L45.9261 26.4226L28.7444 35.0134L27.1191 37.7997Z" fill="url(#paint1_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.8584 46.5189L37.7997 41.7468L35.4778 38.264L31.9951 39.6571L30.5004 46.2765C30.4721 46.4015 30.5506 46.5258 30.6757 46.5541C30.7389 46.5683 30.8051 46.5556 30.8584 46.5189Z" fill="#A9C9DD"/>
<g filter="url(#filter0_f)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M27.5835 38.9607L43.3721 28.048L40.3537 27.5836L27.5835 36.6388V38.9607Z" fill="#B8CEE1"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M21.7791 33.7634L47.1164 24.2366C47.5965 24.056 48.132 24.2989 48.3126 24.779C48.3755 24.9463 48.3888 25.1282 48.3509 25.3028L43.6769 46.8435C43.5682 47.3447 43.0737 47.6629 42.5724 47.5542C42.4438 47.5263 42.3226 47.4714 42.2169 47.3931L32.2065 39.9854C32.0003 39.8328 31.9569 39.5421 32.1094 39.3359C32.1252 39.3145 32.1428 39.2946 32.1621 39.2762L42.907 29.0197C43.0925 28.8426 43.0994 28.5486 42.9223 28.3631C42.765 28.1983 42.5113 28.172 42.3235 28.301L28.0326 38.1152C27.8981 38.2076 27.725 38.2226 27.5766 38.1548L21.7198 35.4774C21.2533 35.2641 21.048 34.7131 21.2613 34.2466C21.3633 34.0234 21.5495 33.8497 21.7791 33.7634Z" fill="url(#paint2_linear)"/>
</g>
<defs>
<filter id="filter0_f" x="24.1851" y="24.1852" width="22.5854" height="18.1739" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.69922" result="effect1_foregroundBlur"/>
</filter>
<linearGradient id="paint0_linear" x1="36.0584" y1="5.99036" x2="36.0584" y2="65.6619" gradientUnits="userSpaceOnUse">
<stop stop-color="#36AEE2"/>
<stop offset="1" stop-color="#1E96C8"/>
</linearGradient>
<linearGradient id="paint1_linear" x1="34.8765" y1="30.6949" x2="36.6973" y2="34.0581" gradientUnits="userSpaceOnUse">
<stop stop-color="#C1D5E7"/>
<stop offset="1" stop-color="#C8DAEA"/>
</linearGradient>
<linearGradient id="paint2_linear" x1="48.372" y1="24.1769" x2="18.4576" y2="45.2981" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8FCFE"/>
<stop offset="1" stop-color="#E3F1F9"/>
</linearGradient>
<clipPath id="clip0">
<rect width="59.9037" height="59.9037" fill="white" transform="translate(5.99048 5.99036)"/>
</clipPath>
</defs>
</svg>
