<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Font Reference - BliP-Icons</title>
    <link href="http://fonts.googleapis.com/css?family=Dosis:400,500,700" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="styles.css">
    <style type="text/css">html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{margin:0;padding:0;border:0;outline:0;font-weight:inherit;font-style:inherit;font-size:100%;vertical-align:baseline}body{line-height:1;color:#000;background:#fff}ol,ul{list-style:none}table{border-collapse:separate;border-spacing:0;vertical-align:middle}caption,th,td{text-align:left;font-weight:normal;vertical-align:middle}a img{border:none}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}body{}.container{margin:15px auto;width:80%}h1{margin:40px 0 20px;font-weight:700;font-size:38px;line-height:32px;color:#fb565e}h2{font-size:18px;padding:0 0 21px 5px;margin:45px 0 0 0;text-transform:uppercase;font-weight:500}.small{font-size:14px;color:#a5adb4;}.small a{color:#a5adb4;}.small a:hover{color:#fb565e}.glyphs.character-mapping{margin:0 0 20px 0;padding:20px 0 20px 30px;color:rgba(0,0,0,0.5);border:1px solid #d8e0e5;-webkit-border-radius:3px;border-radius:3px;}.glyphs.character-mapping li{margin:0 30px 20px 0;display:inline-block;width:90px}.glyphs.character-mapping .icon{margin:10px 0 10px 15px;padding:15px;position:relative;width:55px;height:55px;color:#162a36 !important;overflow:hidden;-webkit-border-radius:3px;border-radius:3px;font-size:32px;}.glyphs.character-mapping .icon svg{fill:#000}.glyphs.character-mapping input{margin:0;padding:5px 0;line-height:12px;font-size:12px;display:block;width:100%;border:1px solid #d8e0e5;-webkit-border-radius:5px;border-radius:5px;text-align:center;outline:0;}.glyphs.character-mapping input:focus{border:1px solid #fbde4a;-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}.glyphs.character-mapping input:hover{-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}.glyphs.css-mapping{margin:0 0 60px 0;padding:30px 0 20px 30px;color:rgba(0,0,0,0.5);border:1px solid #d8e0e5;-webkit-border-radius:3px;border-radius:3px;}.glyphs.css-mapping li{margin:0 30px 20px 0;padding:0;display:inline-block;overflow:hidden}.glyphs.css-mapping .icon{margin:0;margin-right:10px;padding:13px;height:50px;width:50px;color:#162a36 !important;overflow:hidden;float:left;font-size:24px}.glyphs.css-mapping input{margin:0;margin-top:5px;padding:8px;line-height:16px;font-size:16px;display:block;width:150px;height:40px;border:1px solid #d8e0e5;-webkit-border-radius:5px;border-radius:5px;background:#fff;outline:0;float:right;}.glyphs.css-mapping input:focus{border:1px solid #fbde4a;-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}.glyphs.css-mapping input:hover{-webkit-box-shadow:inset 0 0 3px #fbde4a;box-shadow:inset 0 0 3px #fbde4a}</style>
  </head>
  <body>
    <div class="container">
      <h1>BliP-Icons</h1>
      <p class="small">This font was created with<a href="http://fontastic.me/">Fontastic</a></p>
      <h2>CSS mapping</h2>
      <ul class="glyphs css-mapping">
        <li>
          <div class="icon icon-favorite-off"></div>
          <input type="text" readonly="readonly" value="favorite-off">
        </li>
        <li>
          <div class="icon icon-add"></div>
          <input type="text" readonly="readonly" value="add">
        </li>
        <li>
          <div class="icon icon-arrowdown"></div>
          <input type="text" readonly="readonly" value="arrowdown">
        </li>
        <li>
          <div class="icon icon-arrowright"></div>
          <input type="text" readonly="readonly" value="arrowright">
        </li>
        <li>
          <div class="icon icon-arrowup"></div>
          <input type="text" readonly="readonly" value="arrowup">
        </li>
        <li>
          <div class="icon icon-back"></div>
          <input type="text" readonly="readonly" value="back">
        </li>
        <li>
          <div class="icon icon-barlist"></div>
          <input type="text" readonly="readonly" value="barlist">
        </li>
        <li>
          <div class="icon icon-botgroup"></div>
          <input type="text" readonly="readonly" value="botgroup">
        </li>
        <li>
          <div class="icon icon-botmessage"></div>
          <input type="text" readonly="readonly" value="botmessage">
        </li>
        <li>
          <div class="icon icon-broadcast"></div>
          <input type="text" readonly="readonly" value="broadcast">
        </li>
        <li>
          <div class="icon icon-calendar"></div>
          <input type="text" readonly="readonly" value="calendar">
        </li>
        <li>
          <div class="icon icon-clock"></div>
          <input type="text" readonly="readonly" value="clock">
        </li>
        <li>
          <div class="icon icon-close"></div>
          <input type="text" readonly="readonly" value="close">
        </li>
        <li>
          <div class="icon icon-config"></div>
          <input type="text" readonly="readonly" value="config">
        </li>
        <li>
          <div class="icon icon-configsimple"></div>
          <input type="text" readonly="readonly" value="configsimple">
        </li>
        <li>
          <div class="icon icon-connect"></div>
          <input type="text" readonly="readonly" value="connect">
        </li>
        <li>
          <div class="icon icon-conversation"></div>
          <input type="text" readonly="readonly" value="conversation">
        </li>
        <li>
          <div class="icon icon-copy"></div>
          <input type="text" readonly="readonly" value="copy">
        </li>
        <li>
          <div class="icon icon-delete"></div>
          <input type="text" readonly="readonly" value="delete">
        </li>
        <li>
          <div class="icon icon-download"></div>
          <input type="text" readonly="readonly" value="download">
        </li>
        <li>
          <div class="icon icon-edit"></div>
          <input type="text" readonly="readonly" value="edit">
        </li>
        <li>
          <div class="icon icon-external-link"></div>
          <input type="text" readonly="readonly" value="external-link">
        </li>
        <li>
          <div class="icon icon-favorite-on"></div>
          <input type="text" readonly="readonly" value="favorite-on">
        </li>
        <li>
          <div class="icon icon-grow"></div>
          <input type="text" readonly="readonly" value="grow">
        </li>
        <li>
          <div class="icon icon-info"></div>
          <input type="text" readonly="readonly" value="info">
        </li>
        <li>
          <div class="icon icon-integration"></div>
          <input type="text" readonly="readonly" value="integration">
        </li>
        <li>
          <div class="icon icon-lab"></div>
          <input type="text" readonly="readonly" value="lab">
        </li>
        <li>
          <div class="icon icon-line"></div>
          <input type="text" readonly="readonly" value="line">
        </li>
        <li>
          <div class="icon icon-list"></div>
          <input type="text" readonly="readonly" value="list">
        </li>
        <li>
          <div class="icon icon-messagreaded"></div>
          <input type="text" readonly="readonly" value="messagreaded">
        </li>
        <li>
          <div class="icon icon-messagreceived"></div>
          <input type="text" readonly="readonly" value="messagreceived">
        </li>
        <li>
          <div class="icon icon-messagsend"></div>
          <input type="text" readonly="readonly" value="messagsend">
        </li>
        <li>
          <div class="icon icon-pie"></div>
          <input type="text" readonly="readonly" value="pie">
        </li>
        <li>
          <div class="icon icon-refresh"></div>
          <input type="text" readonly="readonly" value="refresh">
        </li>
        <li>
          <div class="icon icon-router"></div>
          <input type="text" readonly="readonly" value="router">
        </li>
        <li>
          <div class="icon icon-search"></div>
          <input type="text" readonly="readonly" value="search">
        </li>
        <li>
          <div class="icon icon-selecton"></div>
          <input type="text" readonly="readonly" value="selecton">
        </li>
        <li>
          <div class="icon icon-send"></div>
          <input type="text" readonly="readonly" value="send">
        </li>
        <li>
          <div class="icon icon-talk"></div>
          <input type="text" readonly="readonly" value="talk">
        </li>
        <li>
          <div class="icon icon-toasttrue"></div>
          <input type="text" readonly="readonly" value="toasttrue">
        </li>
        <li>
          <div class="icon icon-toastwarning"></div>
          <input type="text" readonly="readonly" value="toastwarning">
        </li>
        <li>
          <div class="icon icon-true"></div>
          <input type="text" readonly="readonly" value="true">
        </li>
        <li>
          <div class="icon icon-favorite-off-3"></div>
          <input type="text" readonly="readonly" value="favorite-off-3">
        </li>
        <li>
          <div class="icon icon-selectoff-1"></div>
          <input type="text" readonly="readonly" value="selectoff-1">
        </li>
        <li>
          <div class="icon icon-bullet-down"></div>
          <input type="text" readonly="readonly" value="bullet-down">
        </li>
        <li>
          <div class="icon icon-menumore"></div>
          <input type="text" readonly="readonly" value="menumore">
        </li>
        <li>
          <div class="icon icon-morevertical"></div>
          <input type="text" readonly="readonly" value="morevertical">
        </li>
        <li>
          <div class="icon icon-team-1"></div>
          <input type="text" readonly="readonly" value="team-1">
        </li>
        <li>
          <div class="icon icon-false"></div>
          <input type="text" readonly="readonly" value="false">
        </li>
        <li>
          <div class="icon icon-info-1"></div>
          <input type="text" readonly="readonly" value="info-1">
        </li>
        <li>
          <div class="icon icon-true-1"></div>
          <input type="text" readonly="readonly" value="true-1">
        </li>
        <li>
          <div class="icon icon-warning"></div>
          <input type="text" readonly="readonly" value="warning">
        </li>
        <li>
          <div class="icon icon-bar-copy"></div>
          <input type="text" readonly="readonly" value="bar-copy">
        </li>
        <li>
          <div class="icon icon-layer-copy"></div>
          <input type="text" readonly="readonly" value="layer-copy">
        </li>
        <li>
          <div class="icon icon-attach"></div>
          <input type="text" readonly="readonly" value="attach">
        </li>
        <li>
          <div class="icon icon-arrowleft"></div>
          <input type="text" readonly="readonly" value="arrowleft">
        </li>
        <li>
          <div class="icon icon-doton"></div>
          <input type="text" readonly="readonly" value="doton">
        </li>
        <li>
          <div class="icon icon-right-arrow"></div>
          <input type="text" readonly="readonly" value="right-arrow">
        </li>
        <li>
          <div class="icon icon-dotoff"></div>
          <input type="text" readonly="readonly" value="dotoff">
        </li>
        <li>
          <div class="icon icon-config-1"></div>
          <input type="text" readonly="readonly" value="config-1">
        </li>
        <li>
          <div class="icon icon-publish"></div>
          <input type="text" readonly="readonly" value="publish">
        </li>
      </ul>
      <h2>Character mapping</h2>
      <ul class="glyphs character-mapping">
        <li>
          <div data-icon="a" class="icon"></div>
          <input type="text" readonly="readonly" value="a">
        </li>
        <li>
          <div data-icon="c" class="icon"></div>
          <input type="text" readonly="readonly" value="c">
        </li>
        <li>
          <div data-icon="d" class="icon"></div>
          <input type="text" readonly="readonly" value="d">
        </li>
        <li>
          <div data-icon="f" class="icon"></div>
          <input type="text" readonly="readonly" value="f">
        </li>
        <li>
          <div data-icon="g" class="icon"></div>
          <input type="text" readonly="readonly" value="g">
        </li>
        <li>
          <div data-icon="i" class="icon"></div>
          <input type="text" readonly="readonly" value="i">
        </li>
        <li>
          <div data-icon="j" class="icon"></div>
          <input type="text" readonly="readonly" value="j">
        </li>
        <li>
          <div data-icon="k" class="icon"></div>
          <input type="text" readonly="readonly" value="k">
        </li>
        <li>
          <div data-icon="l" class="icon"></div>
          <input type="text" readonly="readonly" value="l">
        </li>
        <li>
          <div data-icon="m" class="icon"></div>
          <input type="text" readonly="readonly" value="m">
        </li>
        <li>
          <div data-icon="n" class="icon"></div>
          <input type="text" readonly="readonly" value="n">
        </li>
        <li>
          <div data-icon="o" class="icon"></div>
          <input type="text" readonly="readonly" value="o">
        </li>
        <li>
          <div data-icon="-" class="icon"></div>
          <input type="text" readonly="readonly" value="-">
        </li>
        <li>
          <div data-icon="q" class="icon"></div>
          <input type="text" readonly="readonly" value="q">
        </li>
        <li>
          <div data-icon="r" class="icon"></div>
          <input type="text" readonly="readonly" value="r">
        </li>
        <li>
          <div data-icon="s" class="icon"></div>
          <input type="text" readonly="readonly" value="s">
        </li>
        <li>
          <div data-icon="t" class="icon"></div>
          <input type="text" readonly="readonly" value="t">
        </li>
        <li>
          <div data-icon="u" class="icon"></div>
          <input type="text" readonly="readonly" value="u">
        </li>
        <li>
          <div data-icon="v" class="icon"></div>
          <input type="text" readonly="readonly" value="v">
        </li>
        <li>
          <div data-icon="y" class="icon"></div>
          <input type="text" readonly="readonly" value="y">
        </li>
        <li>
          <div data-icon="z" class="icon"></div>
          <input type="text" readonly="readonly" value="z">
        </li>
        <li>
          <div data-icon="A" class="icon"></div>
          <input type="text" readonly="readonly" value="A">
        </li>
        <li>
          <div data-icon="D" class="icon"></div>
          <input type="text" readonly="readonly" value="D">
        </li>
        <li>
          <div data-icon="E" class="icon"></div>
          <input type="text" readonly="readonly" value="E">
        </li>
        <li>
          <div data-icon="F" class="icon"></div>
          <input type="text" readonly="readonly" value="F">
        </li>
        <li>
          <div data-icon="G" class="icon"></div>
          <input type="text" readonly="readonly" value="G">
        </li>
        <li>
          <div data-icon="H" class="icon"></div>
          <input type="text" readonly="readonly" value="H">
        </li>
        <li>
          <div data-icon="J" class="icon"></div>
          <input type="text" readonly="readonly" value="J">
        </li>
        <li>
          <div data-icon="K" class="icon"></div>
          <input type="text" readonly="readonly" value="K">
        </li>
        <li>
          <div data-icon="L" class="icon"></div>
          <input type="text" readonly="readonly" value="L">
        </li>
        <li>
          <div data-icon="M" class="icon"></div>
          <input type="text" readonly="readonly" value="M">
        </li>
        <li>
          <div data-icon="N" class="icon"></div>
          <input type="text" readonly="readonly" value="N">
        </li>
        <li>
          <div data-icon="O" class="icon"></div>
          <input type="text" readonly="readonly" value="O">
        </li>
        <li>
          <div data-icon="P" class="icon"></div>
          <input type="text" readonly="readonly" value="P">
        </li>
        <li>
          <div data-icon="Q" class="icon"></div>
          <input type="text" readonly="readonly" value="Q">
        </li>
        <li>
          <div data-icon="R" class="icon"></div>
          <input type="text" readonly="readonly" value="R">
        </li>
        <li>
          <div data-icon="T" class="icon"></div>
          <input type="text" readonly="readonly" value="T">
        </li>
        <li>
          <div data-icon="U" class="icon"></div>
          <input type="text" readonly="readonly" value="U">
        </li>
        <li>
          <div data-icon="V" class="icon"></div>
          <input type="text" readonly="readonly" value="V">
        </li>
        <li>
          <div data-icon="Z" class="icon"></div>
          <input type="text" readonly="readonly" value="Z">
        </li>
        <li>
          <div data-icon="0" class="icon"></div>
          <input type="text" readonly="readonly" value="0">
        </li>
        <li>
          <div data-icon="1" class="icon"></div>
          <input type="text" readonly="readonly" value="1">
        </li>
        <li>
          <div data-icon="3" class="icon"></div>
          <input type="text" readonly="readonly" value="3">
        </li>
        <li>
          <div data-icon="4" class="icon"></div>
          <input type="text" readonly="readonly" value="4">
        </li>
        <li>
          <div data-icon="b" class="icon"></div>
          <input type="text" readonly="readonly" value="b">
        </li>
        <li>
          <div data-icon="w" class="icon"></div>
          <input type="text" readonly="readonly" value="w">
        </li>
        <li>
          <div data-icon="B" class="icon"></div>
          <input type="text" readonly="readonly" value="B">
        </li>
        <li>
          <div data-icon="C" class="icon"></div>
          <input type="text" readonly="readonly" value="C">
        </li>
        <li>
          <div data-icon="S" class="icon"></div>
          <input type="text" readonly="readonly" value="S">
        </li>
        <li>
          <div data-icon="W" class="icon"></div>
          <input type="text" readonly="readonly" value="W">
        </li>
        <li>
          <div data-icon="5" class="icon"></div>
          <input type="text" readonly="readonly" value="5">
        </li>
        <li>
          <div data-icon="6" class="icon"></div>
          <input type="text" readonly="readonly" value="6">
        </li>
        <li>
          <div data-icon="7" class="icon"></div>
          <input type="text" readonly="readonly" value="7">
        </li>
        <li>
          <div data-icon="8" class="icon"></div>
          <input type="text" readonly="readonly" value="8">
        </li>
        <li>
          <div data-icon="h" class="icon"></div>
          <input type="text" readonly="readonly" value="h">
        </li>
        <li>
          <div data-icon="e" class="icon"></div>
          <input type="text" readonly="readonly" value="e">
        </li>
        <li>
          <div data-icon="x" class="icon"></div>
          <input type="text" readonly="readonly" value="x">
        </li>
        <li>
          <div data-icon="I" class="icon"></div>
          <input type="text" readonly="readonly" value="I">
        </li>
        <li>
          <div data-icon="X" class="icon"></div>
          <input type="text" readonly="readonly" value="X">
        </li>
        <li>
          <div data-icon="Y" class="icon"></div>
          <input type="text" readonly="readonly" value="Y">
        </li>
        <li>
          <div data-icon="2" class="icon"></div>
          <input type="text" readonly="readonly" value="2">
        </li>
      </ul>
    </div>
    <script>(function() {
  var glyphs, i, len, ref;

  ref = document.getElementsByClassName('glyphs');
  for (i = 0, len = ref.length; i < len; i++) {
    glyphs = ref[i];
    glyphs.addEventListener('click', function(event) {
      if (event.target.tagName === 'INPUT') {
        return event.target.select();
      }
    });
  }

}).call(this);

    </script>
  </body>
</html>
