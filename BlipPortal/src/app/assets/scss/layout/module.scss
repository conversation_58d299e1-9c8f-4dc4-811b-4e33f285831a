@import '../main';
@import 'header';

$loading-transition-duration: 0.12s;

* {
    margin: 0;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    position: relative;
    margin: 0;
    padding: 0;
}

body {
    background-color: $color-surface-2;
    background-attachment: fixed;
}

#wrapper {
    position: relative;
    min-height: 100%;
    height: auto;
}

.wrapper {
    height: 100%;
}

.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: $zindex-loading;
    background-color: rgba(46, 53, 66, 0.9);
    @include transition(visibility $loading-transition-duration ease-in-out);
    @include transition(opacity $loading-transition-duration ease-in-out);

    bds-illustration {
        width: 90px;
    }
}

.main-section {
    @include flex-box;
    flex: 1;

    ui-view {
        @include flex-box;
        width: 100%;
        align-items: stretch;
        min-height: 100% !important;
        position: relative;
    }
}

.detail-aside {
    width: auto;
    background-image: linear-gradient(180deg, #ffffff 0%, #f2fcff 100%);
    background-attachment: fixed;
    color: $bp-color-desk;

    li {
        width: 100%;
        min-height: 104px;
    }
}

.main-detail-content {
    background-color: $color-surface-2 !important;
    background-attachment: fixed;
    box-shadow: -2px 6px 6px 0 rgba(226, 231, 236, 0.96);
    @include flex-box;
    flex-direction: column;
    overflow: hidden;
}

.full-screen-container {
    width: 100%;
    min-height: 100%;
    height: auto;
    position: relative;
    z-index: $zindex-page;
}

.blip-ui-content {
    height: calc(100% - #{$navbar-height} + #{$subheader-height});
}

.config-visible {
    bottom: 0;
    left: 0;
    width: 100%;
    height: 90px;
    position: sticky;
}

section {
    padding: 3.6 * $m 0 3.6 * $m;
}

article {
    padding: 2.4 * $m 0.9 * $m;
    @include desktop {
        padding: 2.4 * $m;
    }
}

.container {
    .color-content-default {
        color: $color-content-default;
    }
    .color-content-disable {
        color: $color-content-disable;
    }
    .color-content-ghost {
        color: $color-content-ghost;
    }
}

.bg-color-surface-1 {
    background-color: $color-surface-1;
}
.bg-color-surface-2 {
    background-color: $color-surface-2;
}
.bg-color-surface-3 {
    background-color: $color-surface-3;
}
.bg-color-surface-4 {
    background-color: $color-surface-4;
}

.card--mini-card {
    background-color: $color-surface-1;
}

.container-fluid {
    width: 100%;
    display: table;
}

.container-small {
    max-width: 685px;
}

.container-medium {
    max-width: 920px;
}

.container-sidenav {
    padding-left: 48px;
    padding-right: 12px;
}

.container-tiny {
    max-width: 480px;
}

.box-small {
    width: 310px;
}

.row {
    margin-top: 0.3 * $m;
    margin-bottom: 0.9 * $m;
    &.row-compact {
        margin: 0;
    }
}

.full-initial-section {
    h1 {
        color: $color-content-default;
    }
}

.one-half.column {
    margin-left: 4%;
    width: 48%;
    &:first-child {
        margin-left: 0;
    }
    @include mobile {
        &.no-mobile {
            margin-left: 0;
            width: 100%;
        }
    }
}

.row-grid {
    .clear-after + .column,
    .clear-after + .columns {
        margin-left: 0;
    }
}

@include desktop {
    .offset-by-one-half.column,
    .offset-by-one-half.columns {
        margin-left: 52% !important;
    }
}

.bottom {
    position: absolute;
    bottom: 0;
}

.block-center {
    margin-left: auto;
    margin-right: auto;
}

.margin-left-auto {
    margin-left: auto;
}

br.separator {
    line-height: 2.4 * $m;
    height: 2.4 * $m;
    clear: both;
}

@include desktop {
    br.separator {
        line-height: 3.6 * $m;
        height: 3.6 * $m;
    }
}

.clear-after {
    @include clear-after;
}

/* Skeleton */
.mobile-grid {
    .column,
    .columns {
        margin-left: 4%;
    }
    .column:first-child,
    .columns:first-child {
        margin-left: 0;
    }

    .one.column,
    .one.columns {
        width: 4.66666666667%;
    }
    .two.columns {
        width: 13.3333333333%;
    }
    .three.columns {
        width: 22%;
    }
    .four.columns {
        width: 30.6666666667%;
    }
    .five.columns {
        width: 39.3333333333%;
    }
    .six.columns {
        width: 48%;
    }
    .seven.columns {
        width: 56.6666666667%;
    }
    .eight.columns {
        width: 65.3333333333%;
    }
    .nine.columns {
        width: 74%;
    }
    .ten.columns {
        width: 82.6666666667%;
    }
    .eleven.columns {
        width: 91.3333333333%;
    }
    .twelve.columns {
        width: 100%;
        margin-left: 0;
    }

    .one-half.column {
        width: 48%;
    }

    /* Offsets */
    .offset-by-one.column,
    .offset-by-one.columns {
        margin-left: 8.66666666667%;
    }
    .offset-by-two.column,
    .offset-by-two.columns {
        margin-left: 17.3333333333%;
    }
    .offset-by-three.column,
    .offset-by-three.columns {
        margin-left: 26%;
    }
    .offset-by-four.column,
    .offset-by-four.columns {
        margin-left: 34.6666666667%;
    }
    .offset-by-five.column,
    .offset-by-five.columns {
        margin-left: 43.3333333333%;
    }
    .offset-by-six.column,
    .offset-by-six.columns {
        margin-left: 52%;
    }
    .offset-by-seven.column,
    .offset-by-seven.columns {
        margin-left: 60.6666666667%;
    }
    .offset-by-eight.column,
    .offset-by-eight.columns {
        margin-left: 69.3333333333%;
    }
    .offset-by-nine.column,
    .offset-by-nine.columns {
        margin-left: 78%;
    }
    .offset-by-ten.column,
    .offset-by-ten.columns {
        margin-left: 86.6666666667%;
    }
    .offset-by-eleven.column,
    .offset-by-eleven.columns {
        margin-left: 95.3333333333%;
    }

    .offset-by-one-third.column,
    .offset-by-one-third.columns {
        margin-left: 34.6666666667%;
    }
    .offset-by-two-thirds.column,
    .offset-by-two-thirds.columns {
        margin-left: 69.3333333333%;
    }

    .offset-by-one-half.column,
    .offset-by-one-half.columns {
        margin-left: 52%;
    }
}
