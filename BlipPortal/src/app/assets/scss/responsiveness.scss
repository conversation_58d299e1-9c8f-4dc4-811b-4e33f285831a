@import "./variables";

// Breakpoints
$small-width: 102.4; // 1024px
$xxlarge-width: 256; // 2560px

$small-height: 76.8; // 768px
$xxlarge-height: 144; // 1440px

/*
* Returns a REM-unit calculation that is proportional to the viewport width
* $minSizeWithoutUnit: size in $m but without the unit. So if it is 10px (1 * $m), calculateResponsiveSize(1).
*/
@function calculateResponsiveSize($minSizeWithoutUnit) {
    @return calc(#{$minSizeWithoutUnit} * #{$m} +
        ((#{$minSizeWithoutUnit} * ((120vw - #{$small-width} * #{$m}) / (#{$xxlarge-width} - #{$small-width}))) +
        (#{$minSizeWithoutUnit} * ((100vh - #{$small-height} * #{$m}) / (#{$xxlarge-height} - #{$small-height})))) / 2);
}
