@mixin scrollbar($color) {
    &::-webkit-scrollbar {
        width: 1 * $u;
        height: 1 * $u;
    }

    &::-webkit-scrollbar-track {
        background: none;
    }

    &::-webkit-scrollbar-thumb {
        background-color: $color;
    }

    scrollbar-color: $color none;
    scrollbar-width: thin;
}

@mixin placeholder($color) {
    &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: $color;
    }

    &::-moz-placeholder { /* Firefox 19+ */
        color: $color;
    }

    &:-ms-input-placeholder { /* IE 10+ */
        color: $color;
    }

    &:-moz-placeholder { /* Firefox 18- */
        color: $color;
    }
}

@mixin blip-icon() {
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    speak: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
