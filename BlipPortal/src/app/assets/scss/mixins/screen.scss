@mixin desktop-x-large {
    @media (min-width: 1400px) {
        @content;
    }
}

@mixin desktop-large {
    @media (min-width: 1075px) {
        @content;
    }
}

@mixin landscape {
    @media (min-width: 1023px) {
        @content;
    }
}

@mixin desktop {
    @media (min-width: 750px) {
        @content;
    }
}

@mixin tablet {
    @media (min-width: 550px) {
        @content;
    }
}

@mixin phone {
    @media (max-width: 549px) {
        @content;
    }
}

@mixin mobile {
    @media (max-width: 749px) {
        @content;
    }
}

$breakpoints: (
    "phone":          400px,
    "phone-wide":     480px,
    "phablet":        560px,
    "tablet-small":   640px,
    "tablet":         768px,
    "tablet-wide":    1024px,
    "desktop":        1248px,
    "notebook-small": 1280px,
    "notebook":       1366px,
    "desktop-wide":   1440px,
    "desktop-wide-x": 1920px
);

@mixin media-queries($width, $type: min) {
    @if map_has_key($breakpoints, $width) {
        $width: map_get($breakpoints, $width);
        @if $type == max {
            $width: $width - 1px;
        }
        @media only screen and (#{$type}-width: $width) {
            @content;
        }
    }
}
