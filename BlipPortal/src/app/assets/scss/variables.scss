@import '~blip-toolkit/dist/scss/variables/module';
@import '~blip-ds/dist/collection/styles/colors';

//Variable for scale that permits the use of base 16px with the logic of 10px
//For example, if you want 18px, instead of writing 1.125rem, you can write 1.8*$m, which is easier to understand
$m: 0.625rem;

$u: 0.6*$m;

$footer-height: 57px;
$footer-height-desktop: 50px;
$monospace-font-family: 'Consolas', monospace;

$navbar-height: 80px;
$subheader-height: 56px;
$navbar-collapsed-height: 44px;

$application-list-container-width-small: 830px;
$application-list-container-width-large: 1300px;
$application-list-container-width-medium: 1120px;

$size-xxs: 1.333*$u;
$size-xss: 2*$u;
$size-xs: 2.666*$u;
$size-ss: 3.333*$u;
$size-s: 4*$u;
$size-sm: 6*$u;
$size-m: 8*$u;
$size-l: 12*$u;

//Misc
$color-success-2: #D7FFE6;
$color-info: #73D0FF;
$color-info-light: #d0feff;
$bp-color-warning-light: #fee2df;
$color-warning-light: #FFFCDD;
$color-warning-dark: #D2A51A;
$color-disabled: #BEBEBE;
$color-accent: #B2DFDB;
$color-white-dark: #ECEFF0;
$color-wa: #50C079;

//Gray
$color-gray: $color-disabled;
$color-gray-light: #D3D3D3;
$color-gray-light-2: #E9EDF3;
$color-gray-light-4: #e3f1e9;
$color-gray-light-5: #919c9e;
$color-gray-dark: #999999;

// Color pallete: Light BLiP
$color-blip-light-2: #e3f5f6;

// Color pallete: Dark BLiP
$color-text-dark: #4A4A4A;
$color-text-dark-5: #334962;
$color-gray-dark-2: #363F4E;
$color-gray-dark-3: #222834;
$color-gray-dark-4: #475D72;
$color-text-disabled: #c5c5c5;
$color-input-disabled: #EAECEC;

//Login
$color-blip-blue-1: #1BD6E7;
$color-blip-blue-2: #0FC3F8;
$color-blip-blue-3: #0ABCFF;

//3rd-party
$color-facebook: #3C5A99;
$color-facebook-dark: darken($color-facebook, 9%);
$border-color-1: #d3d3d3;

//extended
$extended-orange-light: #FCAA73;
$extended-pink-dark: #FB4BC1;
$extended-red-light: #F99F9F;
$extended-red-dark: #E60F0F