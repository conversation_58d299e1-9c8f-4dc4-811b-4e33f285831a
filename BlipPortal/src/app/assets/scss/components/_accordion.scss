.accordion {
    padding: 0.6*$m 0;
    margin: 0.9*$m 0;

    border-bottom: 1px $color-disabled dotted;

    .accordion-title {
        margin-bottom: 1.5*$m;
        @include transition(margin-bottom 0.3s ease-in-out);
    }

    .accordion-chevron {
        margin: 0.6*$m 0.3*$m 0;
        content: "";
        width: 8px;
        height: 8px;
        border-right: 3px $bp-color-city solid;
        border-bottom: 3px $bp-color-city solid;

        @include transition(transform 0.3s ease-in-out);

        transform: rotate(-135deg);
    }

    .accordion-content {
        z-index: 1;
        max-height: 100%;

        @include transition(
            max-height 0.3s ease-in-out,
            opacity 0.3s ease-in-out
            );
    }

    > input[type="checkbox"] {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        opacity: 0;

        &:checked {
            ~ .accordion-title {
                margin-bottom: 0;
            }
            ~ .accordion-content {
                max-height: 0;
                padding-top: 0;
                opacity: 0;
            }
            ~ .accordion-chevron { transform: rotate(45deg); }
        }
    }

    > input[type="radio"] {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        margin-left: 15px;
        z-index: 1;
        opacity: 1;        
    }
}
