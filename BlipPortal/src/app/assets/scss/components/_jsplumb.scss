@import "~assets/scss/main";

#diagramContainer {
    width: 500%;
    height: 500%;
    position: relative;
}

.filler {
    position: relative;
    width: 200%;
    height: 300%;
}

.canvas {
    overflow: hidden;
    width: 100%;
    height: calc(100vh - 140px);

    &.hide-conns {
        svg {
            display: none;
        }
    }
}

.fullscreen .canvas {
    height: 100vh;
}

.item2 {
    position: absolute;
    height: 80px;
    width: 80px;
    top: 200px;
}

.diagram-node {
    word-break: break-word;
    position: absolute;
    width: 175px;
    text-align: center;
    z-index: 4;
    border-radius: 3px;
    -moz-border-radius: 3px;
    box-shadow: 0 2px 12px 0 $color-surface-1;
    -o-box-shadow: 0 2px 12px 0 $color-surface-1;
    -webkit-box-shadow: 0 2px 12px 0 $color-surface-1;
    -moz-box-shadow: 0 2px 12px 0 $color-surface-1;
    cursor: move !important;
    background-color: $color-surface-1;
    color: $color-content-default;
    -webkit-transition: -webkit-box-shadow 0.25s ease-in;
    -moz-transition: -moz-box-shadow 0.25s ease-in;
    transition: box-shadow 0.25s ease-in;

    .builder-node-title {
        font-size: 14px;
        font-weight: 400;
    }

    &.subflow {
        &-block {
            width: 225px;
            border-radius: 6px;
        }
    }

    i {
        cursor: pointer !important;
        visibility: hidden;
    }

    .menumore-opt {
        position: absolute;
        top: 4px;
        left: 11px;

        span {
            cursor: pointer !important;
        }
    }

    .builder-node-icon {
        position: absolute;
        bottom: 8px;
        right: 8px;
    }

    &:hover {
        z-index: 5;
        box-shadow: 0px 0px 0px 4px $color-brand;
        .diagram-node-endpoint {
            visibility: visible;
            opacity: 1;
        }

        i {
            visibility: visible;
        }

        &.invalid-node {
            box-shadow: 0px 0px 0px 4px $color-delete;
        }
    }

    &.on-search {
        .diagram-node-endpoint {
            visibility: hidden;
            opacity: 0;
        }
    }

    &.no-match-node {
        opacity: 0.2;
    }

    &.selected-node {
        box-shadow: 0px 0px 0px 4px $color-brand;
        z-index: 5;

        &::before {
            content: "";
            background: $color-surface-3;
            opacity: 0.5;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .builder-node-title {
            font-weight: 700;
        }

        &:not(&.default-node),
        &.invalid-node {
            &::before {
                content: "";
                background: $color-surface-3;
                opacity: 0.5;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        .diagram-node-endpoint {
            visibility: visible;
            opacity: 1;
        }

        &.invalid-node {
            box-shadow: 0px 0px 0px 4px $color-delete;
        }

        &:not(&.invalid-node, &.default-node) {
            background-color: $color-primary;
        }
    }

    &.invalid-node {
        background: $color-error;

        .diagram-node-endpoint {
            background-color: $color-delete;
        }
    }

    &.upgrade-node {
        background: $color-warning;
        color: $color-content-din;

        .diagram-node-endpoint {
            background-color: $color-primary-yellows-guarana;
        }
    }

    &.editing-node {
        box-shadow: 0px 0px 0px 4px $color-primary-main;
        z-index: 5;

        &:not(&.default-node),
        &.invalid-node {
            &::before {
                content: '';
                background: $color-surface-3;
                opacity: 0.5;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        &:not(&.invalid-node, &.default-node) {
            background-color: $color-primary;
        }

        .builder-node-title {
            font-weight: 700;
        }

        i {
            color: $color-primary-main;
            visibility: visible;
        }

        .diagram-node-endpoint {
            visibility: visible;
            opacity: 1;
        }

        &.upgrade-node {
            color: $color-content-din;
        }

        &.invalid-node {
            box-shadow: 0px 0px 0px 4px $color-delete;
        }
    }
}

.diagram-node-endpoint {
    position: absolute;
    bottom: -0.6em;
    left: 50%;
    transform: translateX(-50%);
    width: 1em;
    height: 1em;
    border-radius: 50%;
    background-color: $color-brand;
    cursor: pointer !important;
    -webkit-transition: background-color 0.25s ease-in;
    -moz-transition: background-color 0.25s ease-in;
    transition: background-color 0.25s ease-in;
    display: block;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.3s, opacity 0.3s linear;
}

.default-node {
    background: $color-system;
}
