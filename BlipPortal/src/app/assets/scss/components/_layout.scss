.layout-top {
    position: absolute;
    top: 0;
}
.layout-bottom {
    position: absolute;
    bottom: 0;
}
.layout-left {
    position: absolute;
    left: 0;
}
.layout-right {
    position: absolute;
    right: 0;
}
.layout-center-x {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    -moz-transform: translate(-50%,0%);
    -webkit-transform: translate(-50%,0%);
    -o-transform: translate(-50%,0%);
}
.layout-center-y {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    -moz-transform: translate(0%,-50%);
    -webkit-transform: translate(0%,-50%);
    -o-transform: translate(0%,-50%);
}

.layout-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
}
