input,
textarea,
select {
    margin-bottom: 0;
}

input[type='email'],
input[type='number'],
input[type='search'],
input[type='text'],
input[type='tel'],
input[type='url'],
input[type='password'],
textarea,
select {
    background: none;
    border: none;
    border-bottom: 1px $color-gray solid;
    border-radius: 0;
    padding: 0;
    margin: 0;
    height: 3.6 * $m;

    &:focus {
        border: none;
        border-bottom: 2px $bp-color-bot solid;
    }

    &.u-disable {
        background-color: $color-gray-light;
        border-color: $color-gray-light;
        padding: 0 0.9 * $m;
        cursor: default;
        pointer-events: none;
    }

    &[disabled],
    &[disabled='disabled'],
    &[readonly]:not(.bp-daterange-start-date):not(.bp-daterange-end-date),
    &[readonly='readonly']:not(.bp-daterange-start-date):not(.bp-daterange-end-date) {
        background-color: transparent;
        border-color: $color-gray-light;
        color: darkgray;
        padding: 0 0.9 * $m;
    }

    &.ng-invalid.ng-dirty {
        border-color: $color-delete;
    }
}

.input-file {
    background-color: $color-white-dark;
    border: 1px $color-disabled dashed;
    border-radius: 0;
    color: $bp-color-city;
    text-align: center;
    overflow: hidden;
    > label {
        line-height: 6.8 * $m;
        margin: 0;
        pointer-events: none;
        button {
            pointer-events: all;
        }
    }
    > input[type='file'].has-file-reader {
        position: absolute;
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        filter: alpha(opacity=0);
        overflow: hidden;
        z-index: -1;
    }
    > img {
        margin-top: 0.6 * $m;
        max-width: 100%;
    }

    &.drag-over {
        background-color: darken($color-gray-light, 6%);
        color: $color-text-dark;
        button {
            display: none;
        }
    }
}

textarea {
    background: none;
    border: none;
    border-bottom: 1px $color-gray solid;
    border-radius: 0;
    padding: 0;
    margin: 0;
    height: auto;
    min-height: 3.1 * $m;
    overflow: auto;
    resize: none;
    @include scrollbar($color-gray-light);

    &:focus {
        border-color: $bp-color-bot;
    }

    &.textarea-code,
    &.textarea-code:focus {
        font-family: $monospace-font-family;
        border: 1px $color-white-dark solid;
        border-radius: 2px;
        background-color: $bp-gradient-shine;
        padding: 0.5 * $u 1 * $u;
        width: 100%;

        &.error {
            border-color: $color-delete;
        }
    }
}

.label-required::after {
    content: ' *';
}

.form-group {
    padding-bottom: 3 * $m;
    vertical-align: middle;
}
.form-group-small {
    padding-bottom: 1.5 * $m;
    vertical-align: middle;
}

.input-group {
    position: relative;
    .input-left {
        position: absolute;
        bottom: 0;
        left: 0;

        width: 3.6 * $m;
        height: 3.6 * $m;

        padding: 0;
        margin: 0;
        line-height: 3.6 * $m;
        text-align: center;

        border: 0;
        border-radius: 0;
    }
    .input-left + input,
    .input-left + textarea,
    input.input-to-right,
    textarea.input-to-right {
        padding-left: 4.2 * $m;
    }

    .input-right {
        position: absolute;
        bottom: 0;
        right: 0;

        width: auto;
        height: 3.6 * $m;

        padding: 0 0.9 * $m;
        margin: 0;
        line-height: 1.4 * $m;
        text-align: center;

        border: 0;
        border-radius: 0;
    }
    .input-right + input,
    .input-right + textarea,
    input.input-to-left,
    textarea.input-to-left {
        padding-right: 4.2 * $m;
    }
}

.input-with-search {
    .form-group {
        margin: 30px 0;
        padding-bottom: 0;

        > div {
            @include flex-box;
            align-items: flex-end;
        }
    }
}

.labeled-input {
    background: $color-surface-1;
    border-radius: 3px;
    border: 1px solid $color-surface-2;
    padding: 7px 15px;

    label {
        color: $color-content-default;
        font-size: 1.2 * $m;
        line-height: 1;
        margin-bottom: 5px;
        font-weight: 300;
    }

    input {
        border: 0;
        line-height: 1 * $m;
        height: 1.6 * $m;
        display: inline-block;
        width: 100%;

        &:focus {
            border: 0;
        }
    }
}

.labeled-input--error {
    border: 1px solid $color-delete;

    label {
        color: $color-delete;
    }
}
