.markdown {
    pre,
    code {
        padding: .2*$m .5*$m;
        background: $bp-color-white;
        border: 1px solid $color-white-dark;
        border-radius: 0;

        > .pl-k {
            color: $bp-color-city;
            font-weight: $bp-fw-extra-bold;
        }
        > .pl-s {
            color: $bp-color-bot;
        }
        > .pl-en {
            color: $color-info;
        }
        > .pl-smi {
            color: $bp-color-rooftop;
        }
        > .pl-c {
            font-style: italic;
            color: $bp-color-rooftop;
        }
    }

    pre {
        background: $bp-color-white;
        > code {
            border: none;
            background: none;
            display: block;
            padding: 1*$m 1.5*$m;
            white-space: pre;
        }
    }

    code {
        margin: 0 .2*$m;
        font-size: 90%;
        white-space: nowrap;
    }

    table {
        overflow-x: auto;
        display: block;
    }
}
