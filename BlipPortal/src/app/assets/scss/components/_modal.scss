@import '~blip-ds/dist/collection/styles/_colors.scss';

$modal-transition-duration: 0.50s;

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: $zindex-modal-overlay;
    @include transition(visibility $modal-transition-duration ease-in-out);

    > .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        @include transition(opacity $modal-transition-duration ease-in-out);
    }

    > .modal-dialog {
        position: relative;
        border-radius: 3px;
        padding: 3.0*$m;
        margin-left: auto;
        margin-right: auto;
        display: block;
        z-index: $zindex-modal;
        background-color: $color-surface-1;
        box-shadow: 0 2px 20px 0 #252525;

        @include transition(
            opacity $modal-transition-duration ease-in-out,
            margin-top $modal-transition-duration ease-in-out);

        max-height: 80%;
        overflow-y: auto;

        &.transparent {
            background-color: transparent;
            box-shadow: none;
        }

        @include mobile {
            &.modal-small,
            &.modal-medium,
            &.modal-large {
                margin-left: 0.6*$m;
                margin-right: 0.6*$m;
            }
        }

        .modal-toolbar {
            display: flex;
            justify-content: flex-end;
        }

        .modal-body {
            text-align: center;
            display: block !important;
            width: auto !important;

            div {
                color: $color-content-default;
            }

            h1 {
                word-wrap: break-word;
            }

            h1, p, strong {
                color: $color-content-default;
            }
        }

        @include desktop {
            &.modal-small {
                width: 580px;
                max-width: 80%;
            }
            &.modal-sm {
                width: 690px;
                max-width: 80%;
            }
            &.modal-medium {
                width: 960px;
                max-width: 80%;
            }
            &.modal-large {
                width: 1300px;
                max-width: 80%;
            }
        }

        &.modal-large-max {
            max-width: 960px;
        }
    }


    // ANIMATION
    &.ng-enter {
        > .modal-overlay { opacity: 0; }
        > .modal-dialog {
            margin-top: 3.0*$m;
            opacity: 0;
        }
    }
    &.ng-enter.ng-enter-active,
    &.ng-show {
        > .modal-overlay { opacity: 1; }
        > .modal-dialog {
            margin-top: 0;
            opacity: 1;
        }
    }
    &.ng-leave {
        > .modal-overlay { opacity: 1; }
        > .modal-dialog {
            margin-top: 0;
            opacity: 1;
        }
    }
    &.ng-leave.ng-leave-active,
    &.ng-hide {
        visibility: hidden;
        > .modal-overlay { opacity: 0; }
        > .modal-dialog {
            margin-top: 3.0*$m;
            opacity: 0;
        }
    }
}

.modal-footer {
    padding: 25px 118px;
    text-align: right;
}
