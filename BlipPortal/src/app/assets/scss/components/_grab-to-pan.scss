/* Copyright 2013 <PERSON> <<EMAIL>>
 * https://github.com/Rob--W/grab-to-pan.js
 *
 * grab.cur and grabbing.cur are taken from Firefox's repository.
 **/
 .grab-to-pan-grab {
    cursor: url("../../img/grab.cur"), move !important;
    cursor: -webkit-grab !important;
    cursor: -moz-grab !important;
    cursor: grab !important;
}

.grab-to-pan-grab *:not(input):not(textarea):not(button):not(select):not(:link):not(.blip-tag-container) {
    cursor: inherit;
}

.grab-to-pan-grab:active,
.grab-to-pan-grabbing {
    cursor: url("../../img/grabbing.cur"), move !important;
    cursor: -webkit-grabbing !important;
    cursor: -moz-grabbing !important;
    cursor: grabbing !important;
}
