.timeline {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0 auto;

    .timeline-item {
        position: relative;
        @include clear-after;
        display: inline-flex;
        align-items: center;
        margin: 0;

        @include transition(opacity 0.09s ease-in-out);
        opacity: 1;
        &.ng-hide {
            opacity: 0;
        }

        &:not(:last-child)::before {
            content: '';

            position: absolute;
            top: 0.4*$m;
            bottom: -0.4*$m;
            left: 1.6*$m;

            @include mobile {
                left: 1.0*$m;
            }

            width: 0.4*$m;
        }

        .timeline-icon {
            position: relative;
            z-index: 1;

            display: table-cell;
            vertical-align: top;
            margin-right: 0.9*$m;

            > .icon {
                @include mobile {
                    font-size: $bp-fs-3;
                    line-height: 1;
                    width: 2.4*$m;
                    height: 2.4*$m;
                }
            }
        }
        .timeline-content {

            width: 360px;

            > h6 {
                margin: 0;
            }

            audio,
            video {
                border-radius: 12px 12px 0 0;
                width: 100%;
                max-height: 180px;
            }
        }
    }
}
