tags-input {
    display: block;
}

tags-input *,
tags-input *:before,
tags-input *:after {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

tags-input .host {
    position: relative;
    margin-top: 5px;
    margin-bottom: 5px;
    height: 100%;
}

tags-input .host:active {
    outline: none;
}

tags-input .tags {
    padding: 0;
    overflow: hidden;
    word-wrap: break-word;
    cursor: text;
    background: none;
    border: none;
    border-radius: 0;
}

tags-input .tags.focused {
    outline: none;
    border-color: $bp-color-bot;
}

tags-input .tags .tag-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

tags-input .tags .tag-item {
    margin: 0.6*$m;
    padding: 0 13px;
    float: left;
    border-radius: 0;
    background-color: $color-white-dark;
    color: $bp-color-city;

    span {
        word-break: break-word;
    }
}

tags-input .tags .tag-item.selected {
    background-color: $bp-color-warning;
    color: $bp-color-white;
    .remove-button {
        color: $bp-color-white;
    }
}

tags-input .tags .tag-item .remove-button {
    margin: 0 0 0 5px;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    vertical-align: middle;
    font: bold 16px;
    color: $bp-color-bot;
}

tags-input .tags .tag-item .remove-button:active {
    color: $bp-color-bot;
}

tags-input .tags .input {
    outline: none;
    padding: 0 0 0 0.6*$m;
    float: left;
    width: 100% !important;
}

tags-input .tags .input.invalid-tag {
    color: $bp-color-warning;
}

tags-input .tags .input::-ms-clear {
    display: none;
}

tags-input[disabled] .host:focus {
    outline: none;
}

tags-input[disabled] .tags {
    background-color: #eee;
    cursor: default;
}

tags-input[disabled] .tags .tag-item {
    opacity: 0.65;
    background-color: $color-gray;
}

tags-input[disabled] .tags .tag-item .remove-button {
    cursor: default;
}

tags-input[disabled] .tags .tag-item .remove-button:active {
    color: #585858;
}

tags-input[disabled] .tags .input {
    background-color: #eee;
    cursor: default;
}

tags-input .autocomplete {
    margin-top: 5px;
    position: absolute;
    padding: 5px 0;
    z-index: 999;
    width: 100%;
    background-color: $bp-color-white;
    border: 1px solid rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

tags-input .autocomplete .suggestion-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
    max-height: 280px;
    overflow-y: auto;
    position: relative;
}

tags-input .autocomplete .suggestion-item {
    padding: 5px 10px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: $bp-fs-5;
    color: #000;
    background-color: $bp-color-white;
}

tags-input .autocomplete .suggestion-item.selected {
    color: $bp-color-white;
    background-color: #0097cf;
}

tags-input .autocomplete .suggestion-item.selected em {
    color: $bp-color-white;
    background-color: #0097cf;
}

tags-input .autocomplete .suggestion-item em {
    font: normal bold 1.5*$m;
    color: $bp-color-city;
    background-color: $bp-color-white;
}

/** Inline tags option **/

.inline-tags-input .tags {
    background-color: transparent;
    border-radius: 4px;
    border: 1px solid #ccc;
}

tags-input.inline-tags-input .tags .input {
    width: auto !important;
}

.inline-tags-input input[type="text"] {
    border-bottom: 0;
    &:focus {
        border-bottom: 0;
    }
}

.inline-tags-input .tags.focused {
    border-color: darken($color-white-dark, 5%);
}

.inline-tags-input .tags .tag-item {
    border-radius: 100px;
    color: $bp-color-bot;
    background-color: #DAF2F4;
}

.inline-tags-input .tags .tag-item.selected {
    background: #d9534f;
    border: 1px solid #d43f3a;
    border-radius: 4px;
    color: #fff;
}

.inline-tags-input .tags .tag-item button {
    background: transparent;
    color: #000;
    opacity: .4;
}

.inline-tags-input .autocomplete {
    border-radius: 4px;
}

.inline-tags-input .autocomplete .suggestion-item.selected {
    color: #262626;
    background-color: #e9e9e9;
}

.inline-tags-input .autocomplete .suggestion-item em {
    font-weight: normal;
    background-color: #ffff00;
}

.inline-tags-input .autocomplete .suggestion-item.selected em {
    color: #262626;
    background-color: #ffff00;
}
