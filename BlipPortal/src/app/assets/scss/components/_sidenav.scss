$sidenav-width: 318px;
$sidenav-max-width: 318px;

.sidenav {
    width: $sidenav-width;
    transition: max-width 0.3s ease-in-out;
    margin-top: 0;
    z-index: 99 !important;
    overflow-x: hidden;
    overflow-y: auto;

    @include scrollbar($bp-color-city);

    &.sidenav-subnavbar {
        top: $navbar-height + $subnavbar-height;
    }

    &.collapsed {
        max-width: 36px;
    }

    &:focus { outline: none; }

    header {
        padding: 0 0.6*$m;
        margin-bottom: 0.6*$m;

        width: $sidenav-width;
        max-width: $sidenav-width;

        height: 4.8*$m;
        line-height: 4.8*$m;

        display: table;

        &[href]:hover {
            background-color: $bp-color-suit;
        }

        > div {
            padding-right: 1.2*$m;
            display: table-cell;
            vertical-align: middle;
            > span,
            > small {
                max-width: 162px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                display: block;
            }
        }
    }

    .subheader {
        margin-bottom: 1.5*$m;

        [href]:first-child {
            padding: 0.6*$m 0 0.6*$m 4.2*$m;
            display: block;
            text-decoration: none;

            &:hover {
                background-color: $bp-color-suit;
            }
        }
    }

    // generic list
    ul,
    ol {
        margin: 0;
        list-style: none;
        list-style-type: none;
        min-height: 104px;
    }

    li {
        color:inherit;
        max-width: $sidenav-max-width;
        margin: 0;
        border-bottom: 1px solid $color-gray-light-2;

        a {
            padding: 19px 44px 15px 35px;
            border-left: 3px transparent solid;
            color: inherit;
            display: block;
            text-decoration:none;
            min-height: inherit;
            &:hover { cursor: pointer; }
        }

        .icon {
            line-height: 0.9;
        }

        span {
            display: block;
        }

        .sidebar-title {
            color: $color-content-default;
            font-size: $bp-fs-5;
            font-weight: $bp-fw-regular;
        }

        .sidebar-subtitle {
            color: $bp-color-city;
            font-size: $bp-fs-7;
            font-weight: $bp-fw-regular;
        }
    }


    ol.ordered {
        counter-reset: listCounter;
    }
    ol.ordered li {
        counter-increment: listCounter;
    }

    ol.ordered li a:before {
        content: counter(listCounter) " ";
        padding-left: 0.3*$m;
        padding-right: 1.2*$m;
        display: inline;
    }

    // first level list
    li:hover {
        .sidebar-title, .sidebar-subtitle {
            color: $bp-color-suit;
        }
    }
    li.active > a,
    li > a.active {
        background-color: $color-surface-2;

        .sidebar-title, .sidebar-subtitle {
            color: $color-content-default;
        }
    }

    // second level list
    li.active > ul, ol {
        display: block;
    }
    ul.nav-second-level,
    ol.nav-second-level {
        background-color: $bp-color-obsidian;
        padding: 0;
        overflow: hidden;
        max-height: 0;
        transition: max-height 500ms, padding 500ms;

        &.expanded {
            border-top: 2px $bp-color-obsidian solid;
            padding: 0.9*$m 0 0.3*$m 2.4*$m;
            max-height: 9999px;
            transition: 500ms;
        }

        li { width: auto; }

        li > a {
            padding-left: 0.9*$m;
            font-weight: $bp-fw-regular;
            width: 92%;
        }
        li.active > a,
        li > a.active {
            font-weight: $bp-fw-extra-bold;
        }
    }

    &.collapsed {
        ul.nav-second-level.expanded,
        ol.nav-second-level.expanded {
            display: none;
        }
    }

    @include desktop {
        top: $navbar-height-desktop;
        bottom: $footer-height-desktop;
        left: 0;

        &.sidenav-subnavbar {
            top: $navbar-height-desktop + $subnavbar-height-desktop;
        }
    }
}

.sidenav + article {
    @include desktop {
        margin-left: $sidenav-width;
    }
}
