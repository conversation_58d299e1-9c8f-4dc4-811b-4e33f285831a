input[type="checkbox"].switch {
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
    z-index: -1;
    margin: 0;

    + label {
        position: relative;
        background: $bp-color-rooftop;

        transition: background-color 0.33s ease;

        width: 4.8*$m;
        height: 2.4*$m;
        font-size: $bp-fs-5;
        vertical-align: top;

        border-radius: 36px;

        margin: 0 0 -0.3*$m 0;
        cursor: pointer;
        display: inline-block;

        > span {
            display: block;
            width: 20px;
            height: 20px;
            position: absolute;
            top: 2px;
            left: 2px;
            background: $bp-color-white;
            border-radius: 50%;
            transition: left 0.33s ease;
        }
    }

    &:checked + label {
        background: $bp-color-bot;
        > span {
            left: 25px;
        }
    }
    &:disabled + label {
        background: $bp-color-city;
    }
}
