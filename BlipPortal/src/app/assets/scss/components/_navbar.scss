$navbar-height: 7.2*$m;
$navbar-min-height-desktop: 8.2*$m;
$navbar-height-desktop: 9.2*$m;

.navbar-placeholder {
    line-height: $navbar-height;
    @include desktop {
        line-height: $navbar-height-desktop;
    }
}

.navbar {
    min-height: $navbar-height;
    max-height: 42.2*$m;
    @include transition(max-height 0.3s ease-in-out);
    margin-top: -0.3*$m;

    @include mobile {
        overflow: hidden;
        &.collapsed {
            max-height: $navbar-height;
        }
        .container {
            width: 100%;
            height: $navbar-height;
            padding: 0;
        }
    }

    .brand {
        padding: 2.1*$m 0 0 0;
        text-decoration: none;
        height: $navbar-height - 0.1*$m;

        @include mobile {
            width: 100%;

            .brand-logo {
                width: 150px;
            }

            .translate-portal {
                padding: 0;
                margin-right: 30px;
            }
        }

        a {
            text-decoration: none;
        }

        &:hover {
            color: $bp-color-white;
        }

        img {
            height: 3.6*$m;

            @include mobile {
                height: auto;
            }
        }

        span {
            font-size: $bp-fs-3;
        }
    }

    ul {
        list-style-type: none;
        margin: 0 0 0 20px;
        padding: 0;
        border-bottom: 0.3*$m $bp-color-bot solid;

        @include mobile {
            width: 100%;
            margin: 0;
        }

        li {
            margin: 0;

            @include desktop {
                float: left;
            }

            a {
                font-size: $bp-fs-7;
                font-weight: $bp-fw-extra-bold;
                color: $bp-color-breeze;
                text-decoration: none;
                text-align: center;
                text-transform: uppercase;
                padding: 1.8*$m 1.8*$m;
                display: block;
                transition: 300ms;

                &.active {
                    color: $bp-color-white;
                }
                &:hover {
                    color: $bp-color-white;
                }
            }
        }
    }

    .navbar-border {
        position: absolute;
        border-bottom: 0.3*$m $bp-color-bot solid;
        top: $navbar-height - 0.3*$m;

        @include desktop {
            border-bottom: 0.6*$m $bp-color-bot solid;
            top: $navbar-height-desktop - 0.6*$m;
        }

        width: 100%;
        left: 0;
        right: 100%;
        display: block;

        &.loading {
            animation: loading-border 0.9s linear infinite;
        }
    }

    @include desktop {
        min-height: $navbar-min-height-desktop;
        max-height: $navbar-height-desktop;

        .brand {
            padding: 2.8*$m 0 0 0;
        }

        .container {
            height: $navbar-height-desktop;
        }

        ul {
            display: inherit;
            border: none;
        }

        ul > li > a {
            padding: 4.0*$m 0.8*$m;
        }
    }


    @include landscape {
        ul > li > a {
            padding: 3.8*$m 1.7*$m;
        }
    }

    @include desktop-large {
        ul > li > a {
            padding: 3.3*$m 1.4*$m;
        }
    }

    @keyframes loading-border {
        0% {
            width: 0%;
            left: 0;
            right: 0;
        }
        50% {
            width: 100%;
            left: 0;
            right: 100%;
        }
        100% {
            width: 0%;
            left: 100%;
            right: 100%;
        }
    }
}
