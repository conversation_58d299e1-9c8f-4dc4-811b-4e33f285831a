ul {
    list-style-type: square;
}

ul.no-style {
    list-style: none;
    li {
        list-style: none;
    }
}

ul.list-bordered {
    list-style: none;
    padding: 0;

    li {
        padding: 1.8*$m 0;
        margin: 0;
        display: block;
        + li {
            border-top: 1px $color-gray-light solid;
        }
    }
}

ul.list-inline-block {
    list-style: none;
    padding: 0;

    li {
        margin: 0 1.0*$m;
        display: inline-block;
    }

    &.u-no-margin {
        li { margin: 0; }
    }
}

ul.list-grid {
    li {
        margin: 0;
        padding: 0 0.9*$m;
        vertical-align: top;
    }

    &.list-grid-2 {
        -webkit-column-count: 2;
        -webkit-column-gap: 0;
        -webkit-column-fill: auto;
        -moz-column-count: 2;
        -moz-column-gap: 0;
        column-count: 2;
        column-gap: 0;
        column-fill: auto;
        li {
            -webkit-column-break-inside: avoid;
            -moz-column-break-inside: avoid;
            -ms-column-break-inside: avoid;
            column-break-inside: avoid;
        }
    }
}

ul.list-stripped {
    > li { padding: 10px; }
    > li:nth-child(odd) {
        background: $color-white-dark;
    }
}
