$toast-spacing: 18px;
$ngt-transition-duration: 0.3s;
$ngt-transition-timing-function: ease;

@mixin customToast($background, $color, $font-size, $darken-bg, $icon) {
    background: $background;
    color: $color;

    button.close {
        color: $color;
    }

    &:before {
        margin-right: 25px;
        font-size: $font-size;
        content: $icon;
    }

    &:hover {
        background-color: $darken-bg;
    }
}

.alert {
    background-color: $color-text-dark;
    color: $bp-color-white;
    border-radius: 10px;
    font-size: $bp-fs-6;
    font-family: 'blip-icons';
    // padding: 1.2 * $m 5 * $m;
    padding: 14px 20px;
    align-items: center;
    @include flex-box;
    display: inline-flex;

    span[ng-transclude] {
        width: 100%;
    }
}

.ng-toast .ng-toast__message {
    .alert {
        &:before {
            line-height: 1;
        }
    }

    // Success message
    .alert-success {
        @include customToast(
            linear-gradient(to right, #00e4e8, #00d3be),
            white,
            $bp-fs-3,
            darken($color-success-2, 3%),
            '\35'
        );
    }

    // Warning
    .alert-warning {
        @include customToast(
            linear-gradient(to right, #ffbd4e, #ffb04f),
            white,
            $bp-fs-3,
            darken($color-warning-light, 3%),
            '\36'
        );
    }

    // Info
    .alert-info {
        @include customToast(
            linear-gradient(to right, #00bafe, #00d0ed),
            white,
            $bp-fs-3,
            darken($color-info-light, 3%),
            '\57'
        );
    }

    // Danger
    .alert-danger {
        @include customToast(
            linear-gradient(to right, #ff654b, #ff807f),
            white,
            $bp-fs-3,
            darken($bp-color-warning-light, 3%),
            '\53'
        );
    }

    .alert-refresh {
        @include customToast(
            linear-gradient(to right, #03a7ea, #006ffe),
            white,
            $bp-fs-3,
            darken(#006ffe, 3%),
            " "
        );
        border: 0;
    }
}

/*!
 * ngToast v2.0.0 (http://tameraydin.github.io/ngToast)
 * Copyright 2016 Tamer Aydin (http://tamerayd.in)
 * Licensed under MIT (http://tameraydin.mit-license.org/)
 */
.ng-toast {
    position: fixed;
    z-index: $zindex-toast;
    width: 100%;
    height: 0;
    margin-top: 30px;
    text-align: center;

    // top: auto;
    // bottom: $footer-height;
    top: $navbar-height;
    bottom: auto;
    @include desktop {
        top: 10.5 * $m;
    }

    .ng-toast--top {
        top: 0;
        bottom: auto;

        .ng-toast__list {
            top: 0;
            bottom: auto;
        }

        .ng-toast--center .ng-toast__list {
            position: static;
        }
    }

    &.ng-toast--bottom {
        top: auto;
        bottom: 0;

        .ng-toast__list {
            top: auto;
            bottom: 20px;
        }

        &.ng-toast--center {
            .ng-toast__list {
                pointer-events: none;
            }

            .ng-toast__message {
                .alert {
                    pointer-events: auto;
                }
            }
        }
    }

    &.ng-toast--right {
        .ng-toast__list {
            left: auto;
            right: 0;
            margin-right: $toast-spacing;
        }

        .ng-toast__message {
            text-align: right;
        }
    }

    &.ng-toast--left {
        .ng-toast__list {
            text-align: initial;
            width: 100%;
            right: auto;
            left: 0;
            margin-left: $toast-spacing;
        }

        .ng-toast__message {
            text-align: left;
            display: block;
            width: auto;
            max-width: 500px;
        }
    }

    .ng-toast__list {
        display: inline-block;
        margin: 0 auto;
        margin-right: $toast-spacing;
        padding: 0;
        list-style: none;
        min-width: 50%;
        max-width: 500px;
        position: absolute;
    }

    .ng-toast__message {
        display: block;
        width: 100%;
        text-align: left;

        button.close {
            position: relative;
            right: -1.8 * $m;
            float: right;
            height: auto;
            line-height: 3.2*$m;
            padding: 0 1.2 * $m;
            border: none;
            color: $bp-color-white;
            min-width: auto;
            order: 2; //flex order
        }

        .alert {
            z-index: 90;
            width: auto;
            &:hover {
                cursor: pointer;
            }
        }
    }

    .ng-toast__message__count {
        display: inline-block;
        margin: 0 calc($toast-spacing / 4 * 3)  0 calc($toast-spacing / 4);
    }

    // animations
    &.ng-toast--animate-slide {
        .ng-toast__message {
            position: relative;
            @include transition(
                bottom 0.3s ease-in-out,
                margin-bottom 0.3s ease-in-out,
                opacity 0.3s ease-in-out
            );
            &.ng-enter {
                opacity: 0;
                bottom: -30px;
            }
            &.ng-enter.ng-enter-active {
                opacity: 1;
                bottom: 0;
            }
            &.ng-leave {
                opacity: 1;
                bottom: 0;
            }
            &.ng-leave.ng-leave-active {
                opacity: 0;
                margin-bottom: -(52px + $toast-spacing);
            }
        }
    }
}
