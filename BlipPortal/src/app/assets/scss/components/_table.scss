table.old-style {
    overflow-x: auto;
    display: block;

    @include desktop {
        overflow: inherit;
        display: table;
    }

    th,
    td {
        white-space: nowrap;
    }

    th {
        font-size: $bp-fs-6;
        color: $bp-color-bot;
        white-space: nowrap;

        &:first-child { padding-left: 1.2*$m; }

        @include desktop {
            font-size: $bp-fs-5;
        }

        border-bottom: 2px $bp-color-bot solid;
    }
    td {
        text-align: inherit;
    }

    &.no-style {
        border: none;
        margin: 0;
        padding-top: 0;
        padding-bottom: 0;
        td {
            border: none;
            margin: 0;
            padding-top: 0;
            padding-bottom: 0;
        }
    }
}

.table {
    display: table;
    border-spacing: 0;

    &.table-fixed {
        table-layout: fixed;
    }

    @include mobile {
        &.table-responsive {
            display: block;
            .column,
            .columns {
                display: block;
            }
        }
    }

    .column,
    .columns {
        display: table-cell;
        margin: 0;
        float: none;
        vertical-align: middle;
    }

    .column-auto {
        width: auto;
    }
    .column-xxs {
        width: $size-xxs;
    }
    .column-xs {
        width: $size-xs;
    }
    .column-ss {
        width: $size-ss;
    }
    .column-s {
        width: $size-s;
    }
    .column-sm {
        width: $size-sm;
    }
    .column-m {
        width: $size-m;
    }
    .column-l {
        width: $size-l;
    }

    &.table-grid {
        width: 100%;
        vertical-align: middle;

        .column,
        .columns {
            vertical-align: middle;
            display: table-cell;
            margin: 0;
            float: none;
        }

        .column-top { vertical-align: top; }
        .column-bottom { vertical-align: bottom; }

        .gutter {
            display: table-cell;
            margin: 0;
            width: 4%;
            float: none;
        }
    }

    @include mobile {
        &.table-grid {
            display: block;
            height: auto;
            .column,
            .columns {
                display: block;
            }
            .gutter {
                display: block;
                width: auto;
                height: 1.2*$m;
            }
        }

        &.table-responsive {
            display: block;
            .column {
                display: block;
            }
        }
    }
}

