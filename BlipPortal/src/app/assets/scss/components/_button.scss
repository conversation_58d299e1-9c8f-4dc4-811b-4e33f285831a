button,
.button,
input[type='submit'],
input[type='reset'],
input[type='button'],
input[type='submit']:focus,
input[type='reset']:focus,
input[type='button']:focus {
    border-color: $bp-color-bot;
    border-radius: 3px;
    color: $bp-color-bot;
    background-color: transparent;

    font-size: $bp-fs-5;
    vertical-align: middle;

    > * {
        vertical-align: middle;
    }

    padding: 0 2*$m;
    height: 4.2*$m;
    min-width: 13*$m;
    margin-bottom: 0;
    overflow: hidden;
    outline: none;

    &:hover {
        border-color: $bp-color-blip-light;
        color: $bp-color-blip-light;
        text-decoration: none;
    }

    &.button-disabled,
    &[disabled='disabled'],
    &:disabled {
        border-color: $color-gray;
        background-color: $color-gray;
        color: $bp-color-white;

        &:after {
            border-color: $color-gray;
            background-color: $color-gray;
            color: $bp-color-white;
        }

        &:hover {
            border-color: $color-gray;
            background-color: $color-gray;

            &:after {
                border-color: $color-gray;
                background-color: $color-gray;
            }
        }
    }

    &.no-style {
        outline: none;
        background: none;
        border: none;
        color: inherit;
        padding: 0;
        margin: 0;
        height: auto;
        line-height: inherit;
        vertical-align: top;
        min-width: auto;
    }

    &.button-facebook {
        border-color: $color-facebook;
        background-color: $color-facebook;
        color: $bp-color-white;
        height: auto;
        padding-left: 0.9*$m;
        &:hover {
            border-color: $color-facebook-dark;
            background-color: $color-facebook-dark;
        }
    }

    &.button-hub {
        width: 100%;
        background-color: transparent;
        border: none;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        height: 6.4*$m;
        letter-spacing: 0;
        font-weight: $bp-fw-bold;

        &.connected {
            background: $bp-color-bot;
            color: $bp-color-white;
        }

        &.upgrade {
            color: $color-primary-pinks-watermelon;
        }
    }
}

i[dnd-handle],
button[dnd-handle] {
    &:hover {
        cursor: move;
    }
}
