@import '~blip-ds/dist/collection/styles/colors';
datepicker {
    width: auto !important;

    input[type="text"] {
        width: 110px;
        letter-spacing: inherit;
        font-size: $bp-fs-5;
        margin-left: 10px;
    }

    ._720kb-datepicker-calendar {
        z-index: 999 !important;
    }
}

._720kb-datepicker-calendar-header:nth-child(odd) {
    background: $color-primary !important;
}

._720kb-datepicker-calendar-header:nth-child(even) {
    background: $color-primary !important;
}

.small-datepicker-input, .filter.dib {
    datepicker {
        margin: 0;

        input[type="text"] {
            width: 90px;
            letter-spacing: inherit;
            font-size: 1.6rem;
            margin: 0 5px;
        }
    }

    input[type="number"] {
        border-bottom: none;
    }

    input[type="text"] {
        border-bottom: none;
    }

    .bp-daterange-dropdown {
        right: 0;
        background-color: $color-surface-1;
        .bp-datepicker {
            background-color: $color-surface-1;
            color: $color-content-default;
        }
    }

    .bp-daterange-inputs {
        width: 24rem;
        color: $color-content-default;
        .bp-daterange-start-date {
            width: 10rem;
        }

        .bp-daterange-end-date {
            width: 10rem;
        }
    }
    .bp-datepicker {
        text-align: center;
        background-color: $color-surface-1;
    }

    .bp-datepicker .time-container .time-input-container {
        width: 7rem;
    }
}

.report-datepicker {

    margin-left: auto;

    datepicker {
        margin: 0;
        float: none;
        font: inherit;

        input[type="text"] {
            width: 90px;
            letter-spacing: inherit;
            margin: 0 5px;
            font-weight: 400;
        }
    }

    .timepicker input {
        width: 50px;
        height: 2.5*$m;
    }
}
