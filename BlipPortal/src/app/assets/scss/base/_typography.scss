// Base
body {
    -webkit-font-smoothing: antialiased;
    -moz-font-smoothing: antialiased;
    -ms-font-smoothing: antialiased;
    -o-font-smoothing: antialiased;
    font-smooth: antialised;
}

p {
    color: $color-content-default;
    font-size: $bp-fs-5;
    margin-bottom: 2.4*$m;
}

h1 {
    line-height: $bp-lh-simple;
}

h2, h3, h4, h5, h6, p, span, a, i, b, strong, small, em {
    line-height: $bp-lh-plus;
}

// Headings
h1, h2, h3, h4, h5, h6 {
    color: $bp-color-city;
    font-weight: $bp-fw-regular;
    margin-bottom: 1.2*$m;
}

h1 {
    font-size: $bp-fs-3;
    margin-bottom: 3.0*$m;
    font-weight: $bp-fw-regular;
}
h2 {
    font-size: $bp-fs-4;
    font-weight: $bp-fw-regular;
    margin-bottom: 3.0*$m;
}
h3 {
    font-size: $bp-fs-4;
    font-weight: $bp-fw-regular;
}
h4 { font-size: $bp-fs-5; }
h5 {
    font-size: $bp-fs-6;
    font-weight: $bp-fw-extra-bold;
}
h6 {
    font-size: $bp-fs-5;
    font-weight: $bp-fw-extra-bold;
    letter-spacing: -0.064*$m;
    text-transform: uppercase;
}

@media (max-width: 550px) {
    h1 { font-size: $bp-fs-3; }
    h2 { font-size: $bp-fs-3; }
    h3 { font-size: $bp-fs-4; }
    h4 { font-size: $bp-fs-4; }
    h5 { font-size: $bp-fs-5; }
    h6 { font-size: $bp-fs-5; }
}

small {
    color: $bp-color-rooftop;
    font-size: $bp-fs-7;
    letter-spacing: 0.0em;
}

hr {
    margin-top: 1.5*$m;
    margin-bottom: 1.8*$m;
}

b, strong {
    font-weight: $bp-fw-extra-bold;
}

// Anchors
a {
    color: $color-primary;
    text-decoration: none;
}
a:hover {
    color: $bp-color-blip-dark;
    text-decoration: underline;
    &.text { color: $color-text-dark; }
}

a:focus { outline: 0; }

label {
    font-size: $bp-fs-6;
    letter-spacing: 0.05*$m;
    color: $color-content-default;
    margin: 0 0 0.6*$m;
    font-weight: 700;

    small {
        text-transform: none;
        letter-spacing: normal;
    }
}
.input-group label {
    margin-bottom: 0.5*$m;
}

.label {
    font-weight: $bp-fw-extra-bold;
    color: $color-content-default;
    letter-spacing: 0.01*$m;
    margin: 0;
}
