@import '../variables';

.hover-c-bot:hover { color: $bp-color-bot; }
.hover-c-warning:hover { color: $bp-color-warning; }

.color-info { color: $color-info; }
.color-info-light { color: $color-info-light; }

.color-warning { color: $color-warning; }
.color-warning-light { color: $color-warning-light; }
.color-warning-dark { color: $color-warning-dark; }

.color-disabled { color: $color-disabled; }
.color-accent { color: $color-accent; }
.color-white-dark { color: $color-white-dark; }

.color-gray { color: $color-gray; }
.color-gray-light { color: $color-gray-light; }
.color-gray-light-2 { color: $color-gray-light-2; }
.color-gray-light-4 { color: $color-gray-light-4; }
.color-gray-light-5 { color: $color-gray-light-5; }
.color-gray-dark { color: $color-gray-dark; }

.background-light-blip-2 { background: $color-blip-light-2; }

.color-text-dark { color: $color-text-dark; }
.color-text-dark-5 { color: $color-text-dark-5; }
.background-text-dark-5 { background: $color-text-dark-5; }

.color-gray-dark-2 { color: $color-gray-dark-2; }
.color-gray-dark-3 { color: $color-gray-dark-3; }
.color-gray-dark-4 { color: $color-gray-dark-4; }

.color-text-disabled { color: $color-text-disabled; }
.color-input-disabled { color: $color-input-disabled;}
.color-blip-blue-1 { color: $color-blip-blue-1 }
.color-blip-blue-2 { color: $color-blip-blue-2 }
.color-blip-blue-3 { color: $color-blip-blue-3 }

.color-facebook { color: $color-facebook; }
.color-facebook-dark { color: $color-facebook-dark; }

//Aux classes
.text-accent { color: $color-accent; }
.text-black { color: $color-text-dark; }
.text-info { color: $color-info; }

.text-disabled { color: $color-gray-light; }
.text-info { color: $color-info; }

.border-color-1 { border-color: $border-color-1; }

// Backgrounds
.bp-white-dark { background-color: $color-white-dark; }
.bg-accent { background-color: $color-accent; }
.bg-gray { background-color: $color-gray; }

.bg-disabled { background-color: $color-gray-light; }
.bg-info { background-color: $color-info; }
