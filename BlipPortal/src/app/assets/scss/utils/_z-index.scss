
// Converted Variables


// Custom Media Query Variables


/*

  Z-INDEX

  Base
    z = z-index

  Modifiers
    -0 = literal value 0
    -1 = literal value 1
    -2 = literal value 2
    -3 = literal value 3
    -4 = literal value 4
    -5 = literal value 5
    -999 = literal value 999
    -9999 = literal value 9999

    -max = largest accepted z-index value as integer

    -inherit = string value inherit
    -initial = string value initial
    -unset = string value unset

  MDN: https://developer.mozilla.org/en/docs/Web/CSS/z-index
  Spec: http://www.w3.org/TR/CSS2/zindex.html
  Articles:
    https://philipwalton.com/articles/what-no-one-told-you-about-z-index/

  Tips on extending:
  There might be a time worth using negative z-index values.
  Or if you are using tachyons with another project, you might need to
  adjust these values to suit your needs.

*/

.z-0 { z-index: 0; }
.z-1 { z-index: 1; }
.z-2 { z-index: 2; }
.z-3 { z-index: 3; }
.z-4 { z-index: 4; }
.z-5 { z-index: 5; }
.z-6 { z-index: 6; }
.z-7 { z-index: 7; }
.z-8 { z-index: 8; }
.z-9 { z-index: 9; }
.z-10 { z-index: 10; }

.z-max { z-index: 9999991; }     // Used for modals that take the hole page size

/* New z-index values defined on blip-ds */
.zindex-footer { z-index: $zindex-footer; }
.zindex-local-sidebar { z-index: $zindex-local-sidebar; }
.zindex-subheader { z-index: $zindex-subheader; }
.zindex-header { z-index: $zindex-header; }
.zindex-full-screen-sidebar { z-index: $zindex-full-screen-sidebar; }
.zindex-page { z-index: $zindex-page; }
.zindex-dropdown { z-index: $zindex-dropdown; }
.zindex-modal-overlay { z-index: $zindex-modal-overlay; }
.zindex-modal { z-index: $zindex-modal; }
.zindex-loading { z-index: $zindex-loading; }
.zindex-toast { z-index: $zindex-toast; }

.z-inherit { z-index: inherit; }
.z-initial { z-index: initial; }
.z-unset { z-index: unset; }

