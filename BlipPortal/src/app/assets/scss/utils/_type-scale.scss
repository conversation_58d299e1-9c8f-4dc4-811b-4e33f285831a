
// Converted Variables


// Custom Media Query Variables


/*

   TYPE SCALE
   Docs: http://tachyons.io/docs/typography/scale/

   Base:
    f = font-size

   Modifiers
     1 = 1st step in size scale
     2 = 2nd step in size scale
     3 = 3rd step in size scale
     4 = 4th step in size scale
     5 = 5th step in size scale
     6 = 6th step in size scale
     7 = 7th step in size scale

   Media Query Extensions:
     -ns = not-small
     -m  = medium
     -l  = large
*/

/*
 * For Hero/Marketing Titles
 *
 * These generally are too large for mobile
 * so be careful using them on smaller screens.
 * */

.f-6,
.f-headline {
  font-size: 6*$m;
}
.f-5,
.f-subheadline {
  font-size: 5*$m;
}


/* Type Scale */

.f1 { font-size: 3*$m; }
.f2 { font-size: 2.25*$m; }
.f3-1 { font-size: 2*$m; }
.f3 { font-size: 1.5*$m; }
.f4 { font-size: 1.25*$m; }
.f5 { font-size: 1*$m; }
.f6 { font-size: .875*$m; }
.f7 { font-size: .75*$m; } /* Small and hard to read for many people so use with extreme caution */

@media #{$breakpoint-not-small}{
  .f-6-ns,
  .f-headline-ns { font-size: 6*$m; }
  .f-5-ns,
  .f-subheadline-ns { font-size: 5*$m; }
  .f1-ns { font-size: 3*$m; }
  .f2-ns { font-size: 2.25*$m; }
  .f3-ns { font-size: 1.5*$m; }
  .f4-ns { font-size: 1.25*$m; }
  .f5-ns { font-size: 1*$m; }
  .f6-ns { font-size: .875*$m; }
  .f7-ns { font-size: .75*$m; }
}

@media #{$breakpoint-medium} {
  .f-6-m,
  .f-headline-m { font-size: 6*$m; }
  .f-5-m,
  .f-subheadline-m { font-size: 5*$m; }
  .f1-m { font-size: 3*$m; }
  .f2-m { font-size: 2.25*$m; }
  .f3-m { font-size: 1.5*$m; }
  .f4-m { font-size: 1.25*$m; }
  .f5-m { font-size: 1*$m; }
  .f6-m { font-size: .875*$m; }
  .f7-m { font-size: .75*$m; }
}

@media #{$breakpoint-large} {
  .f-6-l,
  .f-headline-l {
    font-size: 6*$m;
  }
  .f-5-l,
  .f-subheadline-l {
    font-size: 5*$m;
  }
  .f1-l { font-size: 3*$m; }
  .f2-l { font-size: 2.25*$m; }
  .f3-l { font-size: 1.5*$m; }
  .f4-l { font-size: 1.25*$m; }
  .f5-l { font-size: 1*$m; }
  .f6-l { font-size: .875*$m; }
  .f7-l { font-size: .75*$m; }
}
