
// Converted Variables


// Custom Media Query Variables


/*

   BORDER WIDTHS
   Docs: http://tachyons.io/docs/themes/borders/

   Base:
     bw = border-width

   Modifiers:
     0 = 0 width border
     1 = 1st step in border-width scale
     2 = 2nd step in border-width scale
     3 = 3rd step in border-width scale
     4 = 4th step in border-width scale
     5 = 5th step in border-width scale

   Media Query Extensions:
     -ns = not-small
     -m  = medium
     -l  = large

*/

.bw0 { border-width: 0; }
.bw1 { border-width: .125*$m; }
.bw2 { border-width: .25*$m; }
.bw3 { border-width: .5*$m; }
.bw4 { border-width: 1*$m; }
.bw5 { border-width: 2*$m; }

.bw-0 { border-width: 0px; }
.bw-1 { border-width: 1px; }
.bw-2 { border-width: 2px; }
.bw-3 { border-width: 3px; }
.bw-4 { border-width: 4px; }
.bw-5 { border-width: 5px; }

/* Resets */
.bt-0 { border-top-width: 0; }
.br-0 { border-right-width: 0; }
.bb-0 { border-bottom-width: 0; }
.bl-0 { border-left-width: 0; }

@media #{$breakpoint-not-small} {
  .bw0-ns { border-width: 0; }
  .bw1-ns { border-width: .125*$m; }
  .bw2-ns { border-width: .25*$m; }
  .bw3-ns { border-width: .5*$m; }
  .bw4-ns { border-width: 1*$m; }
  .bw5-ns { border-width: 2*$m; }
  .bt-0-ns { border-top-width: 0; }
  .br-0-ns { border-right-width: 0; }
  .bb-0-ns { border-bottom-width: 0; }
  .bl-0-ns { border-left-width: 0; }
}

@media #{$breakpoint-medium} {
  .bw0-m { border-width: 0; }
  .bw1-m { border-width: .125*$m; }
  .bw2-m { border-width: .25*$m; }
  .bw3-m { border-width: .5*$m; }
  .bw4-m { border-width: 1*$m; }
  .bw5-m { border-width: 2*$m; }
  .bt-0-m { border-top-width: 0; }
  .br-0-m { border-right-width: 0; }
  .bb-0-m { border-bottom-width: 0; }
  .bl-0-m { border-left-width: 0; }
}

@media #{$breakpoint-large} {
  .bw0-l { border-width: 0; }
  .bw1-l { border-width: .125*$m; }
  .bw2-l { border-width: .25*$m; }
  .bw3-l { border-width: .5*$m; }
  .bw4-l { border-width: 1*$m; }
  .bw5-l { border-width: 2*$m; }
  .bt-0-l { border-top-width: 0; }
  .br-0-l { border-right-width: 0; }
  .bb-0-l { border-bottom-width: 0; }
  .bl-0-l { border-left-width: 0; }
}
