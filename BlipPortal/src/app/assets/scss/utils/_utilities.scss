@import "../main";

[ng-click] {
    &:not([disabled]),
    &:not(.text-disabled) { cursor: pointer; }
    &[disabled] {
        cursor: default;
        pointer-events: none;
    }
}

.u-code-content {
    font-family: monospace, monospace;
    font-size: $bp-fs-5;
    background: #EEEEEE !important;
    border: 0 !important;
}

.u-light-border {
    border: 1px solid $color-gray-light;
    &:focus { border: 1px solid $color-gray-light; }
}

@mixin u-status($background-color, $border-color) {
    &--small {
        width: 5px; height: 5px;
        border-width: 1px;
    }

    &--medium {
        width: 13px; height: 13px;
        border-width: 2px;
    }

    position: absolute;
    background: $background-color;
    top: 0; right: -3px;
    border-radius: 50%;
    border-color: $border-color;
    border-style: solid;
}

.full-vh-page {
    height: calc(100vh - 140px);
}

.u-status-on {
    @include u-status($bp-color-true, $bp-color-onix);
}

.u-status-off {
    @include u-status($bp-color-delete, $bp-color-onix);
}

.u-bubble-wrapper {
    padding: 30px 50px;
    background: #fff;
    margin: 20px auto 40px auto;
    border-radius: 2px;
    width: 80%;
    position: relative;
    box-shadow: 0 1px 20px 0 hsla(195,10%,80%,.7);

    &.full { width: 100%; }
}

.u-bubble-wrapper:before {
    content: " ";
    width: 15px;
    height: 15px;
    position: absolute;
    top: -8px;
    background: #fff;
    transform: rotate(45deg);
}

.input-error:not(span) {
    border: 1px solid $bp-color-warning !important;
}

hr.dashed  {
    border-top: 2px dashed $bp-color-breeze;
}

input.u-editable-field {
    border: 1px solid transparent;
    color: #546e7a;
    outline: none;
    background-color: transparent;
    padding: 10px 20px;

    &:focus,
    &:hover {
        border: 1px solid #bebebe;
    }
}

.no-content-found {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 31px;
    padding: 80px 0;
    color: #a8bfc4;
    font-size: $bp-fs-4;
    font-weight: 500;
    margin: auto;
}

.online {
    background: $bp-color-true !important;
    border: 1px solid $bp-color-true !important;
}

.status-invisible {
    background: $color-disabled !important;
    border: 1px solid $color-disabled !important;
}

.offline {
    background: $bp-color-delete !important;
    border: 1px solid $bp-color-delete !important;
}

.pause {
    background: $color-warning !important;
    border: 1px solid $color-warning !important;
}

.no-select {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
}
