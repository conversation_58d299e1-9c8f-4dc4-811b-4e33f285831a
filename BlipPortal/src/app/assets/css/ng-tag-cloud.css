/* fonts */

div.ng-tag-cloud {
  font-size: 10px;
  line-height: normal;
}

div.ng-tag-cloud a {
  font-size: inherit;
  text-decoration: none;
}

.ng-tag-cloud .w1,
.ng-tag-cloud .w2,
.ng-tag-cloud .w3,
.ng-tag-cloud .w4,
.ng-tag-cloud .w5 { width: auto; }

div.ng-tag-cloud span.w10 { font-size: 350%; }
div.ng-tag-cloud span.w9 { font-size: 325%; }
div.ng-tag-cloud span.w8 { font-size: 300%; }
div.ng-tag-cloud span.w7 { font-size: 275%; }
div.ng-tag-cloud span.w6 { font-size: 250%; }
div.ng-tag-cloud span.w5 { font-size: 225%; }
div.ng-tag-cloud span.w4 { font-size: 200%; }
div.ng-tag-cloud span.w3 { font-size: 175%; }
div.ng-tag-cloud span.w2 { font-size: 150%; }
div.ng-tag-cloud span.w1 { font-size: 125%; }

/* colors */

div.ng-tag-cloud { color: #09f; }
div.ng-tag-cloud a { color: inherit; }
div.ng-tag-cloud a:hover { color: #0df; }
div.ng-tag-cloud a:hover { color: #0cf; }
div.ng-tag-cloud span.w10 { color: #0cf; }
div.ng-tag-cloud span.w9 { color: #0cf; }
div.ng-tag-cloud span.w8 { color: #0cf; }
div.ng-tag-cloud span.w7 { color: #39d; }
div.ng-tag-cloud span.w6 { color: #90c5f0; }
div.ng-tag-cloud span.w5 { color: #90a0dd; }
div.ng-tag-cloud span.w4 { color: #90c5f0; }
div.ng-tag-cloud span.w3 { color: #a0ddff; }
div.ng-tag-cloud span.w2 { color: #99ccee; }
div.ng-tag-cloud span.w1 { color: #aab5f0; }

/* layout */

div.ng-tag-cloud {
  overflow: hidden;
  position: relative;
}

div.ng-tag-cloud span { padding: 0; }
