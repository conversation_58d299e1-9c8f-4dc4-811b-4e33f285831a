/* Copyright 2013 <PERSON> <<EMAIL>>
 * https://github.com/Rob--W/grab-to-pan.js
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

'use strict';

export const GrabToPan = (function GrabToPanClosure() {
    /**
     * Construct a GrabToPan instance for a given HTML element.
     * @param options.element {Element}
     * @param options.ignoreTarget {function} optional. See `ignoreTarget(node)`
     * @param options.onActiveChanged {function(boolean)} optional. Called
     *  when grab-to-pan is (de)activated. The first argument is a boolean that
     *  shows whether grab-to-pan is activated.
     */
    function GrabToPan(options) {
        this.positions = { posX: 0, posY: 0 };
        this.maxPositions;
        this.element = options.element;
        this.document = options.element.ownerDocument;
        if (typeof options.ignoreTarget === 'function') {
            this.ignoreTarget = options.ignoreTarget;
        }
        this.onActiveChanged = options.onActiveChanged;

        // Bind the contexts to ensure that `this` always points to
        // the GrabToPan instance.
        this.activate = this.activate.bind(this);
        this.deactivate = this.deactivate.bind(this);
        this.toggle = this.toggle.bind(this);
        this._onmousedown = this._onmousedown.bind(this);
        this._onmousemove = this._onmousemove.bind(this);
        this._endPan = this._endPan.bind(this);

        // This overlay will be inserted in the document when the mouse moves during
        // a grab operation, to ensure that the cursor has the desired appearance.
        let overlay = (this.overlay = document.createElement('div'));
        // Inline styles to get maximum CSS specifity
        overlay.style.position = 'fixed';
        overlay.style.background = 'transparent';
        overlay.style.display = 'block';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.right = '0';
        overlay.style.bottom = '0';
        overlay.style.height = 'auto';
        overlay.style.width = 'auto';
        overlay.style.overflow = 'hidden';
        overlay.style.zIndex = '2000000000';
        // Inherit cursor style via CSS style sheet:
        overlay.className = 'grab-to-pan-grabbing';
    }
    GrabToPan.prototype = {
        /**
         * Class name of element which can be grabbed
         */

        CSS_CLASS_GRAB: 'grab-to-pan-grab',

        /**
         * Bind a mousedown event to the element to enable grab-detection.
         */
        activate: function GrabToPan_activate() {
            if (!this.active) {
                this.active = true;
                this.element.addEventListener(
                    'mousedown',
                    this._onmousedown,
                    true,
                );
                this.element.classList.add(this.CSS_CLASS_GRAB);
                if (this.onActiveChanged) {
                    this.onActiveChanged(true);
                }
            }
        },

        getPositions: function() {
            return this.positions;
        },

        setPositions: function(posX, posY) {
            let x = posX > 0 ? posX : 0;
            x = x > this.maxPositions.posX ? this.maxPositions.posX : x;

            let y = posY > 0 ? posY : 0;
            y = y > this.maxPositions.posY ? this.maxPositions.posY : y;

            this.positions = { posX: x, posY: y };
        },

        setMaxPositions: function(posX, posY) {
            this.maxPositions = { posX, posY };
        },

        /**
         * Removes all events. Any pending pan session is immediately stopped.
         */
        deactivate: function GrabToPan_deactivate() {
            if (this.active) {
                this.active = false;
                this.element.removeEventListener(
                    'mousedown',
                    this._onmousedown,
                    true,
                );
                this._endPan();
                this.element.classList.remove(this.CSS_CLASS_GRAB);
                if (this.onActiveChanged) {
                    this.onActiveChanged(false);
                }
            }
        },

        toggle: function GrabToPan_toggle() {
            if (this.active) {
                this.deactivate();
            } else {
                this.activate();
            }
        },

        /**
         * Whether to not pan if the target element is clicked.
         * Override this method to change the default behaviour.
         *
         * @param node {Element} The target of the event
         * @return {boolean} Whether to not react to the click event.
         */
        ignoreTarget: function GrabToPan_ignoreTarget(node) {
            // Use matchesSelector to check whether the clicked element
            // is (a child of) an input element / link
            return node[matchesSelector](
                'a[href], a[href] *, input, textarea, button, button *, select, option',
            );
        },

        /**
         * @private
         */
        _onmousedown: function GrabToPan__onmousedown(event) {
            if (event.button !== 0 || this.ignoreTarget(event.target)) {
                return;
            }
            if (event.originalTarget) {
                try {
                    /* jshint expr:true */
                    event.originalTarget.tagName;
                } catch (e) {
                    // Mozilla-specific: element is a scrollbar (XUL element)
                    return;
                }
            }

            if (this.maxPositions === undefined) {
                this.setMaxPositions(
                    event.target.offsetWidth,
                    event.target.offsetHeight,
                );
            }

            this.scrollLeftStart = this.element.scrollLeft;
            this.scrollTopStart = this.element.scrollTop;
            this.clientXStart = event.clientX;
            this.clientYStart = event.clientY;
            this.document.addEventListener(
                'mousemove',
                this._onmousemove,
                true,
            );
            this.document.addEventListener('mouseup', this._endPan, true);
            // When a scroll event occurs before a mousemove, assume that the user
            // dragged a scrollbar (necessary for Opera Presto, Safari and IE)
            // (not needed for Chrome/Firefox)
            this.element.addEventListener('scroll', this._endPan, true);
            event.preventDefault();
            event.stopPropagation();

            let focusedElement = document.activeElement;
            if (focusedElement && !focusedElement.contains(event.target)) {
                focusedElement.blur();
            }
        },

        /**
         * @private
         */
        _onmousemove: function GrabToPan__onmousemove(event) {
            this.element.removeEventListener('scroll', this._endPan, true);
            if (isLeftMouseReleased(event)) {
                this._endPan();
                return;
            }
            let xDiff = event.clientX - this.clientXStart;
            let yDiff = event.clientY - this.clientYStart;
            let scrollTop = this.scrollTopStart - yDiff;
            let scrollLeft = this.scrollLeftStart - xDiff;
            this.setPositions(scrollLeft, scrollTop);
            if (this.element.scrollTo) {
                this.element.scrollTo({
                    top: scrollTop,
                    left: scrollLeft,
                    behavior: 'instant',
                });
            } else {
                this.element.scrollTop = scrollTop;
                this.element.scrollLeft = scrollLeft;
            }
            if (!this.overlay.parentNode) {
                document.body.appendChild(this.overlay);
            }
        },

        /**
         * @private
         */
        _endPan: function GrabToPan__endPan() {
            this.element.removeEventListener('scroll', this._endPan, true);
            this.document.removeEventListener(
                'mousemove',
                this._onmousemove,
                true,
            );
            this.document.removeEventListener('mouseup', this._endPan, true);
            if (this.overlay.parentNode) {
                this.overlay.parentNode.removeChild(this.overlay);
            }
        },
    };

    // Get the correct (vendor-prefixed) name of the matches method.
    let matchesSelector;
    ['webkitM', 'mozM', 'msM', 'oM', 'm'].some(function(prefix) {
        let name = prefix + 'atches';
        if (name in document.documentElement) {
            matchesSelector = name;
        }
        name += 'Selector';
        if (name in document.documentElement) {
            matchesSelector = name;
        }
        return matchesSelector; // If found, then truthy, and [].some() ends.
    });

    // Browser sniffing because it's impossible to feature-detect
    // whether event.which for onmousemove is reliable
    let isNotIEorIsIE10plus =
        !document.documentMode || document.documentMode > 9;
    let chrome = window.chrome;
    let isChrome15OrOpera15plus = chrome && (chrome.webstore || chrome.app);
    //                                       ^ Chrome 15+       ^ Opera 15+
    let isSafari6plus =
        /Apple/.test(navigator.vendor) &&
        /Version\/([6-9]\d*|[1-5]\d+)/.test(navigator.userAgent);

    /**
     * Whether the left mouse is not pressed.
     * @param event {MouseEvent}
     * @return {boolean} True if the left mouse button is not pressed.
     *                   False if unsure or if the left mouse button is pressed.
     */
    function isLeftMouseReleased(event) {
        if ('buttons' in event && isNotIEorIsIE10plus) {
            // http://www.w3.org/TR/DOM-Level-3-Events/#events-MouseEvent-buttons
            // Firefox 15+
            // Internet Explorer 10+
            return !(event.buttons & 1);
        }
        if (isChrome15OrOpera15plus || isSafari6plus) {
            // Chrome 14+
            // Opera 15+
            // Safari 6.0+
            return event.which === 0;
        }
    }

    return GrabToPan;
})();
