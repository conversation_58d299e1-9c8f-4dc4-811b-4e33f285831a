import * as angular from 'angular';

/**
 * ng-context-menu - v1.0.3 - An AngularJS directive to display a context menu
 * when a right-click event is triggered
 *
 * <AUTHOR> (http://ianvonwalter.com)
 */
(function(angular) {
    'use strict';

    angular
        .module('ng-context-menu', [])
        .factory('ContextMenuService', function() {
            return {
                element: null,
                menuElement: null,
                isOpen() {
                    if (this.menuElement) {
                        return this.menuElement[0].classList.contains('open');
                    }
                    return false;
                },
                close() {
                    if (this.menuElement) {
                        this.menuElement.removeClass('open');
                    }
                },
            };
        })
        .directive('contextMenu', [
            '$document',
            'ContextMenuService',
            function($document, ContextMenuService) {
                return {
                    restrict: 'A',
                    scope: {
                        callback: '&contextMenu',
                        disabled: '&contextMenuDisabled',
                        closeCallback: '&contextMenuClose',
                        marginBottom: '@contextMenuMarginBottom',
                    },
                    link: function($scope, $element, $attrs) {
                        let opened = false;

                        function open(event, menuElement) {
                            menuElement.addClass('open');

                            const left = event.offsetX;
                            const top = event.offsetY;

                            menuElement.css('top', top + 'px');
                            menuElement.css('left', left + 'px');
                            opened = true;
                        }

                        function close(menuElement) {
                            menuElement.removeClass('open');

                            if (opened) {
                                $scope.closeCallback();
                            }

                            opened = false;
                        }

                        $element.bind('contextmenu', function(event) {
                            if (!$scope.disabled()) {
                                if (ContextMenuService.menuElement !== null) {
                                    close(ContextMenuService.menuElement);
                                }
                                ContextMenuService.menuElement = angular.element(
                                    document.getElementById($attrs.target),
                                );
                                ContextMenuService.element = event.target;

                                event.preventDefault();
                                event.stopPropagation();
                                $scope.$apply(function() {
                                    $scope.callback({ $event: event });
                                });
                                $scope.$apply(function() {
                                    open(event, ContextMenuService.menuElement);
                                });
                            }
                        });

                        function handleKeyUpEvent(event) {
                            if (
                                !$scope.disabled() &&
                                opened &&
                                event.keyCode === 27
                            ) {
                                $scope.$apply(function() {
                                    close(ContextMenuService.menuElement);
                                });
                            }
                        }

                        function handleClickEvent(event) {
                            if (
                                !$scope.disabled() &&
                                opened &&
                                (
                                    event.button !== 2 ||
                                    event.target !== ContextMenuService.element ||
                                    getDataTarget(event.target) != getDataTarget(ContextMenuService.element)
                                )
                            ) {
                                $scope.$apply(function() {
                                    close(ContextMenuService.menuElement);
                                });
                            }
                        }

                        function getDataTarget(element) {
                            return element && element.getAttribute('data-target');
                        }

                        $document.bind('keyup', handleKeyUpEvent);
                        // Firefox treats a right-click as a click and a contextmenu event
                        // while other browsers just treat it as a contextmenu event
                        $document.bind('click', handleClickEvent);
                        $document.bind('contextmenu', handleClickEvent);

                        $scope.$on('$destroy', function() {
                            $document.unbind('keyup', handleKeyUpEvent);
                            $document.unbind('click', handleClickEvent);
                            $document.unbind('contextmenu', handleClickEvent);
                        });
                    },
                };
            },
        ]);
})(angular);
