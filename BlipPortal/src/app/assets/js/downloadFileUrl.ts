export default class DownloadFileUrl {
    static async dowloadFile(nameFile: string, uri: string) {
        const file = await fetch(uri);
        const url = await file.blob();
        const report = document.createElement('a');
        report.setAttribute('href', window.URL.createObjectURL(url));
        report.setAttribute('download', nameFile);
        document.body.appendChild(report);
        report.click();
        report.remove();
    }
}
