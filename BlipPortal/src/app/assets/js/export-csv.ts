export default class CSVParser {
    public static separator: any;

    constructor() {}

    static ArrayToCSV({
        data,
        hasHeader = true,
        columnDelimiter = ',',
        lineDelimiter = '\n',
    }) {
        if (!data) {
            return undefined;
        }
        const keys = Object.keys(data[0]);
        let csv = hasHeader ? keys.join().concat(lineDelimiter) : '';
        const csvBody = data.reduce((csvLines, obj) => {
            const newLine = keys
                .reduce(
                    (line, key, index) =>
                        index < keys.length - 1
                            ? line.concat(obj[key], columnDelimiter)
                            : line.concat(obj[key]),
                    '',
                )
                .concat(lineDelimiter);
            return csvLines.concat(newLine);
        }, '');
        csv = csv.concat(csvBody);
        return `sep=${columnDelimiter}`.concat(lineDelimiter).concat(csv);
    }

    static CSVAsBlob({
        data,
        hasHeader = true,
        filename = 'export.csv',
        columnDelimiter = ',',
        lineDelimiter = '\n',
    }) {
        const csv = CSVParser.ArrayToCSV({
            data,
            hasHeader,
            lineDelimiter,
            columnDelimiter,
        });

        const file = CSVParser.CSVStringToBlob(csv);

        return { file, filename };
    }

    static CSVStringToBlob(csv: string) {
        return new Blob([new Uint8Array([0xef, 0xbb, 0xbf]), csv], {
            type: 'data:text/csv;charset=utf-8,',
        });
    }

    static CSVToArray({ data, emptyFields = true, lineDelimiter = ',' }) {
        if (!data) {
            return undefined;
        }
        const lines = data.split(/\r\n|\n/) as Array<string>;
        if (lines.length === 0) {
            return undefined;
        }

        const keys = lines.shift().split(lineDelimiter);
        const array = lines.reduce((arr, line) => {
            const element = line
                .split(lineDelimiter)
                .reduce((obj, item, pos) => {
                    const newItem =
                        !emptyFields && item.length === 0
                            ? undefined
                            : { [keys[pos]]: item };
                    return newItem ? { ...obj, ...newItem } : undefined;
                }, {});
            return arr.concat(element);
        }, []);
        return array.findIndex((l) => !l) === -1 ? array : undefined;
    }
}
