import { IRootScopeService } from 'angular';
export interface IDetailController {
    application: any;
    query: () => Promise<any>;
}

export interface IComponent {
    bindings: any;
    controller: any;
    templateUrl?: string;
    template?: string;
}

export interface IBlipRootScopeService extends IRootScopeService {
    application: any;
    hasInstability: boolean;
}

export interface IBlipScope extends IRootScopeService {
    isLoadingGlobal: () => boolean;
    isLoadingLocal: () => boolean;
}

declare global {
    interface Array<T> {
        removeItem(value): T[];
        getProp(...args): any;
    }

    interface Object {
        getProp(...args): any;
    }

    interface Window {
        _hsq: Array<object> | undefined;
    }
}
