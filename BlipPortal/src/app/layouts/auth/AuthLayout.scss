@import '~assets/scss/main';

#wrapper {
    #onboarding-cta {
        bottom: 1.5rem;
        right: 1.5rem;
        border: 0;
        border-radius: 0.75rem;
        background-color: $color-surface-4;
        box-shadow: 0px 10px 16px 0px rgba(0, 0, 0, 0.12);
        padding: 0.7rem 1rem;
        gap: 1rem;
        transition: all ease-in-out 0.2s;
        height: auto;
        bds-typo {
            color: $color-surface-1;
        }

        &:hover {
            box-shadow: 0px 10px 16px 0px rgba(0, 0, 0, 0.36);
        }

        .onboarding-percentage {
			gap: 0.25rem;

            .single-chart {
                width: 1rem;
                height: 1rem;
				margin-top: -2px;

                .circle-bg {
                    fill: none;
                    stroke: $color-surface-3;
                    stroke-width: 6;
                }

                .circle {
                    fill: none;
                    stroke-width: 6;
                    stroke: $color-primary;
                    stroke-linecap: round;
                    animation: progress 1s ease-out forwards;
                }

                @keyframes progress {
                    0% {
                        stroke-dasharray: 0 100;
                    }
                }
            }
        }
    }
}
