import { IBlipScope } from 'interfaces';
import { IToggleable } from 'feature-toggle-client';

import { CookieModalController } from 'modules/application/cookieModal/CookieModalController';
import { OnboardingController } from 'modules/onboarding/OnboardingController';

import { AccountService2 } from 'modules/account/AccountService2';
import { TenantService } from 'modules/application/tenant/TenantService';
import { ContextProcessorService } from 'modules/core/ContextProcessorService';
import { SidebarService } from 'modules/shared/SidebarService';
import { SegmentService } from 'modules/application/misc/SegmentService';
import { OnboardingService } from 'modules/onboarding/OnboardingService';

import CookieModalView from 'modules/application/cookieModal/CookieModalView.html';
import OnboardingSidebarView from 'modules/onboarding/sidebar/OnboardingSidebarView.html';

import './AuthLayout.scss';
import { CHECKLIST_BUTTON_VIEW } from './AuthLayoutSegmentEvent';

export class AuthLayoutController implements IToggleable {
    isOrganizationEnabled: any;
    canViewOnboardingSidebar: boolean = false;
    onboardingOverallProgress: number = 0;
    isModalEnabled: boolean;

    constructor(
        $scope: IBlipScope,
        LoadingService,
        private AccountService2: AccountService2,
        private ContextProcessorService: ContextProcessorService,
        private SidebarService: SidebarService,
        private ModalService,
        private TenantService: TenantService,
        private SegmentService: SegmentService,
        private OnboardingService: OnboardingService,
        private $window: Window,
    ) {
        $scope.isLoadingGlobal = LoadingService.isLoadingGlobal;
        $scope.isLoadingLocal = LoadingService.isLoadingLocal;
        this.onInit();
    }

    async onInit(): Promise<void> {
        await this.ContextProcessorService.waitForInitialization();
        await this.checkFeatures();
        await this.showCookieModal();
        this.setCanViewOnboardingSidebar();

        // Rerender button if sidebar is updated (progress or visibility)
        this.$window.addEventListener('onOnboardingSidebarUpdate', (_) => {
            this.setCanViewOnboardingSidebar();
        });
    }

    async checkFeatures(): Promise<any> {
        this.isOrganizationEnabled = await this.TenantService.isOrganizationEnabled();
    }

    async showCookieModal(): Promise<void> {
        const account = await this.AccountService2.me();

        const cookies = account.extras?.cookies &&
            (JSON.parse(account.extras.cookies));

        if (!cookies?.portalAcceptedCookiesDate) {
            await this.ModalService.showModal({
                template: CookieModalView,
                controller: CookieModalController,
                controllerAs: '$ctrl'
            });
        }
    }

    async setCanViewOnboardingSidebar(): Promise<void> {
        this.canViewOnboardingSidebar = await this.OnboardingService.shouldShowOnboardingSidebar();

        if (this.canViewOnboardingSidebar) {
            this.createTrackOnboardingButtonView();
            this.onboardingOverallProgress = await this.OnboardingService.getOnboardingSidebarProgress();
        }
    }

    async createTrackOnboardingButtonView(): Promise<void> {
        const isTracked = sessionStorage.getItem(CHECKLIST_BUTTON_VIEW);

        if (isTracked !== 'true') {
            sessionStorage.setItem(CHECKLIST_BUTTON_VIEW, 'true');
            this.SegmentService.createOrganizationTrack(CHECKLIST_BUTTON_VIEW);
        }
    }

    openOnboardingSidebar(): void {
        this.SidebarService.showSidebar({
            template: OnboardingSidebarView,
            controller: OnboardingController,
            margin: false,
            position: 'right',
            width: 580,
            id: 'checklist',
        });
    }
}
