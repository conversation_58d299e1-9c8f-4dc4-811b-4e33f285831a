@import "~assets/scss/main";

.terms-service-wrapper {
    background-color: #F2F5F6;
    top: 0;

    bds-illustration {
        height: 60px;
    }

    .terms-service-container {
        @include mobile {
            width: 95%;
        }
    }

    span.heading {
        @include mobile {
            font-size: 24px;
        }
    }

    span.lastModified {
        @include mobile {
            font-size: 10px;
        }
    }

    span.subheading {
        @include mobile {
            font-size: 14px;
        }
    }

    .terms-service-list {
        @include mobile {
            padding: 40px;
        }

        ol > li {
            font-weight: bold;
        }

        ul li {
            list-style: inside;
            font-size: 16px;
        }
    }
}
