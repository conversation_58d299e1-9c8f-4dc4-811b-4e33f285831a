#!/bin/bash

# This is script is supposed to run with npm at src directory
# npm run start-submodules:ps1
# If you want to run this script in the terminal, you can uncomment the line below
# cd ../../src

projectRoot=$(pwd)

# Get the list of submodules
submodules=$(git submodule status --recursive | awk '{print $2}')

# Loop through each submodule
for submodule in $submodules; do
  echo "Updating submodule: $submodule"

  # Enter the submodule directory
  cd "$submodule"

  echo "Checkout the master branch"
  git checkout master

  # Fetch and pull the latest changes from the remote master branch
  
  echo "git fetch origin for $submodule"
  git fetch origin

  echo "git pull origin master for $submodule"
  git pull origin master

  # Return to the parent directory
  cd "$projectRoot"

  # Output the submodule name and status
  echo "Updated submodule: $submodule"
done