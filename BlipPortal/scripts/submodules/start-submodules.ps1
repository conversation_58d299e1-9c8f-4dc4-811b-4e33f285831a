# This is script is supposed to run with npm at src directory
# npm run start-submodules:ps1
# If you want to run this script in the terminal, you can uncomment the line below
# Change directory to the 'src' directory
# Set-Location -Path ../../src

# if ssh parameter is set, use ssh to clone the submodule
param (
    [string[]]$args
)

Write-Output "args $args"

foreach ($arg in $args) {
    if ($arg -eq "--ssh") {
        Write-Output "setting the submodule to use ssh..."
        git config submodule."src/app/modules/application/detail/portal-submodule-builder".url *********************:v3/curupira/BLiP/portal-submodule-builder
    }

    if ($arg -eq "--https") {
        Write-Output "setting the submodule to use https..."
        git config submodule."src/app/modules/application/detail/portal-submodule-builder".url https://<EMAIL>/curupira/BLiP/_git/portal-submodule-builder
    }
}

# Initialize the git submodules
git submodule init

# Update the git submodules
git submodule update

# ignore any change in the submodule, such as the change in the submodule's branch
git config submodule."src/app/modules/application/detail/portal-submodule-builder".ignore all
