# This is script is supposed to run with npm at src directory
# npm run start-submodules:ps1
# If you want to run this script in the terminal, you can uncomment the line below
# Set-Location ../../src

$projectRoot = Get-Location

# Get the list of submodules
$submodules = git submodule status --recursive | ForEach-Object { $_.Split(' ')[2] }

# Loop through each submodule
foreach ($submodule in $submodules) {
  Write-Host "Updating submodule: $submodule"

  # # Enter the submodule directory
  Set-Location -Path $submodule

  Write-Host "Checkout the master branch"
  git checkout master

  # # Fetch and pull the latest changes from the remote master branch
  Write-Host "git fetch origin for $submodule"
  git fetch origin

  Write-Host "git pull origin master for $submodule"
  git pull origin master

  # # Return to the parent directory
  Set-Location -Path $projectRoot

  # # Output the submodule name and status
  Write-Host "Updated submodule: $submodule"
}