{
    "presets": [
        ["@babel/preset-env", { "targets": { "esmodules": true } }],
        ["@babel/preset-react", { "runtime": "automatic" }],
        "@babel/preset-typescript",
        // "@babel/preset-flow"
    ],
    "plugins": [
        "@babel/plugin-syntax-async-generators",
        "@babel/plugin-syntax-dynamic-import",
        "@babel/plugin-proposal-class-properties",
        "@babel/plugin-transform-regenerator",
        "@babel/plugin-proposal-object-rest-spread",
        "@babel/plugin-transform-exponentiation-operator",
        "@babel/plugin-transform-runtime"
    ]
}
