import { act } from 'react';
import { render, screen } from '@testing-library/react';
import TenantsListReact from '../../src/app/modules/navbar/tenantsList/TenantsListReact';
import { Tenant } from 'app/modules/application/tenant/TenantService';
import { SegmentServiceMock, TenantServiceMock } from '../utils/MockUtils';

describe('TenantsListReact', () => {
  test('renders a list of tenants', async () => {
    const mockProps = {
      tenants: [
        { id: 1, name: 'Tenant 1' },
        { id: 2, name: 'Tenant 2' },
        { id: 3, name: 'Tenant 3' },
      ] as unknown as Tenant[],
      expanded: true,
      TenantService: TenantServiceMock,
      SegmentService: SegmentServiceMock,
    }

    const tenants = [
      { id: 1, name: 'Tenant 1' },
      { id: 2, name: 'Tenant 2' },
      { id: 3, name: 'Tenant 3' },
    ] as unknown as Tenant[];

    await act(() => {
      render(<TenantsListReact {...mockProps} />);
    });

    const tenantItems = screen.getAllByTestId('tenant-item');
    expect(tenantItems).toHaveLength(tenants.length);
  });
});