import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import TenantMenuReact, { TenantMenuReactProps } from '../../src/app/modules/navbar/tenantMenu/TenantMenuReact';
import { act } from 'react';
import { $translateMock, BlipServiceMock, ContextProcessorServiceMock, SegmentServiceMock, TenantServiceMock } from '../utils/MockUtils';
import { applyPolyfills, defineCustomElements } from 'blip-ds/loader';

jest.mock('../../src/app/modules/navbar/tenantMenu/tenantMenu.component', () => ({
  TENANT_PANEL_LINK: 'TENANT_PANEL_LINK',
}));

jest.mock('../../src/app/modules/core/ContextProcessorService', () => ({
  Contexts: {
    Default: 'Default',
  },
}));

jest.mock('../../src/app/modules/navbar/NavbarFeatures', () => ({
  NavbarFeatures: {
    isCreateButtonsOnSharedViewDisabled: () => true,
  },
}));

describe('TenantMenuReact', () => {
  beforeAll(async () => {
    await applyPolyfills();
    defineCustomElements(window);
  });

  const mockProps = {
    user: { name: 'John Doe' },
    $translate: $translateMock,
    SegmentService: SegmentServiceMock,
    TenantService: TenantServiceMock,
    ContextProcessorService: ContextProcessorServiceMock,
    BlipService: BlipServiceMock,
  } as TenantMenuReactProps;

  test('Methods are called to start instance', async () => {
    // spy instantiation methods
    jest.spyOn(mockProps.$translate, 'instant');
    jest.spyOn(mockProps.TenantService, 'getDefaultPortalUrl').mockReturnValue('http://localhost');
    jest.spyOn(mockProps.TenantService, 'getPersonalTenant').mockReturnValue(Promise.resolve({ id: '', name: 'Personal Tenant' }));
    jest.spyOn(mockProps.TenantService, 'mine').mockReturnValue(Promise.resolve({
      items: [
        { id: '1', name: 'Tenant 1' },
        { id: '2', name: 'Tenant 2' },
      ]
    }));
    jest.spyOn(mockProps.TenantService, 'getTenantPlan').mockReturnValue(Promise.resolve('enterprise'));
    jest.spyOn(mockProps.TenantService, 'getTenantUrl').mockReturnValue('http://localhost');
    jest.spyOn(mockProps.ContextProcessorService, 'waitForInitialization').mockReturnValue(Promise.resolve());
    jest.spyOn(mockProps.BlipService, 'waitForInitialization').mockReturnValue(Promise.resolve());
    jest.spyOn(mockProps.SegmentService, 'createOrganizationTrack');
    jest.spyOn(document, 'getElementById').mockReturnValue({open: true} as unknown as HTMLElement)

    await act(() => {
      render(<TenantMenuReact {...mockProps} />);
      expect(screen.getAllByText('')).toBeTruthy();
    });

    // assert the instantiation methods are called
    expect(mockProps.$translate.instant).toHaveBeenCalled();
    expect(mockProps.TenantService.getDefaultPortalUrl).toHaveBeenCalled();
    expect(mockProps.TenantService.getPersonalTenant).toHaveBeenCalled();
    expect(mockProps.TenantService.mine).toHaveBeenCalled();
    expect(mockProps.TenantService.getTenantPlan).toHaveBeenCalled();
    expect(mockProps.TenantService.getTenantUrl).toHaveBeenCalled();
    expect(mockProps.ContextProcessorService.waitForInitialization).toHaveBeenCalled();
    expect(mockProps.BlipService.waitForInitialization).toHaveBeenCalled();

    await waitFor(() => {
      fireEvent.click(screen.getByTestId('menu-contract'));
      expect(mockProps.SegmentService.createOrganizationTrack).toHaveBeenCalled();
      expect(screen.getByText('Tenant 1')).toBeTruthy();
      expect(screen.getByText('Tenant 2')).toBeTruthy();
    });
  });
});