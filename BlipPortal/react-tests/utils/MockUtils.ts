import { SegmentService } from 'app/modules/application/misc/SegmentService';
import { TenantService } from 'app/modules/application/tenant/TenantService';
import { ContextProcessorService } from 'app/modules/core/ContextProcessorService';
import { BlipService } from 'app/modules/messaginghub/BlipService';

export const $translateMock = {
  instant: jest.fn(),
};

export const SegmentServiceMock = { 
  track: jest.fn(),
  createOrganizationTrack: jest.fn(),
  createTrack: jest.fn(),
} as unknown as SegmentService
export const TenantServiceMock = {
  getPersonalTenant: () => ({ id: '', name: 'Personal Tenant' }),
  getTenantUrl: jest.fn(),
  getDefaultPortalUrl: jest.fn(),
  mine: () => ({
    items: [
      { id: '1', name: 'Tenant 1' },
      { id: '2', name: 'Tenant 2' },
    ],
  }),
  getTenantPlan: () => 'enterprise',
} as unknown as TenantService;
export const ContextProcessorServiceMock = {
  process: jest.fn(),
  context: 'context',
  tenant: { name: 'tenant' },
  waitForInitialization: () => () => Promise.resolve(),
} as unknown as ContextProcessorService;

export const BlipServiceMock = { 
  sendMessage: jest.fn(),
  waitForInitialization: () => Promise.resolve(),
} as unknown as BlipService;