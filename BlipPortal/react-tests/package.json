{"name": "react-tests", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"show-babel-config": "babel --showConfig", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "^7.25.9", "@babel/core": "^7.26.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-exponentiation-operator": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-flow": "^7.25.9", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.14", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "babel-jest": "^29.7.0", "blip-ds": "^1.295.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.2.5", "typescript": "^4.9.5"}}