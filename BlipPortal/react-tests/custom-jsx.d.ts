// import React from 'react'
// import 'react-dom';
import '@types/react';
// import '@types/react-dom';

declare module 'react/jsx-runtime' {
    namespace JSX {
        interface IntrinsicElements {
            // item: React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
            'bds-avatar': any;
            'bds-icon': any;
            'bds-typo': any;
            'bds-menu': any;
        }
    }
}
