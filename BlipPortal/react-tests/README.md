# React Tests

The `react-tests` folder is dedicated to testing the React components of the Portal. It is kept separate from the `src` folder to avoid dependency conflicts that could break the portal.

## Running Tests

The tests are set to map the `.tsx` files inside the `src/` structure. To run the tests, navigate to the folder `react-tests` and run the following commands:

Before running the react-tests, ensure that you have run `npm install` in the `src` directory. This is necessary because the React version used in the tests is the one installed in the `src` directory. If the dependencies in the `src` directory are already installed, you can proceed directly to step 3.

1. Navigate to the `src` folder:
    ```bash
    cd src
    ```

2. Install the dependencies:

    ```bash
    npm install
    ```


3. Navigate to the `react-tests` folder:

    ```bash
    cd ../react-tests
    ```

4. Install the dependencies:

    ```bash
    npm install
    ```

5. Run the tests:

    ```bash
    npm test
    ```

## Coverage

Currently, coverage is not working properly due to an issue in Jest that prevents it from collecting coverage for files outside its directory. We are actively looking for a solution to this problem.