{
    "compilerOptions": {
        "target": "es5",
        "module": "commonjs",
        "jsx": "react-jsx",
        "strict": true,
        "esModuleInterop": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "lib": ["dom", "es2015"],
        "types": ["node", "jest"],
        "baseUrl": "../src",
        "paths": {
            "modules/*": [
                "../src/app/modules/*"
            ]
        },
        "allowJs": true,
    },
    "include": ["./**/*", "../src/**/*", "custom-jsx.d.ts"],
    "exclude": ["node_modules", "dist"]
}
