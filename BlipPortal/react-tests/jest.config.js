module.exports = {
  rootDir: './',
  collectCoverage: true,
  collectCoverageFrom: [
    '<rootDir>/../src/**/*.{tsx}'
  ],
  transform: {
    '\\.[jt]sx?$': 'ts-jest',
    // "^.+\\.(t|j)sx?$": "ts-jest",
  },
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.tsx?$',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  setupFilesAfterEnv: ['<rootDir>/setupTests.js'],
  testEnvironment: 'jest-environment-jsdom',
  modulePaths: [
    "<rootDir>",
    "../src/app",
    "../src/app/",
    "../src/app/modules"
  ],
  moduleNameMapper: {
    '\\.(s?css|less)$': '<rootDir>/styleMock.js',
    '^react$': '<rootDir>/../src/node_modules/react',
    '^react-dom$': '<rootDir>/../src/node_modules/react-dom',
  },
};